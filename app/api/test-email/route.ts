import { NextRequest, NextResponse } from 'next/server';
import { sendMailgunEmail } from '@/lib/services/mailgunService';

export async function POST(request: NextRequest) {
  try {
    const { to, subject, message } = await request.json();

    if (!to) {
      return NextResponse.json(
        { error: 'Recipient email is required' },
        { status: 400 }
      );
    }

    console.log('🧪 API Test Email Request:');
    console.log(`- To: ${to}`);
    console.log(`- Subject: ${subject || 'Test Email'}`);
    console.log(`- Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`- FORCE_REAL_EMAILS: ${process.env.FORCE_REAL_EMAILS}`);

    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">✅ API Email Test</h1>
        </div>
        <div style="padding: 20px;">
          <p>Hello!</p>
          <p>${message || 'This is a test email sent via the API endpoint.'}</p>
          <p><strong>Configuration:</strong></p>
          <ul>
            <li>Domain: ${process.env.NEXT_PUBLIC_MAILGUN_DOMAIN}</li>
            <li>From: ${process.env.NEXT_PUBLIC_MAILGUN_FROM}</li>
            <li>Environment: ${process.env.NODE_ENV || 'development'}</li>
            <li>Force Real Emails: ${process.env.FORCE_REAL_EMAILS}</li>
          </ul>
          <p>Sent at: ${new Date().toISOString()}</p>
          <p>If you received this email, your API email configuration is working correctly!</p>
        </div>
        <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p>Powered by <a href="https://onpointly.com" style="color: #4f46e5; text-decoration: none;">Onpointly</a></p>
        </div>
      </div>
    `;

    const result = await sendMailgunEmail(
      to,
      subject || 'API Test Email from Onpointly',
      emailHtml,
      process.env.NEXT_PUBLIC_MAILGUN_FROM || 'Onpointly <<EMAIL>>'
    );

    if (result.success) {
      console.log('✅ API test email sent successfully:', result.id);
      return NextResponse.json({
        success: true,
        message: 'Email sent successfully',
        messageId: result.id
      });
    } else {
      console.error('❌ API test email failed:', result.details);
      return NextResponse.json(
        { 
          error: 'Failed to send email',
          details: result.details 
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('❌ API test email error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Email test API endpoint',
    usage: 'POST with { "to": "<EMAIL>", "subject": "Test", "message": "Hello" }',
    environment: {
      NODE_ENV: process.env.NODE_ENV || 'development',
      FORCE_REAL_EMAILS: process.env.FORCE_REAL_EMAILS,
      MAILGUN_DOMAIN: process.env.NEXT_PUBLIC_MAILGUN_DOMAIN,
      MAILGUN_FROM: process.env.NEXT_PUBLIC_MAILGUN_FROM,
      hasApiKey: !!process.env.NEXT_PUBLIC_MAILGUN_API_KEY
    }
  });
}
