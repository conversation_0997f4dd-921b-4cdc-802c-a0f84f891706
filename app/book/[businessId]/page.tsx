// src/app/book/[businessId]/page.tsx
'use client'; // Required for state, calendar interaction, booking form

import React, { useState, useMemo } from 'react';
import { useParams } from 'next/navigation'; // Hook to get dynamic param
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Timestamp } from 'firebase/firestore';
import { format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz'; // Important for timezone handling


import { getBusiness } from '@/lib/services/businessService';
import { getServiceDefinitions } from '@/lib/services/serviceDefinitionService';
import { getAppointmentsForDay } from '@/lib/services/appointmentService';
import { ServiceDefinition } from '@/lib/types/db';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ServiceSelector } from '@/components/features/ServiceSelector';
import { CalendarView } from '@/components/features/CalendarView';
import { BookingForm } from '@/components/features/BookingForm';
import { Loader2, AlertCircle } from 'lucide-react';
import { generateTimeSlots } from '@/lib/utils/slotGenerator'; // We need to create this utility

// Define the Slot type
interface TimeSlot {
    time: Date; // UTC Date object representing the start of the slot
    formattedTime: string; // Time formatted for display in business timezone
}

export default function PublicBookingPage() {
  const params = useParams();
  const businessId = params.businessId as string; // Get ID from URL

  const [selectedServiceId, setSelectedServiceId] = useState<string | undefined>();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined); // JS Date object (represents a day, time part ignored for selection)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<Date | undefined>(undefined); // UTC Date object for the chosen slot

  // Fetch business details
  const { data: business, isLoading: isLoadingBusiness, error: errorBusiness } = useQuery({
    queryKey: ['publicBusiness', businessId],
    queryFn: () => getBusiness(businessId),
    enabled: !!businessId,
    staleTime: 1000 * 60 * 15, // Business details change less often (15 mins)
  });

  // Fetch services for this business
  const { data: services, isLoading: isLoadingServices, error: errorServices } = useQuery({
    queryKey: ['publicServices', businessId],
    queryFn: () => getServiceDefinitions(businessId),
    enabled: !!businessId,
    staleTime: 1000 * 60 * 5, // Services might change more often (5 mins)
  });

  // Fetch appointments *only* when a date is selected
  const { data: existingAppointments, isLoading: isLoadingAppointments } = useQuery({
      queryKey: ['publicAppointments', businessId, selectedDate ? format(selectedDate, 'yyyy-MM-dd') : ''],
      queryFn: () => getAppointmentsForDay(businessId, selectedDate!), // Pass the selected date
      enabled: !!businessId && !!selectedDate, // Only run when businessId and date are selected
      staleTime: 1000 * 30, // Appointment data is volatile (30 seconds)
  });


  // Find the selected service object
  const selectedService = useMemo(() => {
    return services?.find(s => s.id === selectedServiceId);
  }, [services, selectedServiceId]);


  // --- Generate Available Time Slots ---
   const availableSlots = useMemo<TimeSlot[]>(() => {
       if (!selectedDate || !selectedService || !business?.businessHours || isLoadingAppointments || !business?.timeZone) {
           return [];
       }
       // Ensure appointments are loaded or handle loading state appropriately if needed
       if (existingAppointments === undefined) return [];

       // Convert existing appointment Timestamps back to UTC Date objects for comparison
       const existingAppointmentsDates = existingAppointments.map(appt => ({
           ...appt,
           startTime: (appt.startTime as Timestamp).toDate(),
           endTime: (appt.endTime as Timestamp).toDate(),
       }));

       // **TODO: Create `generateTimeSlots` utility function in `src/lib/utils/slotGenerator.ts`**
       // This function will take: selectedDate, serviceDuration, businessHours, existingAppointmentsDates, timezone
       // And return an array of UTC Date objects representing available start times.
       // return generateTimeSlots(
       //    selectedDate,
       //    selectedService.durationMinutes,
       //    business.businessHours,
       //    existingAppointmentsDates,
       //    business.timezone
       // );

       // Placeholder logic - replace with call to generateTimeSlots
       // Example: Generate dummy slots for now
        const slots: TimeSlot[] = [];
        const zonedDayStart = toZonedTime(selectedDate, business.timeZone);
        for (let hour = 9; hour < 17; hour++) {
             const slotTimeLocal = new Date(zonedDayStart.getFullYear(), zonedDayStart.getMonth(), zonedDayStart.getDate(), hour, 0);
             const slotTimeUTC = toZonedTime(slotTimeLocal, 'UTC');
             // VERY BASIC conflict check placeholder
             const conflict = existingAppointmentsDates.some(appt => appt.startTime.getTime() === slotTimeUTC.getTime());
             if (!conflict) {
                 slots.push({
                     time: slotTimeUTC,
                     // Format time for display using the business's timezone
                    formattedTime: format(slotTimeUTC, 'h:mm a') // format does not take timeZone option directly
                 });
             }
        }
        return slots; // Replace with actual generator call

   }, [selectedDate, selectedService, business, existingAppointments, isLoadingAppointments]);


  // --- Event Handlers ---
  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    setSelectedTimeSlot(undefined); // Reset selected time when date changes
  };

   const handleServiceSelect = (serviceId: string | undefined) => {
     setSelectedServiceId(serviceId);
     setSelectedTimeSlot(undefined); // Reset selected time when service changes
     // Optional: reset selected date too? Depends on desired UX.
     // setSelectedDate(undefined);
   };

   const handleTimeSelect = (timeSlot: Date) => {
        setSelectedTimeSlot(timeSlot);
   }

   const handleBookingSuccess = () => {
        setSelectedTimeSlot(undefined); // Go back to time selection view
        // Optionally clear selected date/service too, or show a success message component
        setSelectedDate(undefined);
        setSelectedServiceId(undefined);
        // Re-fetch appointments for the day to ensure the slot disappears
         queryClient.invalidateQueries({ queryKey: ['publicAppointments', businessId, selectedDate ? format(selectedDate, 'yyyy-MM-dd') : ''] });
   }

   // --- Render Logic ---
   const queryClient = useQueryClient(); // Get query client for invalidation

   const isLoading = isLoadingBusiness || isLoadingServices; // Initial loading state
   const queryError = errorBusiness || errorServices;

   if (isLoading) return <div className="flex min-h-screen items-center justify-center"><Loader2 className="h-12 w-12 animate-spin text-primary" /></div>;

   if (queryError) return (
       <div className="flex min-h-screen items-center justify-center p-4">
          <Card className="max-w-md text-center">
             <CardHeader><CardTitle className="text-destructive">Error</CardTitle></CardHeader>
             <CardContent className="space-y-4">
                 <AlertCircle className="mx-auto h-12 w-12 text-destructive" />
                 <p>Could not load booking information.</p>
                 <p className="text-sm text-muted-foreground">{(queryError as Error).message}</p>
                 <Button onClick={() => window.location.reload()}>Try Again</Button>
             </CardContent>
          </Card>
       </div>
   );

    if (!business) return (
         <div className="flex min-h-screen items-center justify-center p-4">
             <Card className="max-w-md text-center">
                <CardHeader><CardTitle>Business Not Found</CardTitle></CardHeader>
                <CardContent>
                    <p>The requested business could not be found.</p>
                </CardContent>
             </Card>
         </div>
    );

  return (
    <main className="container mx-auto px-4 py-8 md:py-12">
        {/* Display Business Name/Logo */}
        <div className="text-center mb-8">
            {/* Add Logo Later */}
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight">{business.name}</h1>
            {/* Add address/contact info if needed */}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">

            {/* Step 1 & 2: Service and Date Selection (Left/Top) */}
            <div className="md:col-span-1 space-y-6">
                 <Card>
                    <CardHeader><CardTitle className="text-lg">1. Select Service</CardTitle></CardHeader>
                    <CardContent>
                        <ServiceSelector
                            services={services || []}
                            selectedServiceId={selectedServiceId}
                            onServiceSelect={handleServiceSelect}
                            isLoading={isLoadingServices}
                        />
                    </CardContent>
                 </Card>

                {selectedServiceId && (
                     <Card>
                        <CardHeader><CardTitle className="text-lg">2. Select Date</CardTitle></CardHeader>
                        <CardContent>
                            <CalendarView
                                selectedDate={selectedDate}
                                onDateSelect={handleDateSelect}
                                // Pass disabled days or modifiers if needed later
                             />
                        </CardContent>
                    </Card>
                )}
            </div>

             {/* Step 3: Time Slot Selection / Booking Form (Right/Bottom) */}
            <div className="md:col-span-2">
                {!selectedServiceId && (
                     <Card className="flex items-center justify-center min-h-[200px]">
                         <CardContent><p className="text-muted-foreground">Please select a service first.</p></CardContent>
                     </Card>
                )}
                 {selectedServiceId && !selectedDate && (
                     <Card className="flex items-center justify-center min-h-[200px]">
                         <CardContent><p className="text-muted-foreground">Please select a date to view available times.</p></CardContent>
                     </Card>
                )}

                 {/* Show Time Slots */}
                {selectedDate && selectedService && !selectedTimeSlot && (
                    <Card>
                        <CardHeader>
                           <CardTitle className="text-lg">3. Select Time for {format(selectedDate, 'MMM d, yyyy')}</CardTitle>
                        </CardHeader>
                        <CardContent>
                           {isLoadingAppointments && <div className="flex justify-center p-4"><Loader2 className="h-6 w-6 animate-spin text-muted-foreground" /></div>}
                            {!isLoadingAppointments && availableSlots.length === 0 && (
                                <p className="text-center text-muted-foreground py-4">No available slots for this date and service. Please try another date.</p>
                            )}
                            {!isLoadingAppointments && availableSlots.length > 0 && (
                               <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                                    {availableSlots.map((slot) => (
                                        <Button
                                            key={slot.time.toISOString()}
                                            variant="outline"
                                            onClick={() => handleTimeSelect(slot.time)}
                                        >
                                            {slot.formattedTime}
                                        </Button>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                )}

                {/* Show Booking Form */}
                 {selectedDate && selectedService && selectedTimeSlot && (
                    <BookingForm
                        businessId={businessId}
                        businessTimezone={business.timeZone}
                        service={selectedService}
                        selectedTime={selectedTimeSlot}
                        onBookingSuccess={handleBookingSuccess}
                     />
                 )}
            </div>
        </div>
    </main>
  );
}
