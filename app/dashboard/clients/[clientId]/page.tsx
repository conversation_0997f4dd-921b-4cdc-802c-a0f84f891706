'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/components/providers/AuthProvider';
import { getBusinessId } from '@/lib/utils/businessUtils';
import {
  getClientById,
  getClientCategories,
  getClientNotes,
  updateClient,
  deleteClient
} from '@/lib/services/clientService';
import { getClientAppointments } from '@/lib/services/appointmentService';
import { getStaff } from '@/lib/services/staffService';
import { getServices } from '@/lib/services/serviceService';
import { Client, ClientCategory, ClientNote, Appointment, StaffMember, ServiceDefinition } from '@/lib/types/db';
import { ClientDetails } from '@/components/features/ClientDetails';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

export default function ClientDetailsPage({ params }: { params: { clientId: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const businessId = getBusinessId(user?.uid);
  const { clientId } = params;

  // State for dialogs
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Form state
  const [editClientData, setEditClientData] = useState<Partial<Client>>({});
  const [newTag, setNewTag] = useState('');

  // Fetch client data
  const {
    data: client,
    isLoading: isLoadingClient,
    error: clientError,
    refetch: refetchClient
  } = useQuery({
    queryKey: ['client', clientId],
    queryFn: () => getClientById(clientId),
    enabled: !!clientId && !!businessId,
  });

  React.useEffect(() => {
    if (client) {
      setEditClientData({
        name: client.name,
        email: client.email,
        phone: client.phone || '',
        address: client.address || '',
        notes: client.notes || '',
        categoryIds: client.categoryIds || [],
        tags: client.tags || [],
        communicationPreferences: client.communicationPreferences || {
          email: true,
          sms: false,
          push: false
        }
      });
    }
  }, [client]);

  // Fetch client categories
  const {
    data: categories,
    isLoading: isLoadingCategories
  } = useQuery({
    queryKey: ['clientCategories', businessId],
    queryFn: () => getClientCategories(businessId),
    enabled: !!businessId
  });

  // Fetch client notes
  const {
    data: notes,
    isLoading: isLoadingNotes,
    refetch: refetchNotes
  } = useQuery({
    queryKey: ['clientNotes', clientId],
    queryFn: () => getClientNotes(clientId),
    enabled: !!clientId
  });

  // Fetch client appointments
  const {
    data: appointments,
    isLoading: isLoadingAppointments
  } = useQuery({
    queryKey: ['clientAppointments', clientId, businessId],
    queryFn: () => getClientAppointments(businessId, clientId),
    enabled: !!clientId && !!businessId
  });

  // Fetch staff
  const {
    data: staff,
    isLoading: isLoadingStaff
  } = useQuery({
    queryKey: ['staff', businessId],
    queryFn: () => getStaff(businessId),
    enabled: !!businessId
  });

  // Fetch services
  const {
    data: services,
    isLoading: isLoadingServices
  } = useQuery({
    queryKey: ['services', businessId],
    queryFn: () => getServices(businessId),
    enabled: !!businessId
  });

  // Update client mutation
  const updateClientMutation = useMutation({
    mutationFn: (data: { clientId: string; clientData: Partial<Client> }) => {
      return updateClient(data.clientId, data.clientData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['client', clientId] });
      queryClient.invalidateQueries({ queryKey: ['clients', businessId] });
      setIsEditDialogOpen(false);
      toast.success('Client updated successfully');
    },
    onError: (error: Error) => {
      toast.error(`Error updating client: ${error.message}`);
    }
  });

  // Delete client mutation
  const deleteClientMutation = useMutation({
    mutationFn: (clientId: string) => {
      return deleteClient(clientId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clients', businessId] });
      router.push('/dashboard/clients');
      toast.success('Client deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Error deleting client: ${error.message}`);
    }
  });

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditClientData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle category toggle
  const handleCategoryToggle = (categoryId: string, checked: boolean) => {
    setEditClientData(prev => {
      const currentCategories = prev.categoryIds || [];
      if (checked) {
        return {
          ...prev,
          categoryIds: [...currentCategories, categoryId]
        };
      } else {
        return {
          ...prev,
          categoryIds: currentCategories.filter(id => id !== categoryId)
        };
      }
    });
  };

  // Handle communication preference toggle
  const handleCommunicationToggle = (type: 'email' | 'sms' | 'push', checked: boolean) => {
    setEditClientData(prev => ({
      ...prev,
      communicationPreferences: {
        ...(prev.communicationPreferences || { email: true, sms: false, push: false }),
        [type]: checked
      }
    }));
  };

  // Handle adding a tag
  const handleAddTag = () => {
    if (!newTag.trim()) return;

    setEditClientData(prev => ({
      ...prev,
      tags: [...(prev.tags || []), newTag.trim()]
    }));

    setNewTag('');
  };

  // Handle removing a tag
  const handleRemoveTag = (tag: string) => {
    setEditClientData(prev => ({
      ...prev,
      tags: (prev.tags || []).filter(t => t !== tag)
    }));
  };

  // Handle client form submission
  const handleSubmitEdit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!editClientData.name?.trim() || !editClientData.email?.trim()) {
      toast.error('Name and email are required');
      return;
    }

    // Prepare client data for update
    // Convert empty strings to undefined to avoid overwriting with empty values
    const clientDataToUpdate = Object.entries(editClientData).reduce((acc, [key, value]) => {
      // Skip empty strings but keep falsy values like false
      if (value === '') {
        return acc;
      }
      return { ...acc, [key]: value };
    }, {});

    // Update client
    updateClientMutation.mutate({
      clientId,
      clientData: clientDataToUpdate
    });
  };

  // Handle client deletion
  const handleDeleteClient = () => {
    deleteClientMutation.mutate(clientId);
  };

  // Refresh data
  const refreshData = () => {
    refetchClient();
    refetchNotes();
  };

  // Create staff names map
  const staffNames: Record<string, string> = {};
  if (staff) {
    staff.forEach((staffMember: StaffMember) => {
      staffNames[staffMember.id] = staffMember.name;
    });
  }

  // Create service names map
  const serviceNames: Record<string, string> = {};
  if (services) {
    services.forEach((service: ServiceDefinition) => {
      serviceNames[service.id] = service.name;
    });
  }

  const isLoading =
    isLoadingClient ||
    isLoadingCategories ||
    isLoadingNotes ||
    isLoadingAppointments ||
    isLoadingStaff ||
    isLoadingServices;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
          <p className="text-muted-foreground">Loading client data...</p>
        </div>
      </div>
    );
  }

  if (clientError || !client) {
    return (
      <div className="rounded-md bg-destructive/10 p-6 text-destructive flex flex-col items-center">
        <AlertCircle className="h-10 w-10 mb-4" />
        <h2 className="text-lg font-medium mb-2">Client not found</h2>
        <p className="text-center mb-4">
          {clientError ? (clientError as Error).message : "The client you're looking for doesn't exist or you don't have permission to view it."}
        </p>
        <Button onClick={() => router.push('/dashboard/clients')}>
          Back to Clients
        </Button>
      </div>
    );
  }

  return (
    <div>
      <ClientDetails
        client={client}
        categories={categories || []}
        notes={notes || []}
        appointments={appointments || []}
        staffNames={staffNames}
        serviceNames={serviceNames}
        onEditClient={() => setIsEditDialogOpen(true)}
        onDeleteClient={() => setIsDeleteDialogOpen(true)}
        onRefreshData={refreshData}
      />

      {/* Edit Client Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="w-[95vw] max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl rounded-lg p-3 sm:p-6 overflow-y-auto max-h-[90vh]">
          <DialogHeader className="pb-2 sm:pb-4">
            <DialogTitle className="text-lg sm:text-xl">Edit Client</DialogTitle>
            <DialogDescription className="text-xs sm:text-sm">
              Update client information. Changes will be saved when you click Save.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmitEdit} className="space-y-4 mt-2">
            <div className="grid gap-3 sm:gap-4 sm:grid-cols-2">
              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="name" className="text-xs sm:text-sm font-medium">Name *</Label>
                <Input
                  className="h-9 sm:h-10 text-sm"
                  id="name"
                  name="name"
                  placeholder="Client's full name"
                  value={editClientData.name || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="email" className="text-xs sm:text-sm font-medium">Email *</Label>
                <Input
                  className="h-9 sm:h-10 text-sm"
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={editClientData.email || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="phone" className="text-xs sm:text-sm font-medium">Phone (Optional)</Label>
                <Input
                  className="h-9 sm:h-10 text-sm"
                  id="phone"
                  name="phone"
                  placeholder="(*************"
                  value={editClientData.phone || ''}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-1 sm:space-y-2">
                <Label htmlFor="address" className="text-xs sm:text-sm font-medium">Address (Optional)</Label>
                <Input
                  className="h-9 sm:h-10 text-sm"
                  id="address"
                  name="address"
                  placeholder="Client's address"
                  value={editClientData.address || ''}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-1 sm:space-y-2 sm:col-span-2">
                <Label htmlFor="notes" className="text-xs sm:text-sm font-medium">Notes (Optional)</Label>
                <Textarea
                  className="min-h-[80px] sm:min-h-[100px] text-sm"
                  id="notes"
                  name="notes"
                  placeholder="Any additional information about this client..."
                  value={editClientData.notes || ''}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
            </div>

            <div className="space-y-1 sm:space-y-2">
              <Label className="text-xs sm:text-sm font-medium">Categories</Label>
              <div className="grid gap-1 sm:gap-2 sm:grid-cols-2">
                {categories?.map((category) => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={(editClientData.categoryIds || []).includes(category.id)}
                      onCheckedChange={(checked) =>
                        handleCategoryToggle(category.id, !!checked)
                      }
                    />
                    <Label htmlFor={`category-${category.id}`} className="font-normal text-xs sm:text-sm">
                      {category.name}
                    </Label>
                  </div>
                ))}
                {(!categories || categories.length === 0) && (
                  <div className="text-xs sm:text-sm text-muted-foreground">
                    No categories available. Create categories in the settings.
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-1 sm:space-y-2">
              <Label className="text-xs sm:text-sm font-medium">Tags</Label>
              <div className="flex items-center gap-1 sm:gap-2">
                <Input
                  className="h-9 sm:h-10 text-sm"
                  placeholder="Add a tag..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                />
                <Button
                  className="h-9 sm:h-10 px-2 sm:px-4 text-xs sm:text-sm"
                  type="button"
                  variant="outline"
                  onClick={handleAddTag}
                  disabled={!newTag.trim()}
                >
                  Add
                </Button>
              </div>

              {(editClientData.tags || []).length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {(editClientData.tags || []).map((tag) => (
                    <div
                      key={tag}
                      className="flex items-center gap-1 bg-muted text-muted-foreground px-2 py-1 rounded-md text-xs sm:text-sm"
                    >
                      {tag}
                      <button
                        type="button"
                        className="text-muted-foreground hover:text-foreground"
                        onClick={() => handleRemoveTag(tag)}
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-1 sm:space-y-2">
              <Label className="text-xs sm:text-sm font-medium">Communication Preferences</Label>
              <div className="grid gap-1 sm:gap-2 sm:grid-cols-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="comm-email"
                    checked={editClientData.communicationPreferences?.email ?? true}
                    onCheckedChange={(checked) =>
                      handleCommunicationToggle('email', !!checked)
                    }
                  />
                  <Label htmlFor="comm-email" className="font-normal text-xs sm:text-sm">
                    Email
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="comm-sms"
                    checked={editClientData.communicationPreferences?.sms ?? false}
                    onCheckedChange={(checked) =>
                      handleCommunicationToggle('sms', !!checked)
                    }
                  />
                  <Label htmlFor="comm-sms" className="font-normal text-xs sm:text-sm">
                    SMS
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="comm-push"
                    checked={editClientData.communicationPreferences?.push ?? false}
                    onCheckedChange={(checked) =>
                      handleCommunicationToggle('push', !!checked)
                    }
                  />
                  <Label htmlFor="comm-push" className="font-normal text-xs sm:text-sm">
                    Push Notifications
                  </Label>
                </div>
              </div>
            </div>

            <DialogFooter className="mt-6 sm:mt-8 gap-2 flex-col sm:flex-row sm:justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                className="w-full sm:w-auto h-9 sm:h-10"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={updateClientMutation.isPending || !editClientData.name || !editClientData.email}
                className="w-full sm:w-auto h-9 sm:h-10"
              >
                {updateClientMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="w-[95vw] max-w-md rounded-lg p-3 sm:p-6 overflow-y-auto max-h-[90vh]">
          <DialogHeader className="pb-2 sm:pb-4">
            <DialogTitle className="text-lg sm:text-xl">Delete Client</DialogTitle>
            <DialogDescription className="text-xs sm:text-sm">
              Are you sure you want to delete {client.name}? This action cannot be undone and will remove all client data including appointments and notes.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-6 sm:mt-8 gap-2 flex-col sm:flex-row sm:justify-end">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="w-full sm:w-auto h-9 sm:h-10"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteClient}
              disabled={deleteClientMutation.isPending}
              className="w-full sm:w-auto h-9 sm:h-10"
            >
              {deleteClientMutation.isPending && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
