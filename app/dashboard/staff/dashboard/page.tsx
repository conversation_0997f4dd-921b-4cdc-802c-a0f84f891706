'use client';

import React, { useMemo } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { AppointmentList } from '@/components/features/AppointmentList';
import { useQuery } from '@tanstack/react-query';
import { getStaffAppointments } from '@/lib/services/appointmentService';
import { getServices } from '@/lib/services/serviceService';
import { getStaffByEmail } from '@/lib/services/staffService';
import { getBusiness } from '@/lib/services/businessService';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Calendar, Users, Package, Clock, AlertCircle, Loader2, BarChart } from 'lucide-react';
import { StaffMember } from '@/lib/types/db';
import { AppointmentTrendChart } from '@/components/dashboard/AppointmentTrendChart';
import { DailyScheduleChart } from '@/components/dashboard/DailyScheduleChart';
import { StatsCard } from '@/components/dashboard/StatsCard';
import { StaffMetricsChart } from '@/components/dashboard/StaffMetricsChart';
import { isToday, isPast, isFuture, startOfToday, endOfToday, isWithinInterval, subDays } from 'date-fns';

export default function StaffDashboardPage() {
  const { user, businessId: authBusinessId } = useAuth();

  // Fetch staff member by email
  const {
    data: staffMember,
    isLoading: isLoadingStaff,
    error: staffError
  } = useQuery({
    queryKey: ['staffByEmail', user?.email],
    queryFn: () => getStaffByEmail(user?.email || ''),
    enabled: !!user?.email
  });

  // Get business ID from staff member
  const businessId = staffMember?.businessId || authBusinessId;

  // Fetch business data (mainly for timezone)
  const {
    data: business,
    isLoading: isLoadingBusiness
  } = useQuery({
    queryKey: ['business', businessId],
    queryFn: () => getBusiness(businessId || ''),
    enabled: !!businessId
  });

  // Fetch staff appointments
  const {
    data: appointments,
    isLoading: isLoadingAppointments,
    error: appointmentsError
  } = useQuery({
    queryKey: ['staffAppointments', staffMember?.id],
    queryFn: () => getStaffAppointments(businessId || '', staffMember?.id || ''),
    enabled: !!staffMember?.id && !!businessId,
    staleTime: 1000 * 60 // Consider appointments data slightly less stale (e.g., 1 min)
  });

  // Fetch services (needed for AppointmentList to show service names)
  const {
    data: services,
    isLoading: isLoadingServices,
    error: servicesError
  } = useQuery({
    queryKey: ['services', businessId],
    queryFn: () => getServices(businessId || ''),
    enabled: !!businessId
  });

  const isLoading = isLoadingStaff || isLoadingBusiness || isLoadingAppointments || isLoadingServices;
  const queryError = staffError || appointmentsError || servicesError;

  // Calculate appointment statistics
  const appointmentStats = useMemo(() => {
    if (!appointments) return { today: 0, upcoming: 0, completed: 0, cancelled: 0, pastWeek: 0 };

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const oneWeekAgo = subDays(today, 7);

    const todayCount = appointments.filter(appt => {
      const apptDate = appt.startTime.toDate();
      return apptDate >= today && apptDate < tomorrow;
    }).length;

    const upcomingCount = appointments.filter(appt => {
      const apptDate = appt.startTime.toDate();
      return apptDate >= now;
    }).length;

    const completedCount = appointments.filter(appt => appt.status === 'completed').length;
    const cancelledCount = appointments.filter(appt => appt.status === 'cancelled').length;

    const pastWeekCount = appointments.filter(appt => {
      const apptDate = appt.startTime.toDate();
      return apptDate >= oneWeekAgo && apptDate < today;
    }).length;

    return {
      today: todayCount,
      upcoming: upcomingCount,
      completed: completedCount,
      cancelled: cancelledCount,
      pastWeek: pastWeekCount
    };
  }, [appointments]);

  // Get upcoming appointments (for the list)
  const upcomingAppointments = useMemo(() => {
    if (!appointments) return [];
    const now = new Date();
    return appointments.filter(appt => {
      const apptDate = appt.startTime.toDate();
      return apptDate >= now;
    }).sort((a, b) => a.startTime.toDate().getTime() - b.startTime.toDate().getTime());
  }, [appointments]);

  // Get today's appointments (for the schedule)
  const todaysAppointments = useMemo(() => {
    if (!appointments) return [];
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return appointments.filter(appt => {
      const apptDate = appt.startTime.toDate();
      return apptDate >= today && apptDate < tomorrow;
    });
  }, [appointments]);

  // Get assigned services
  const assignedServices = useMemo(() => {
    if (!services || !staffMember?.services) return [];
    return services.filter(service => staffMember.services?.includes(service.id));
  }, [services, staffMember?.services]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!staffMember) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold tracking-tight">Staff Dashboard</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Not a Staff Member</AlertTitle>
          <AlertDescription>
            You are not registered as a staff member. Please contact your administrator.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
        Welcome, {staffMember.name}
      </h1>

      {/* Quick Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <StatsCard
          title="Today's Appointments"
          value={appointmentStats.today}
          description="Scheduled for today"
          icon={Calendar}
          isLoading={isLoading}
        />
        <StatsCard
          title="Upcoming Appointments"
          value={appointmentStats.upcoming}
          description="Future bookings"
          icon={Clock}
          isLoading={isLoading}
        />
        <StatsCard
          title="My Services"
          value={assignedServices.length}
          description="Services you provide"
          icon={Package}
          isLoading={isLoading}
        />
      </div>

      {/* Today's Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Today's Schedule
          </CardTitle>
          <CardDescription>Your appointments for today</CardDescription>
        </CardHeader>
        <CardContent>
          {todaysAppointments.length > 0 ? (
            <div className="space-y-3">
              {todaysAppointments
                .sort((a, b) => a.startTime.toDate().getTime() - b.startTime.toDate().getTime())
                .map((appointment) => {
                  const service = services?.find(s => s.id === appointment.serviceId);
                  const startTime = appointment.startTime.toDate();
                  const endTime = appointment.endTime.toDate();

                  return (
                    <div key={appointment.id} className="p-3 border rounded-md">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">{service?.name || 'Unknown Service'}</div>
                          <div className="text-sm">
                            {startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} -
                            {endTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {appointment.clientName || 'No client name'}
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No appointments scheduled for today.</p>
          )}
        </CardContent>
      </Card>

      {/* My Services */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            My Services
          </CardTitle>
          <CardDescription>Services you are assigned to provide</CardDescription>
        </CardHeader>
        <CardContent>
          {assignedServices.length > 0 ? (
            <div className="space-y-2">
              {assignedServices.map((service) => (
                <div key={service.id} className="p-3 border rounded-md">
                  <div className="font-medium">{service.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {service.durationMinutes} minutes - ${service.price}
                  </div>
                  {service.description && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {service.description}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">You are not assigned to any services yet.</p>
          )}
        </CardContent>
      </Card>

      {/* Appointments Lists */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Upcoming Appointments
            </CardTitle>
            <CardDescription>Your next scheduled appointments</CardDescription>
          </CardHeader>
          <CardContent>
            <AppointmentList
              appointments={appointments || []}
              services={services || []}
              isLoading={isLoading}
              error={queryError as Error | null | undefined}
              businessTimezone={business?.timeZone || 'UTC'}
              limit={5}
              showPastAppointments={false}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Recent Past Appointments
            </CardTitle>
            <CardDescription>Your recently completed appointments</CardDescription>
          </CardHeader>
          <CardContent>
            <AppointmentList
              appointments={appointments || []}
              services={services || []}
              isLoading={isLoading}
              error={queryError as Error | null | undefined}
              businessTimezone={business?.timeZone || 'UTC'}
              limit={5}
              showPastAppointments={true}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
