'use client';

import React, { useState } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Search, UserCheck, Loader2, AlertCircle, Calendar, Mail, Phone } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getStaffByEmail } from '@/lib/services/staffService';
import { getClientAppointments, getStaffAppointments } from '@/lib/services/appointmentService';
import { getClients } from '@/lib/services/clientService';
import { Client } from '@/lib/types/db';

export default function StaffClientsPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch staff member by email
  const {
    data: staffMember,
    isLoading: isLoadingStaff,
    error: staffError
  } = useQuery({
    queryKey: ['staffByEmail', user?.email],
    queryFn: () => getStaffByEmail(user?.email || ''),
    enabled: !!user?.email
  });

  // Fetch all clients for the business
  const {
    data: allClients,
    isLoading: isLoadingClients,
    error: clientsError
  } = useQuery({
    queryKey: ['clients', staffMember?.businessId],
    queryFn: () => getClients(staffMember?.businessId || ''),
    enabled: !!staffMember?.businessId
  });

  // Fetch staff appointments
  const {
    data: staffAppointments,
    isLoading: isLoadingAppointments,
    error: appointmentsError
  } = useQuery({
    queryKey: ['staffAppointments', staffMember?.id],
    queryFn: () => getStaffAppointments(staffMember?.businessId || '', staffMember?.id || ''),
    enabled: !!staffMember?.id && !!staffMember?.businessId
  });

  const isLoading = isLoadingStaff || isLoadingClients || isLoadingAppointments;

  // Filter clients that have appointments with this staff member
  const myClients = React.useMemo(() => {
    if (!allClients || !staffAppointments) return [];

    // Get unique client IDs from staff appointments
    const clientIds = new Set(staffAppointments.map(appt => appt.clientId));

    // Filter clients that match these IDs
    return allClients.filter(client => clientIds.has(client.id));
  }, [allClients, staffAppointments]);

  // Filter clients based on search query
  const filteredClients = React.useMemo(() => {
    if (!myClients) return [];
    if (!searchQuery.trim()) return myClients;

    const query = searchQuery.toLowerCase().trim();
    return myClients.filter(client =>
      client.name?.toLowerCase().includes(query) ||
      client.email?.toLowerCase().includes(query) ||
      client.phone?.includes(query)
    );
  }, [myClients, searchQuery]);

  // Get client appointment count
  const getClientAppointmentCount = (clientId: string) => {
    if (!staffAppointments) return 0;
    return staffAppointments.filter(appt => appt.clientId === clientId).length;
  };

  // Get client's next appointment
  const getClientNextAppointment = (clientId: string) => {
    if (!staffAppointments) return null;

    const now = new Date();
    const futureAppointments = staffAppointments
      .filter(appt => appt.clientId === clientId && appt.startTime.toDate() > now)
      .sort((a, b) => a.startTime.toDate().getTime() - b.startTime.toDate().getTime());

    return futureAppointments[0] || null;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (staffError || clientsError || appointmentsError) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold tracking-tight">My Clients</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {(staffError || clientsError || appointmentsError)?.message || 'An error occurred while loading data.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-1">
        <h1 className="text-2xl font-bold tracking-tight">My Clients</h1>
        <p className="text-muted-foreground">Clients you have provided services to in the past or have upcoming appointments with</p>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search clients..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Client List</CardTitle>
          <CardDescription>
            These are clients you have serviced or have upcoming appointments with ({myClients.length} total)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredClients.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <UserCheck className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No clients found</h3>
              <p className="text-sm text-muted-foreground mt-1">
                {searchQuery ? 'Try a different search term' : 'You have no clients assigned to you yet'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredClients.map((client) => (
                <div key={client.id} className="flex flex-col md:flex-row md:items-center justify-between p-4 rounded-lg border">
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarFallback>
                        {client.name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'C'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium">{client.name}</h3>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-muted-foreground">
                        {client.email && (
                          <div className="flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            <span>{client.email}</span>
                          </div>
                        )}
                        {client.phone && (
                          <div className="flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            <span>{client.phone}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col md:flex-row items-start md:items-center gap-2 mt-4 md:mt-0">
                    <Badge variant="outline" className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {getClientAppointmentCount(client.id)} appointments
                    </Badge>

                    {getClientNextAppointment(client.id) && (
                      <Badge className="flex items-center">
                        Next: {getClientNextAppointment(client.id)?.startTime.toDate().toLocaleDateString()}
                      </Badge>
                    )}

                    <Button variant="outline" size="sm" asChild>
                      <a href={`/dashboard/clients/${client.id}`}>
                        View Details
                      </a>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
