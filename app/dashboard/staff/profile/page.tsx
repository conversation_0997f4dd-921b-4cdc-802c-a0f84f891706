'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Calendar, Check, AlertCircle, Info } from 'lucide-react';
import { toast } from 'sonner';
import { getStaffByEmail, updateStaff } from '@/lib/services/staffService';
import { getServices } from '@/lib/services/serviceService';
import { StaffMember, ServiceDefinition  } from '@/lib/types/db';
import { getGoogleCalendarAuthUrl, getGoogleTokens, saveGoogleTokens } from '@/lib/services/googleAuthService';
import { db } from '@/lib/firebase/config';
import { doc, getDoc, updateDoc, deleteField } from 'firebase/firestore';

// Get the Google Calendar auth URL for staff
const getStaffGoogleCalendarAuthUrl = (staffId: string, businessId: string): string => {
  // Use the standard Google Calendar auth service with staff-specific state information
  return getGoogleCalendarAuthUrl(
    staffId,
    'staff',
    '/dashboard/staff/profile?tab=calendar'
  );
};

export default function StaffProfilePage() {
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const [staffMember, setStaffMember] = useState<StaffMember | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [services, setServices] = useState<ServiceDefinition[]>([]);
  const [isCalendarConnected, setIsCalendarConnected] = useState<boolean | null>(null);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    bio: '',
  });

  useEffect(() => {
    const loadStaffProfile = async () => {
      if (user?.email) {
        try {
          setIsLoading(true);
          const staff = await getStaffByEmail(user.email);

          if (staff) {
            setStaffMember(staff);
            setFormData({
              name: staff.name || '',
              email: staff.email || '',
              phone: staff.phone || '',
              bio: staff.bio || '',
            });

            // Check if Google Calendar is connected using the getGoogleTokens function
            try {
              console.log('Checking Google Calendar connection for staff ID:', staff.id);
              const tokens = await getGoogleTokens(staff.id, 'staff');

              if (tokens && tokens.access_token) {
                console.log('Google Calendar tokens found for staff ID:', staff.id);
                setIsCalendarConnected(true);
              } else {
                console.log('No valid Google Calendar tokens found for staff ID:', staff.id);
                setIsCalendarConnected(false);
              }
            } catch (error) {
              console.error('Error checking Google Calendar connection:', error);
              setIsCalendarConnected(false);
            }
          } else {
            toast(`Error: You are not registered as a staff member.`);
          }
        } catch (error) {
          console.error('Error loading staff profile:', error);
          toast(`Error loading profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadStaffProfile();
  }, [user]);

  // Load services
  useEffect(() => {
    const loadServices = async () => {
      if (staffMember?.businessId) {
        try {
          setIsLoadingServices(true);
          const businessServices = await getServices(staffMember.businessId);
          setServices(businessServices);
        } catch (error) {
          console.error('Error loading services:', error);
          toast.error(`Error loading services: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } finally {
          setIsLoadingServices(false);
        }
      }
    };

    loadServices();
  }, [staffMember?.businessId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!staffMember) {
      toast(`Error: Staff profile not found.`);
      return;
    }

    setIsUpdating(true);

    try {
      await updateStaff(staffMember.id, {
        name: formData.name,
        phone: formData.phone || undefined,
        bio: formData.bio || undefined,
      });

      toast(`Profile updated successfully.`);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast(`Error updating profile: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleConnectCalendar = () => {
    if (!staffMember) {
      toast(`Error: Staff profile not found.`);
      return;
    }

    const authUrl = getStaffGoogleCalendarAuthUrl(staffMember.id, staffMember.businessId);
    window.location.href = authUrl;
  };

  const handleDisconnectCalendar = async () => {
    if (!staffMember) {
      toast(`Error: Staff profile not found.`);
      return;
    }

    setIsDisconnecting(true);

    try {
      // Get the staff document reference
      const staffRef = doc(db, 'staff', staffMember.id);

      // Update the document to remove Google Calendar integration
      await updateDoc(staffRef, {
        'googleCalendar.enabled': false,
        'googleCalendar.tokens': deleteField()
      });

      // Update the UI
      setIsCalendarConnected(false);
      toast.success('Google Calendar disconnected successfully');
    } catch (error) {
      console.error('Error disconnecting Google Calendar:', error);
      toast.error(`Error disconnecting Google Calendar: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsDisconnecting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!staffMember) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold tracking-tight">Staff Profile</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Not a Staff Member</AlertTitle>
          <AlertDescription>
            You are not registered as a staff member. Please contact your administrator.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">My Profile & Settings</h1>
      </div>

      <Tabs defaultValue={searchParams.get('tab') || "profile"} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="calendar">Calendar Integration</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>Update your personal information</CardDescription>
            </CardHeader>
            <form onSubmit={handleUpdateProfile}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    disabled={isUpdating}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    disabled={true} // Email cannot be changed
                    required
                  />
                  <p className="text-xs text-muted-foreground">Email cannot be changed</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange}
                    disabled={isUpdating}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio">Bio</Label>
                  <Input
                    id="bio"
                    name="bio"
                    value={formData.bio}
                    onChange={handleInputChange}
                    disabled={isUpdating}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isUpdating}>
                  {isUpdating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    'Update Profile'
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Google Calendar Integration</CardTitle>
              <CardDescription>Connect your Google Calendar to sync appointments</CardDescription>
            </CardHeader>
            <CardContent>
              {isCalendarConnected === null ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : isCalendarConnected ? (
                <div className="space-y-4">
                  <Alert variant="success" className="bg-green-50 border-green-200">
                    <Check className="h-4 w-4 text-green-600" />
                    <AlertTitle className="text-green-800">Connected</AlertTitle>
                    <AlertDescription className="text-green-700">
                      Your Google Calendar is connected and appointments will be synced automatically.
                    </AlertDescription>
                  </Alert>

                  <div className="text-sm text-muted-foreground">
                    <p>When appointments are assigned to you, they will be automatically added to your Google Calendar.</p>
                    <p className="mt-2">You'll receive calendar invitations and can respond to them directly.</p>
                    <p className="mt-2">Your calendar will be checked for availability when appointments are scheduled.</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <Alert variant="warning" className="bg-amber-50 border-amber-200">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <AlertTitle className="text-amber-800">Not Connected</AlertTitle>
                    <AlertDescription className="text-amber-700">
                      Your Google Calendar is not connected. Connect it to sync appointments automatically.
                    </AlertDescription>
                  </Alert>

                  <div className="text-sm text-muted-foreground">
                    <p>Connecting your Google Calendar allows Onpointly to:</p>
                    <ul className="list-disc pl-5 mt-2 space-y-1">
                      <li>Add your appointments to your calendar</li>
                      <li>Update events when appointments are rescheduled</li>
                      <li>Remove events when appointments are cancelled</li>
                      <li>Check your availability to prevent double-booking</li>
                    </ul>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              {process.env.NODE_ENV === 'development' && (
                <div className="w-full p-2 bg-gray-100 rounded-md">
                  <p className="text-xs text-gray-500 mb-2">Debug Info (Development Only):</p>
                  <p className="text-xs text-gray-500">Connection Status: {isCalendarConnected ? 'Connected' : 'Not Connected'}</p>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs"
                    onClick={async () => {
                      if (staffMember) {
                        try {
                          console.log('Manually checking Google Calendar connection for staff ID:', staffMember.id);
                          const tokens = await getGoogleTokens(staffMember.id, 'staff');

                          if (tokens && tokens.access_token) {
                            console.log('Google Calendar tokens found for staff ID:', staffMember.id);
                            toast.success('Google Calendar is connected');
                            setIsCalendarConnected(true);
                          } else {
                            console.log('No valid Google Calendar tokens found for staff ID:', staffMember.id);
                            toast.info('Google Calendar is not connected');
                            setIsCalendarConnected(false);
                          }
                        } catch (error) {
                          console.error('Error checking Google Calendar connection:', error);
                          toast.error('Error checking Google Calendar connection');
                          setIsCalendarConnected(false);
                        }
                      }
                    }}
                  >
                    Refresh Connection Status
                  </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs"
                      onClick={async () => {
                        if (staffMember) {
                          try {
                            // Get the raw data from Firestore
                            const staffRef = doc(db, 'staff', staffMember.id);
                            const staffDoc = await getDoc(staffRef);

                            if (staffDoc.exists()) {
                              const data = staffDoc.data();
                              console.log('Raw staff data:', data);
                              console.log('Google Calendar data:', data.googleCalendar);
                              toast.info('Raw data logged to console');
                            } else {
                              console.log('Staff document not found');
                              toast.error('Staff document not found');
                            }
                          } catch (error) {
                            console.error('Error fetching raw data:', error);
                            toast.error('Error fetching raw data');
                          }
                        }
                      }}
                    >
                      View Raw Data
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs mt-2"
                      onClick={async () => {
                        if (staffMember) {
                          try {
                            // Create a mock Google Calendar integration for testing
                            const mockTokens = {
                              access_token: 'mock_access_token_' + Date.now(),
                              refresh_token: 'mock_refresh_token_' + Date.now(),
                              expiry_date: Date.now() + 3600 * 1000 // 1 hour from now
                            };

                            await saveGoogleTokens(staffMember.id, mockTokens, 'staff');
                            console.log('Created mock Google Calendar integration');
                            toast.success('Created mock Google Calendar integration');

                            // Refresh the connection status
                            const tokens = await getGoogleTokens(staffMember.id, 'staff');
                            setIsCalendarConnected(!!tokens?.access_token);
                          } catch (error) {
                            console.error('Error creating mock integration:', error);
                            toast.error('Error creating mock integration');
                          }
                        }
                      }}
                    >
                      Create Mock Integration (Testing)
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs mt-2"
                      onClick={() => {
                        if (staffMember) {
                          try {
                            // Get the auth URL and log the state parameter
                            const authUrl = getStaffGoogleCalendarAuthUrl(staffMember.id, staffMember.businessId);
                            const url = new URL(authUrl);
                            const stateParam = url.searchParams.get('state');
                            let state = null;
                            if (stateParam) {
                              state = JSON.parse(decodeURIComponent(stateParam));
                            }
                            console.log('Auth URL:', authUrl);
                            console.log('State parameter:', state);
                            toast.info('Auth URL and state logged to console');
                          } catch (error) {
                            console.error('Error generating auth URL:', error);
                            toast.error('Error generating auth URL');
                          }
                        }
                      }}
                    >
                      Debug Auth URL
                    </Button>
                  </div>
                </div>
              )}

              {isCalendarConnected ? (
                <Button
                  variant="destructive"
                  onClick={handleDisconnectCalendar}
                  disabled={isDisconnecting}
                  className="w-full"
                >
                  {isDisconnecting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Disconnecting...
                    </>
                  ) : (
                    'Disconnect Google Calendar'
                  )}
                </Button>
              ) : (
                <Button onClick={handleConnectCalendar} className="w-full">
                  <Calendar className="mr-2 h-4 w-4" />
                  Connect Google Calendar
                </Button>
              )}
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="services" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>My Services</CardTitle>
              <CardDescription>Services you are assigned to provide</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoadingServices ? (
                  <div className="flex justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : services && services.length > 0 ? (
                  <div className="space-y-4">
                    {services.map((service) => {
                      const isAssigned = staffMember.services?.includes(service.id);
                      return (
                        <div key={service.id} className={`p-4 border rounded-lg ${isAssigned ? 'border-primary bg-primary/5' : ''}`}>
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="font-medium">{service.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {service.durationMinutes} minutes - ${service.price}
                              </p>
                              {service.description && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  {service.description}
                                </p>
                              )}
                            </div>
                            <div className="ml-4">
                              <Badge variant={isAssigned ? 'default' : 'outline'}>
                                {isAssigned ? 'Assigned' : 'Not Assigned'}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                    <Alert className="bg-muted/50">
                      <Info className="h-4 w-4" />
                      <AlertTitle>About Service Assignments</AlertTitle>
                      <AlertDescription>
                        Service assignments are managed by your business administrator. Contact them if you need changes to your service assignments.
                      </AlertDescription>
                    </Alert>
                  </div>
                ) : (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>No Services Available</AlertTitle>
                    <AlertDescription>
                      There are no services defined for your business yet. Please contact your administrator.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
