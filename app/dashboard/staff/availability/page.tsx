'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/components/providers/AuthProvider';
import { Timestamp } from 'firebase/firestore';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { StaffBreakManager } from '@/components/features/StaffBreakManager';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Clock, Loader2, Calendar as CalendarIcon, AlertCircle, Save, Info, X } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getStaffByEmail, updateStaff } from '@/lib/services/staffService';
import { getBusiness } from '@/lib/services/businessService';
import { toast } from 'sonner';

// Days of the week for working hours
const days = [
  { key: 'monday', label: 'Monday', isWeekend: false },
  { key: 'tuesday', label: 'Tuesday', isWeekend: false },
  { key: 'wednesday', label: 'Wednesday', isWeekend: false },
  { key: 'thursday', label: 'Thursday', isWeekend: false },
  { key: 'friday', label: 'Friday', isWeekend: false },
  { key: 'saturday', label: 'Saturday', isWeekend: true },
  { key: 'sunday', label: 'Sunday', isWeekend: true },
];

export default function StaffAvailabilityPage() {
  const { user } = useAuth();
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [workingHours, setWorkingHours] = useState<Record<string, { start: string; end: string } | null>>({
    monday: { start: '09:00', end: '17:00' },
    tuesday: { start: '09:00', end: '17:00' },
    wednesday: { start: '09:00', end: '17:00' },
    thursday: { start: '09:00', end: '17:00' },
    friday: { start: '09:00', end: '17:00' },
    saturday: null,
    sunday: null,
  });

  // Fetch staff member by email
  const {
    data: staffMember,
    isLoading: isLoadingStaff,
    error: staffError,
    refetch: refetchStaff
  } = useQuery({
    queryKey: ['staffByEmail', user?.email],
    queryFn: () => getStaffByEmail(user?.email || ''),
    enabled: !!user?.email
  });

  // Fetch business data (for default hours)
  const {
    data: business,
    isLoading: isLoadingBusiness
  } = useQuery({
    queryKey: ['business', staffMember?.businessId],
    queryFn: () => getBusiness(staffMember?.businessId || ''),
    enabled: !!staffMember?.businessId
  });

  // Initialize working hours and unavailable dates when business or staffMember changes
  useEffect(() => {
    if (business?.businessHours) {
      setWorkingHours(prevHours => ({
        ...prevHours,
        ...business.businessHours
      }));
    }

    if (staffMember?.workingHours) {
      setWorkingHours(prevHours => ({
        ...prevHours,
        ...staffMember.workingHours
      }));
    }

    if (staffMember?.unavailableDates && staffMember.unavailableDates.length > 0) {
      setSelectedDates(staffMember.unavailableDates.map(timestamp => timestamp.toDate()));
    }
  }, [business, staffMember]);

  // Update working hours when staff member data changes
  useEffect(() => {
    if (staffMember?.workingHours) {
      console.log('Loading staff working hours:', staffMember.workingHours);
      setWorkingHours(staffMember.workingHours);
    }

    if (staffMember?.unavailableDates && staffMember.unavailableDates.length > 0) {
      const dates = staffMember.unavailableDates.map(timestamp => timestamp.toDate());
      console.log('Loading staff unavailable dates:', dates);
      setSelectedDates(dates);
    }
  }, [staffMember]);

  const isLoading = isLoadingStaff || isLoadingBusiness;

  const handleDayToggle = (day: string, checked: boolean) => {
    setWorkingHours(prev => ({
      ...prev,
      [day]: checked ? { start: '09:00', end: '17:00' } : null
    }));
  };

  const handleTimeChange = (day: string, field: 'start' | 'end', value: string) => {
    if (!workingHours[day]) return;

    setWorkingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day]!,
        [field]: value
      }
    }));
  };

  const handleSaveAvailability = async () => {
    if (!staffMember) {
      toast.error('Staff profile not found');
      return;
    }

    setIsSaving(true);

    try {
      // Convert selected dates to Firestore timestamps
      const unavailableDates = selectedDates.map(date => {
        return Timestamp.fromDate(new Date(date));
      });

      console.log('Saving working hours:', workingHours);
      console.log('Saving unavailable dates:', unavailableDates);

      // Filter out days with null values and ensure correct type for StaffWorkingHours
      const filteredWorkingHours: Record<string, { start: string; end: string; isWorking: boolean }> = Object.fromEntries(
        Object.entries(workingHours)
          .filter(([_, value]) => value !== null)
          .map(([key, value]) => [
            key,
            {
              ...(value as { start: string; end: string }),
              isWorking: true
            }
          ])
      );

      // Update the staff member with the new working hours and unavailable dates
      await updateStaff(staffMember.id, {
        workingHours: filteredWorkingHours,
        unavailableDates
      });

      toast.success('Availability updated successfully');
      refetchStaff();
    } catch (error) {
      console.error('Error updating availability:', error);
      toast.error(`Failed to update availability: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (staffError) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold tracking-tight">My Availability</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {staffError.message || 'An error occurred while loading your profile.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!staffMember) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold tracking-tight">My Availability</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Not a Staff Member</AlertTitle>
          <AlertDescription>
            You are not registered as a staff member. Please contact your administrator.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold tracking-tight">My Availability</h1>

      <Tabs defaultValue="working-hours">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="working-hours">Working Hours</TabsTrigger>
          <TabsTrigger value="breaks">Breaks</TabsTrigger>
          <TabsTrigger value="time-off">Time Off</TabsTrigger>
        </TabsList>

        <TabsContent value="working-hours" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Working Hours</CardTitle>
              <CardDescription>
                Set your regular working hours for each day of the week
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <Info className="h-4 w-4" />
                <AlertTitle>About Working Hours</AlertTitle>
                <AlertDescription>
                  These hours override the business default hours for you specifically.
                  Toggle off days when you're not available to work.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                {days.map(({ key, label, isWeekend }) => (
                  <div key={key} className={`flex items-center justify-between space-x-4 rounded-lg border p-4 ${isWeekend ? 'bg-muted/30' : ''}`}>
                    <div className="flex items-center space-x-4">
                      <div className="cursor-pointer flex items-center space-x-2">
                        <Switch
                          id={`${key}-toggle`}
                          checked={!!workingHours[key]}
                          onCheckedChange={(checked) => handleDayToggle(key, checked)}
                        />
                        <Label htmlFor={`${key}-toggle`} className="font-medium cursor-pointer">
                          {label}
                        </Label>
                      </div>
                    </div>

                    {workingHours[key] && (
                      <div className="flex items-center space-x-2">
                        <div className="grid gap-1">
                          <Label htmlFor={`${key}-start`} className="text-xs">Start</Label>
                          <Input
                            id={`${key}-start`}
                            type="time"
                            value={workingHours[key]?.start || '09:00'}
                            onChange={(e) => handleTimeChange(key, 'start', e.target.value)}
                            className="w-24"
                          />
                        </div>
                        <div className="grid gap-1">
                          <Label htmlFor={`${key}-end`} className="text-xs">End</Label>
                          <Input
                            id={`${key}-end`}
                            type="time"
                            value={workingHours[key]?.end || '17:00'}
                            onChange={(e) => handleTimeChange(key, 'end', e.target.value)}
                            className="w-24"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveAvailability} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Working Hours
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="breaks" className="mt-4">
          {staffMember && (
            <StaffBreakManager
              staffMember={staffMember}
              onUpdate={refetchStaff}
            />
          )}
        </TabsContent>

        <TabsContent value="time-off" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Time Off</CardTitle>
              <CardDescription>
                Mark dates when you're unavailable for appointments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <Info className="h-4 w-4" />
                <AlertTitle>About Time Off</AlertTitle>
                <AlertDescription>
                  Select dates when you're not available. You won't be assigned appointments on these dates.
                </AlertDescription>
              </Alert>

              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1">
                  <Calendar
                    mode="multiple"
                    selected={selectedDates}
                    onSelect={(dates) => setSelectedDates(dates ?? [])}
                    className="rounded-md border"
                    disabled={{ before: new Date() }}
                  />
                </div>

                <div className="flex-1">
                  <h3 className="text-sm font-medium mb-2">Selected Dates</h3>
                  {selectedDates.length > 0 ? (
                    <div className="space-y-2">
                      {selectedDates.sort((a, b) => a.getTime() - b.getTime()).map((date, index) => (
                        <div key={index} className="flex items-center justify-between p-2 rounded-md bg-muted">
                          <span>{date.toLocaleDateString()}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedDates(selectedDates.filter((d) => d !== date))}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No dates selected</p>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveAvailability} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Time Off
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
