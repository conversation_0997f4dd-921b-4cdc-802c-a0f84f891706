'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import { Timestamp } from 'firebase/firestore';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Clock, Loader2, Calendar as CalendarIcon, AlertCircle, Save, Info, X, ArrowLeft, Lock, ShieldAlert } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getStaffById, updateStaff } from '@/lib/services/staffService';
import { getBusiness } from '@/lib/services/businessService';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { StaffBreakManagerAdmin } from '@/components/features/StaffBreakManagerAdmin';

// Days of the week for working hours
const days = [
  { key: 'monday', label: 'Monday', isWeekend: false },
  { key: 'tuesday', label: 'Tuesday', isWeekend: false },
  { key: 'wednesday', label: 'Wednesday', isWeekend: false },
  { key: 'thursday', label: 'Thursday', isWeekend: false },
  { key: 'friday', label: 'Friday', isWeekend: false },
  { key: 'saturday', label: 'Saturday', isWeekend: true },
  { key: 'sunday', label: 'Sunday', isWeekend: true },
];

export default function StaffAvailabilityManagementPage({ params }: { params: { id: string } }) {
  const { user } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();
  const staffId = params.id;
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [workingHours, setWorkingHours] = useState<Record<string, { start: string; end: string } | null>>({
    monday: { start: '09:00', end: '17:00' },
    tuesday: { start: '09:00', end: '17:00' },
    wednesday: { start: '09:00', end: '17:00' },
    thursday: { start: '09:00', end: '17:00' },
    friday: { start: '09:00', end: '17:00' },
    saturday: null,
    sunday: null,
  });
  const [businessWorkingHours, setBusinessWorkingHours] = useState<Record<string, { start: string; end: string } | null> | null>(null);
  const [enforceBusinessHours, setEnforceBusinessHours] = useState(true);
  const [requireApproval, setRequireApproval] = useState(true);

  // Fetch staff member by ID
  const {
    data: staffMember,
    isLoading: isLoadingStaff,
    error: staffError,
    refetch: refetchStaff
  } = useQuery({
    queryKey: ['staff', staffId],
    queryFn: () => getStaffById(staffId),
    enabled: !!staffId
  });

  // Fetch business data (for default hours)
  const {
    data: business,
    isLoading: isLoadingBusiness
  } = useQuery({
    queryKey: ['business', staffMember?.businessId],
    queryFn: () => getBusiness(staffMember?.businessId || ''),
    enabled: !!staffMember?.businessId,
  });

  // Update staff mutation
  const updateStaffMutation = useMutation({
    mutationFn: async (data: any) => {
      return updateStaff(staffId, data);
    },
    onSuccess: () => {
      toast.success('Staff availability updated successfully');
      queryClient.invalidateQueries({ queryKey: ['staff', staffId] });
    },
    onError: (error: any) => {
      toast.error(`Failed to update staff availability: ${error.message}`);
    }
  });

  // Handle side effects after business or staffMember data is loaded
  useEffect(() => {
    if (business?.businessHours) {
      setBusinessWorkingHours(business.businessHours as unknown as Record<string, { start: string; end: string } | null>);
      
      // Initialize working hours from business hours if available
      if (!staffMember?.workingHours) {
        setWorkingHours(prevHours => ({
          ...prevHours,
          ...business.businessHours
        }));
      }
    }

    // Then override with staff-specific hours if available
    if (staffMember?.workingHours) {
      setWorkingHours(prevHours => ({
        ...prevHours,
        ...staffMember.workingHours
      }));
    }

    // Set unavailable dates if available
    if (staffMember?.unavailableDates && staffMember.unavailableDates.length > 0) {
      setSelectedDates(staffMember.unavailableDates.map(timestamp => timestamp.toDate()));
    }

    // Set enforcement settings if available
    if (staffMember?.availabilitySettings) {
      setEnforceBusinessHours(staffMember.availabilitySettings.enforceBusinessHours ?? true);
      setRequireApproval(staffMember.availabilitySettings.requireApproval ?? true);
    }
  }, [business, staffMember]);

  const isLoading = isLoadingStaff || isLoadingBusiness;

  const handleDayToggle = (day: string, checked: boolean) => {
    setWorkingHours(prev => ({
      ...prev,
      [day]: checked ? { start: '09:00', end: '17:00' } : null
    }));
  };

  const handleTimeChange = (day: string, field: 'start' | 'end', value: string) => {
    if (!workingHours[day]) return;

    setWorkingHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day]!,
        [field]: value
      }
    }));
  };

  const validateWorkingHours = () => {
    if (!enforceBusinessHours || !businessWorkingHours) return true;

    // Check if staff hours are within business hours
    for (const day of days) {
      const dayKey = day.key;
      const staffHours = workingHours[dayKey];
      const businessHours = businessWorkingHours[dayKey];

      // If business is closed on this day, staff should also be closed
      if (!businessHours && staffHours) {
        toast.error(`Staff cannot work on ${day.label} when business is closed`);
        return false;
      }

      // If both are open, check if staff hours are within business hours
      if (staffHours && businessHours) {
        if (staffHours.start < businessHours.start) {
          toast.error(`Staff cannot start before business hours on ${day.label}`);
          return false;
        }
        if (staffHours.end > businessHours.end) {
          toast.error(`Staff cannot end after business hours on ${day.label}`);
          return false;
        }
      }
    }

    return true;
  };

  const handleSaveAvailability = async () => {
    if (!staffMember) {
      toast.error('Staff profile not found');
      return;
    }

    // Validate working hours against business hours
    if (!validateWorkingHours()) {
      return;
    }

    setIsSaving(true);

    try {
      // Convert selected dates to Firestore timestamps
      const unavailableDates = selectedDates.map(date => {
        return Timestamp.fromDate(new Date(date));
      });

      // Filter out days with null values and add isWorking property to match StaffWorkingHours type
      const filteredWorkingHours: Record<string, { start: string; end: string; isWorking: boolean }> = Object.fromEntries(
        Object.entries(workingHours)
          .filter(([_, value]) => value !== null)
          .map(([day, value]) => [
            day,
            { ...value!, isWorking: true }
          ])
      );

      // Update the staff member with the new working hours and unavailable dates
      await updateStaffMutation.mutateAsync({
        workingHours: filteredWorkingHours,
        unavailableDates,
        availabilitySettings: {
          enforceBusinessHours,
          requireApproval
        }
      });

      refetchStaff();
    } catch (error) {
      console.error('Error updating availability:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const resetToBusinessHours = () => {
    if (businessWorkingHours) {
      setWorkingHours(businessWorkingHours);
      toast.success('Reset to business hours');
    } else {
      toast.error('Business hours not available');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (staffError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" onClick={() => router.back()} className="h-9 px-2">
            <ArrowLeft className="h-4 w-4 mr-1" />
            <span className="text-sm">Back</span>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">Staff Availability</h1>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {staffError.message || 'An error occurred while loading the staff profile.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!staffMember) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" onClick={() => router.back()} className="h-9 px-2">
            <ArrowLeft className="h-4 w-4 mr-1" />
            <span className="text-sm">Back</span>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">Staff Availability</h1>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Staff Not Found</AlertTitle>
          <AlertDescription>
            The staff member could not be found.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" onClick={() => router.back()} className="h-9 px-2">
          <ArrowLeft className="h-4 w-4 mr-1" />
          <span className="text-sm">Back</span>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Manage Staff Availability</h1>
      </div>

      <div className="flex flex-col sm:flex-row items-start gap-4">
        <div className="w-full sm:w-auto">
          <Card className="w-full sm:w-64">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Staff Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <h3 className="font-medium">{staffMember.name}</h3>
                  <p className="text-sm text-muted-foreground">{staffMember.email}</p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{staffMember.role || 'Staff'}</Badge>
                  {staffMember.isAdmin && <Badge variant="secondary">Admin</Badge>}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex-1 w-full">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Availability Settings</CardTitle>
              <CardDescription>Control how this staff member's availability is managed</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between space-x-2">
                  <div className="space-y-0.5">
                    <Label htmlFor="enforce-business-hours">Enforce Business Hours</Label>
                    <p className="text-sm text-muted-foreground">
                      Staff cannot set working hours outside of business hours
                    </p>
                  </div>
                  <Switch
                    id="enforce-business-hours"
                    checked={enforceBusinessHours}
                    onCheckedChange={setEnforceBusinessHours}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between space-x-2">
                  <div className="space-y-0.5">
                    <Label htmlFor="require-approval">Require Approval for Changes</Label>
                    <p className="text-sm text-muted-foreground">
                      Staff availability changes require admin approval
                    </p>
                  </div>
                  <Switch
                    id="require-approval"
                    checked={requireApproval}
                    onCheckedChange={setRequireApproval}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs defaultValue="working-hours">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="working-hours">Working Hours</TabsTrigger>
          <TabsTrigger value="breaks">Breaks</TabsTrigger>
          <TabsTrigger value="time-off">Time Off</TabsTrigger>
        </TabsList>

        <TabsContent value="working-hours" className="mt-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div>
                  <CardTitle>Working Hours</CardTitle>
                  <CardDescription>
                    Set regular working hours for this staff member
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" onClick={resetToBusinessHours}>
                  Reset to Business Hours
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <Info className="h-4 w-4" />
                <AlertTitle>About Working Hours</AlertTitle>
                <AlertDescription>
                  These hours override the business default hours for this staff member.
                  Toggle off days when they're not available to work.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                {days.map(({ key, label, isWeekend }) => {
                  const businessClosed = !businessWorkingHours?.[key];
                  const outsideBusinessHours = enforceBusinessHours && businessClosed;
                  
                  return (
                    <div key={key} className={`flex items-center justify-between space-x-4 rounded-lg border p-4 ${isWeekend ? 'bg-muted/30' : ''} ${outsideBusinessHours ? 'opacity-60' : ''}`}>
                      <div className="flex items-center space-x-4">
                        <div className="cursor-pointer flex items-center space-x-2">
                          <Switch
                            id={`${key}-toggle`}
                            checked={!!workingHours[key]}
                            onCheckedChange={(checked) => handleDayToggle(key, checked)}
                            disabled={outsideBusinessHours}
                          />
                          <Label htmlFor={`${key}-toggle`} className="font-medium cursor-pointer">
                            {label}
                          </Label>
                          {outsideBusinessHours && (
                            <Badge variant="outline" className="ml-2 text-xs">
                              <Lock className="h-3 w-3 mr-1" />
                              Business Closed
                            </Badge>
                          )}
                        </div>
                      </div>

                      {workingHours[key] && (
                        <div className="flex items-center space-x-2">
                          <div className="grid gap-1">
                            <Label htmlFor={`${key}-start`} className="text-xs">Start</Label>
                            <Input
                              id={`${key}-start`}
                              type="time"
                              value={workingHours[key]?.start || '09:00'}
                              onChange={(e) => handleTimeChange(key, 'start', e.target.value)}
                              className="w-24"
                              disabled={outsideBusinessHours}
                            />
                          </div>
                          <div className="grid gap-1">
                            <Label htmlFor={`${key}-end`} className="text-xs">End</Label>
                            <Input
                              id={`${key}-end`}
                              type="time"
                              value={workingHours[key]?.end || '17:00'}
                              onChange={(e) => handleTimeChange(key, 'end', e.target.value)}
                              className="w-24"
                              disabled={outsideBusinessHours}
                            />
                          </div>
                          {enforceBusinessHours && businessWorkingHours?.[key] && (
                            <div className="hidden sm:block text-xs text-muted-foreground ml-2">
                              <p>Business hours: {businessWorkingHours[key]?.start} - {businessWorkingHours[key]?.end}</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveAvailability} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Working Hours
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="breaks" className="mt-4">
          {staffMember && (
            <StaffBreakManagerAdmin
              staffMember={staffMember}
              onUpdate={refetchStaff}
            />
          )}
        </TabsContent>

        <TabsContent value="time-off" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Time Off</CardTitle>
              <CardDescription>
                Manage dates when this staff member is unavailable
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4">
                <Info className="h-4 w-4" />
                <AlertTitle>About Time Off</AlertTitle>
                <AlertDescription>
                  Select dates when the staff member is not available. They won't be assigned appointments on these dates.
                </AlertDescription>
              </Alert>

              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1">
                  <Calendar
                    mode="multiple"
                    selected={selectedDates}
                    onSelect={(dates) => setSelectedDates(dates ?? [])}
                    className="rounded-md border"
                    disabled={{ before: new Date() }}
                  />
                </div>

                <div className="flex-1">
                  <h3 className="text-sm font-medium mb-2">Selected Dates</h3>
                  {selectedDates.length > 0 ? (
                    <div className="space-y-2">
                      {selectedDates.sort((a, b) => a.getTime() - b.getTime()).map((date, index) => (
                        <div key={index} className="flex items-center justify-between p-2 rounded-md bg-muted">
                          <span>{date.toLocaleDateString()}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedDates(selectedDates.filter((d) => d !== date))}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No dates selected</p>
                  )}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSaveAvailability} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Time Off
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
