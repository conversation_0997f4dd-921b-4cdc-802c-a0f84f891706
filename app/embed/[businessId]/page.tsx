'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { Timestamp, collection, query, where, getDocs, limit } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

import { getBusiness } from '@/lib/services/businessService';
import { getServices } from '@/lib/services/serviceService';
import { getAppointmentsForDay } from '@/lib/services/appointmentService';
import { getStaff } from '@/lib/services/staffService';
import { ReviewsCarousel } from '@/components/features/ReviewsCarousel';
// Import types and components
import { Appointment } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Loader2, AlertCircle, Check } from 'lucide-react';
import { createAppointment } from '@/lib/services/appointmentService';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

// Define the TimeSlot type
interface TimeSlot {
  time: Date; // UTC Date object representing the start of the slot
  formattedTime: string; // Time formatted for display in business timezone
}

export default function EmbeddableBookingPage() {
  const params = useParams();
  const businessId = params.businessId as string;

  const [selectedServiceId, setSelectedServiceId] = useState<string | undefined>();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<Date | undefined>(undefined);
  const [bookingStep, setBookingStep] = useState<'service' | 'date' | 'time' | 'details' | 'confirmation'>('service');

  // Client details form
  const [clientDetails, setClientDetails] = useState({
    name: '',
    email: '',
    phone: '',
    notes: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bookingConfirmed, setBookingConfirmed] = useState(false);

  // Fetch business details
  const { data: business, isLoading: isLoadingBusiness } = useQuery({
    queryKey: ['embedBusiness', businessId],
    queryFn: () => getBusiness(businessId),
    enabled: !!businessId,
    staleTime: 1000 * 60 * 15, // 15 minutes
  });

  // Fetch services for this business
  const { data: services, isLoading: isLoadingServices } = useQuery({
    queryKey: ['embedServices', businessId],
    queryFn: () => getServices(businessId),
    enabled: !!businessId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Fetch appointments only when a date is selected
  const { data: existingAppointments, isLoading: isLoadingAppointments } = useQuery({
    queryKey: ['embedAppointments', businessId, selectedDate ? format(selectedDate, 'yyyy-MM-dd') : ''],
    queryFn: () => getAppointmentsForDay(businessId, selectedDate!),
    enabled: !!businessId && !!selectedDate,
    staleTime: 1000 * 30, // 30 seconds
  });

  // Fetch staff members for this business
  const { data: staffMembers, isLoading: isLoadingStaff } = useQuery({
    queryKey: ['embedStaff', businessId],
    queryFn: () => getStaff(businessId),
    enabled: !!businessId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Find the selected service object
  const selectedService = useMemo(() => {
    return services?.find(s => s.id === selectedServiceId);
  }, [services, selectedServiceId]);

  // State for available time slots
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [isLoadingSlots, setIsLoadingSlots] = useState(false);

  // Generate available time slots using the unified availability service
  useEffect(() => {
    if (!selectedDate || !selectedService || !business) {
      setAvailableSlots([]);
      return;
    }

    const generateSlots = async () => {
      setIsLoadingSlots(true);
      try {
        console.log('🔍 Generating slots using unified availability service');

        // Import the unified availability service
        const { UnifiedAvailabilityService } = await import('@/lib/services/unifiedAvailabilityService');

        // Get available slots for the selected date and service
        const slots = await UnifiedAvailabilityService.getAvailableSlots(
          businessId,
          selectedDate,
          selectedService.id,
          undefined, // No specific staff selected (will check all available staff)
          15 // 15-minute intervals
        );

        console.log(`✅ Generated ${slots.length} slots, ${slots.filter(s => s.available).length} available`);

        // Convert to the format expected by the UI
        const timeSlots = slots
          .filter(slot => slot.available) // Only show available slots
          .map(slot => ({
            time: slot.start,
            formattedTime: format(slot.start, 'h:mm a')
          }));

        setAvailableSlots(timeSlots);
      } catch (error) {
        console.error('Error generating time slots:', error);
        setAvailableSlots([]);
      } finally {
        setIsLoadingSlots(false);
      }
    };

    generateSlots();
  }, [selectedDate, selectedService, business, businessId]);



  // Handle service selection
  const handleServiceSelect = (serviceId: string) => {
    setSelectedServiceId(serviceId);
    setBookingStep('date');
  };

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    if (date) {
      setBookingStep('time');
    }
  };

  // Handle time slot selection
  const handleTimeSelect = (time: Date) => {
    setSelectedTimeSlot(time);
    setBookingStep('details');
  };

  // Handle client details input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setClientDetails(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle booking submission
  const handleBookingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted');

    if (!selectedService || !selectedTimeSlot || !business) {
      console.log('Missing required data:', { selectedService, selectedTimeSlot, business });
      toast.error('Please complete all booking information');
      return;
    }

    if (!clientDetails.name || !clientDetails.email) {
      console.log('Missing client details:', clientDetails);
      toast.error('Name and email are required');
      return;
    }

    // Set submitting state to true to disable the button
    setIsSubmitting(true);
    console.log('Setting isSubmitting to true');

    try {
      // Calculate end time based on service duration
      const endTime = new Date(selectedTimeSlot);
      endTime.setMinutes(endTime.getMinutes() + (selectedService.durationMinutes || 60));

      // Validate dates before creating appointment
      if (!(selectedTimeSlot instanceof Date) || isNaN(selectedTimeSlot.getTime())) {
        console.error('Invalid start time:', selectedTimeSlot);
        toast.error('Invalid appointment time. Please select a different time.');
        setIsSubmitting(false);
        return;
      }

      if (!(endTime instanceof Date) || isNaN(endTime.getTime())) {
        console.error('Invalid end time:', endTime);
        toast.error('Invalid appointment duration. Please select a different service.');
        setIsSubmitting(false);
        return;
      }

      // Create appointment data with valid dates
      const appointmentData = {
        businessId,
        serviceId: selectedService.id,
        serviceName: selectedService.name,
        startTime: Timestamp.fromDate(selectedTimeSlot),
        endTime: Timestamp.fromDate(endTime),
        clientName: clientDetails.name,
        clientEmail: clientDetails.email,
        clientPhone: clientDetails.phone || '',
        notes: clientDetails.notes || '',
        status: 'scheduled' as 'scheduled',
        price: selectedService.price || 0,
        useRoundRobin: true, // Use round-robin scheduling
        // Allow Google Calendar integration if available, but don't require it
        skipCalendarIntegration: false,
        // Add staff information for email notifications
        staffName: null, // Will be filled by round-robin assignment
        staffEmail: null, // Will be filled by round-robin assignment
      };

      // Log detailed appointment data for debugging
      console.log('Creating appointment with data:', {
        businessId: appointmentData.businessId,
        serviceId: appointmentData.serviceId,
        clientEmail: appointmentData.clientEmail,
        startTime: appointmentData.startTime.toDate().toISOString(),
        endTime: appointmentData.endTime.toDate().toISOString(),
        useRoundRobin: appointmentData.useRoundRobin,
        skipCalendarIntegration: appointmentData.skipCalendarIntegration,
        durationMinutes: appointmentData.durationMinutes
      });

      // Log the dates for debugging
      console.log('Appointment dates:', {
        startTimeJS: selectedTimeSlot,
        startTimeISO: selectedTimeSlot.toISOString(),
        endTimeJS: endTime,
        endTimeISO: endTime.toISOString(),
      });

      // Create appointment with Google Calendar integration for the business account
      // We'll also provide the manual Google Calendar button for clients
      try {
        // First, check if the business has Google Calendar configured
        const { getGoogleCalendarSettings } = await import('@/lib/services/googleCalendarService');
        const settings = await getGoogleCalendarSettings(businessId);

        console.log('Google Calendar settings:', {
          enabled: settings?.enabled,
          hasTokens: !!settings?.tokens,
          businessId
        });

        // Pass true for useRoundRobin to ensure staff assignment
        // Pass the business timezone for proper time calculations
        await createAppointment(
          appointmentData,
          'email_ics' // Default to email_ics for embed appointments
        );
        console.log('Appointment created successfully with round-robin scheduling');
      } catch (error) {
        console.error('Error creating appointment:', error);

        // If there's a Google Calendar error, try again without calendar integration
        if (error instanceof Error && error.message.includes('Google Calendar')) {
          console.log('Retrying without Google Calendar integration');

          // Create a copy with skipCalendarIntegration flag
          const appointmentDataWithoutCalendar = {
            ...appointmentData,
            skipCalendarIntegration: true
          };

          try {
            await createAppointment(
              appointmentDataWithoutCalendar,
              'email_ics' // Default to email_ics for embed appointments
            );
            console.log('Appointment created successfully without Google Calendar integration');
          } catch (secondError) {
            console.error('Error creating appointment without Google Calendar:', secondError);
            throw secondError; // Rethrow to be caught by the outer try/catch
          }
        } else {
          throw error; // Rethrow to be caught by the outer try/catch
        }
      }
      console.log('Appointment created successfully');

      // Note: We don't need to send confirmation emails here anymore
      // The appointment service now handles sending emails automatically
      // This code is kept as a fallback in case the server-side email fails
      try {
        console.log('Checking if server-side email was sent...');

        // Make sure we have the required imports
        if (!collection || !query || !where || !getDocs || !limit || !db) {
          console.error('Missing required Firestore imports');
          console.log('Skipping client-side email check - server should handle it');
          return;
        }

        // Get the appointment details from Firestore
        const appointmentsRef = collection(db, 'appointments');
        const q = query(
          appointmentsRef,
          where('businessId', '==', businessId),
          where('clientEmail', '==', clientDetails.email),
          where('startTime', '==', Timestamp.fromDate(selectedTimeSlot)),
          limit(1)
        );

        console.log('Querying for appointment with:', {
          businessId,
          email: clientDetails.email,
          startTime: selectedTimeSlot.toISOString()
        });

        const appointmentSnapshot = await getDocs(q);
        console.log(`Found ${appointmentSnapshot.size} matching appointments`);

        if (!appointmentSnapshot.empty) {
          const appointmentDoc = appointmentSnapshot.docs[0];
          const appointment = { id: appointmentDoc.id, ...appointmentDoc.data() } as Appointment;

          console.log('Found appointment:', {
            id: appointment.id,
            startTime: appointment.startTime?.toDate?.()?.toISOString() || 'unknown',
            clientEmail: appointment.clientEmail
          });

          // Send confirmation email with ICS file
          const { sendAppointmentConfirmationEmailWithICS } = await import('@/lib/services/emailService');
          await sendAppointmentConfirmationEmailWithICS(
            appointment,
            business?.name || 'Business',
            business?.timeZone || 'UTC',
            business?.address,
            true // Send to all parties
          );
          console.log('Confirmation email with ICS sent successfully');
        } else {
          console.warn('Could not find the created appointment for sending email. Trying direct email approach...');

          // Create a simplified appointment object for email sending
          const simplifiedAppointment = {
            id: 'manual-' + Date.now(),
            businessId,
            clientName: clientDetails.name,
            clientEmail: clientDetails.email,
            clientPhone: clientDetails.phone || '',
            serviceId: selectedService.id,
            serviceName: selectedService.name,
            startTime: Timestamp.fromDate(selectedTimeSlot),
            endTime: Timestamp.fromDate(new Date(selectedTimeSlot.getTime() + (selectedService.durationMinutes || 60) * 60000)),
            status: 'confirmed',
            notes: clientDetails.notes || '',
            createdAt: Timestamp.fromDate(new Date()),
          } as unknown as Appointment;

          // Send confirmation email with the simplified appointment
          const { sendAppointmentConfirmationEmailWithICS } = await import('@/lib/services/emailService');
          await sendAppointmentConfirmationEmailWithICS(
            simplifiedAppointment,
            business?.name || 'Business',
            business?.timeZone || 'UTC',
            business?.address,
            true // Send to all parties
          );
          console.log('Confirmation email with ICS sent successfully using simplified appointment');
        }
      } catch (emailError) {
        console.error('Error sending confirmation email:', emailError);

        // Try one more approach - direct API call
        try {
          console.log('Attempting to send email via direct API call...');

          // Format dates for email
          const startTime = selectedTimeSlot;
          const endTime = new Date(selectedTimeSlot.getTime() + (selectedService.durationMinutes || 60) * 60000);

          const formattedStartTime = startTime.toLocaleString('en-US', {
            timeZone: business?.timeZone || 'UTC',
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
          });

          const formattedEndTime = endTime.toLocaleString('en-US', {
            timeZone: business?.timeZone || 'UTC',
            hour: 'numeric',
            minute: '2-digit',
          });

          // Create ICS file content
          const icsStartTime = startTime.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
          const icsEndTime = endTime.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

          const icsContent = [
            'BEGIN:VCALENDAR',
            'VERSION:2.0',
            'PRODID:-//Onpointly//Appointment Calendar//EN',
            'CALSCALE:GREGORIAN',
            'METHOD:REQUEST',
            'BEGIN:VEVENT',
            `SUMMARY:${selectedService.name} - ${business?.name || 'Appointment'}`,
            `DTSTART:${icsStartTime}`,
            `DTEND:${icsEndTime}`,
            `DESCRIPTION:Your appointment with ${business?.name || 'us'} for ${selectedService.name}.`,
            `LOCATION:${business?.address || ''}`,
            `ORGANIZER;CN=${business?.name || 'Business'}:mailto:<EMAIL>`,
            `ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE;CN=${clientDetails.name}:mailto:${clientDetails.email}`,
            'STATUS:CONFIRMED',
            `UID:appointment-manual-${Date.now()}@onpointly.com`,
            'SEQUENCE:0',
            'BEGIN:VALARM',
            'TRIGGER:-PT1H',
            'ACTION:DISPLAY',
            'DESCRIPTION:Reminder',
            'END:VALARM',
            'END:VEVENT',
            'END:VCALENDAR'
          ].join('\r\n');

          // Create email HTML
          const emailHtml = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
                <h1 style="margin: 0;">Appointment Confirmation</h1>
              </div>
              <div style="padding: 20px;">
                <p>Hello ${clientDetails.name},</p>
                <p>Your appointment has been confirmed with ${business?.name || 'us'}.</p>
                <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="margin-top: 0;">Appointment Details</h3>
                  <p><strong>Date:</strong> ${formattedStartTime}</p>
                  <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
                  <p><strong>Service:</strong> ${selectedService.name}</p>
                </div>

                <div style="background-color: #e6f7ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                  <p style="margin-top: 0;"><strong>📅 Add to Your Calendar</strong></p>
                  <p>We've attached a calendar file (.ics) to this email that you can use to add this appointment to your calendar app.</p>
                  <p style="margin-bottom: 0;">Simply open the attachment and your calendar app will import the appointment details automatically.</p>
                </div>

                <p>If you need to reschedule or cancel your appointment, please contact us as soon as possible.</p>
                <p>Thank you for choosing ${business?.name || 'us'}!</p>
              </div>

              <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
                <p style="margin-bottom: 5px;">This is an automated message, please do not reply to this email.</p>
                <p style="margin-top: 0;">Powered by <a href="https://onpointly.com" style="color: #4f46e5; text-decoration: none;">Onpointly</a></p>
              </div>
            </div>
          `;

          // Send the email via API
          const response = await fetch('/api/email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              to: clientDetails.email,
              subject: `Appointment Confirmation - ${business?.name || 'Appointment'}`,
              html: emailHtml,
              from: `${business?.name || 'Onpointly'} <<EMAIL>>`,
              preferredProvider: 'mailgun',
              attachments: [
                {
                  filename: `appointment-${icsStartTime}.ics`,
                  content: Buffer.from(icsContent).toString('base64'),
                  contentType: 'text/calendar'
                }
              ]
            }),
          });

          if (response.ok) {
            console.log('Confirmation email sent successfully via direct API call');
          } else {
            const errorData = await response.json();
            throw new Error(errorData.error || `API responded with status: ${response.status}`);
          }
        } catch (directApiError) {
          console.error('Error sending email via direct API call:', directApiError);
          // Still don't show an error to the user
        }
      }

      // Show confirmation
      setBookingConfirmed(true);
      setBookingStep('confirmation');
      toast.success('Appointment booked successfully!');
    } catch (error) {
      console.error('Error booking appointment:', error);
      toast.error('Failed to book appointment. Please try again.');
    } finally {
      console.log('Setting isSubmitting to false');
      setIsSubmitting(false);
    }
  };

  // Reset booking form
  const handleReset = () => {
    setSelectedServiceId(undefined);
    setSelectedDate(new Date());
    setSelectedTimeSlot(undefined);
    setClientDetails({
      name: '',
      email: '',
      phone: '',
      notes: ''
    });
    setBookingConfirmed(false);
    setBookingStep('service');
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Apply custom theme if available
  useEffect(() => {
    if (business?.appearance) {
      const root = document.documentElement;

      if (business.appearance.primaryColor) {
        root.style.setProperty('--primary', business.appearance.primaryColor);
      }

      if (business.appearance.fontFamily) {
        root.style.setProperty('--font-sans', business.appearance.fontFamily);
      }

      // Apply custom CSS if available
      if (business.appearance.customCss) {
        const styleElement = document.createElement('style');
        styleElement.textContent = business.appearance.customCss;
        document.head.appendChild(styleElement);

        return () => {
          document.head.removeChild(styleElement);
        };
      }
    }
  }, [business]);

  const isLoading = isLoadingBusiness || isLoadingServices || isLoadingAppointments || isLoadingStaff;

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center p-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!business) {
    return (
      <div className="flex min-h-screen items-center justify-center p-4">
        <Card className="max-w-md text-center">
          <CardHeader>
            <CardTitle>Business Not Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p>The requested business could not be found.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-4 bg-gradient-to-b from-background to-muted/20 min-h-screen">
      {/* Business Header */}
      <div className="text-center mb-8 py-6 border-b">
        {business.appearance?.logo && (
          <img
            src={business.appearance.logo}
            alt={business.name}
            className="h-20 mx-auto mb-3"
          />
        )}
        <h1 className="text-3xl font-bold tracking-tight">{business.name}</h1>
        {business.address && (
          <p className="text-muted-foreground mt-2">{business.address}</p>
        )}
      </div>

      {/* Reviews Section */}
      {bookingStep === 'service' && (
        <div className="mb-8">
          <ReviewsCarousel businessId={businessId} maxReviews={3} />
        </div>
      )}

      {/* Service-specific Reviews */}
      {bookingStep === 'date' && selectedService && (
        <div className="mb-8">
          <ReviewsCarousel
            businessId={businessId}
            serviceId={selectedService.id}
            maxReviews={3}
            showServiceName={true}
          />
        </div>
      )}

      {/* Booking Progress */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <div className="text-sm font-medium">Booking Progress</div>
          <div className="text-sm text-muted-foreground">
            Step {bookingStep === 'service' ? '1' :
                 bookingStep === 'date' ? '2' :
                 bookingStep === 'time' ? '3' :
                 bookingStep === 'details' ? '4' : '5'} of 5
          </div>
        </div>
        <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
          <div
            className="bg-primary h-2 transition-all duration-300 ease-in-out"
            style={{
              width: bookingStep === 'service' ? '20%' :
                    bookingStep === 'date' ? '40%' :
                    bookingStep === 'time' ? '60%' :
                    bookingStep === 'details' ? '80%' : '100%'
            }}
          ></div>
        </div>
      </div>

      {/* Booking Steps */}
      {bookingStep === 'service' && (
        <Card className="shadow-md border-muted/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-xl">Select a Service</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingServices ? (
              <div className="flex justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary/70" />
              </div>
            ) : services && services.length > 0 ? (
              <div className="grid gap-3">
                {services.map(service => (
                  <Button
                    key={service.id}
                    variant="outline"
                    className="flex justify-between items-center h-auto p-0 text-left hover:border-primary/50 hover:bg-primary/5 transition-all group overflow-hidden"
                    onClick={() => handleServiceSelect(service.id)}
                  >
                    <div className="flex-1 p-4">
                      <div className="font-medium text-lg">{service.name}</div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                        <span className="flex items-center">
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1 text-muted-foreground">
                            <path d="M12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM12.5 7H11V13L16.2 16.2L17 14.9L12.5 12.2V7Z" fill="currentColor"/>
                          </svg>
                          {service.durationMinutes} min
                        </span>
                        <span className="w-1 h-1 rounded-full bg-muted-foreground"></span>
                        <span className="font-medium">{formatCurrency(service.price || 0)}</span>
                      </div>
                      {service.description && (
                        <div className="text-sm mt-2 text-muted-foreground line-clamp-2">
                          {service.description}
                        </div>
                      )}
                    </div>
                    <div className="bg-primary/10 h-full flex items-center px-4 text-primary group-hover:bg-primary/20 transition-colors">
                      <svg width="20" height="20" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="group-hover:translate-x-1 transition-transform">
                        <path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                      </svg>
                    </div>
                  </Button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No services available</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {bookingStep === 'date' && selectedService && (
        <Card className="shadow-md border-muted/50">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="text-xl">Select a Date</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-1 text-muted-foreground hover:text-foreground"
                onClick={() => setBookingStep('service')}
              >
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4">
                  <path d="M6.85355 3.14645C7.04882 3.34171 7.04882 3.65829 6.85355 3.85355L3.70711 7H12.5C12.7761 7 13 7.22386 13 7.5C13 7.77614 12.7761 8 12.5 8H3.70711L6.85355 11.1464C7.04882 11.3417 7.04882 11.6583 6.85355 11.8536C6.65829 12.0488 6.34171 12.0488 6.14645 11.8536L2.14645 7.85355C1.95118 7.65829 1.95118 7.34171 2.14645 7.14645L6.14645 3.14645C6.34171 2.95118 6.65829 2.95118 6.85355 3.14645Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                </svg>
                Back
              </Button>
            </div>
            <div className="mt-2 text-muted-foreground text-sm">
              <div className="flex items-center gap-2">
                <div className="font-medium">Selected Service:</div>
                <div>{selectedService.name}</div>
              </div>
              <div className="flex items-center gap-2 mt-1">
                <div className="font-medium">Duration:</div>
                <div>{selectedService.durationMinutes} minutes</div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={(date) => {
                  // Disable dates in the past
                  return date < new Date(new Date().setHours(0, 0, 0, 0));
                }}
                className="rounded-md border shadow-sm p-3"
                classNames={{
                  day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                  day_today: "bg-accent text-accent-foreground"
                }}
              />
              <div className="w-full mt-4 text-center text-sm text-muted-foreground">
                Select a date to see available time slots
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {bookingStep === 'time' && selectedService && selectedDate && (
        <Card className="shadow-md border-muted/50">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="text-xl">Select a Time</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-1 text-muted-foreground hover:text-foreground"
                onClick={() => setBookingStep('date')}
              >
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4">
                  <path d="M6.85355 3.14645C7.04882 3.34171 7.04882 3.65829 6.85355 3.85355L3.70711 7H12.5C12.7761 7 13 7.22386 13 7.5C13 7.77614 12.7761 8 12.5 8H3.70711L6.85355 11.1464C7.04882 11.3417 7.04882 11.6583 6.85355 11.8536C6.65829 12.0488 6.34171 12.0488 6.14645 11.8536L2.14645 7.85355C1.95118 7.65829 1.95118 7.34171 2.14645 7.14645L6.14645 3.14645C6.34171 2.95118 6.65829 2.95118 6.85355 3.14645Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                </svg>
                Back
              </Button>
            </div>
            <div className="mt-2 text-muted-foreground text-sm">
              <div className="flex items-center gap-2">
                <div className="font-medium">Selected Date:</div>
                <div>{format(selectedDate, 'EEEE, MMMM d, yyyy')}</div>
              </div>
              <div className="flex items-center gap-2 mt-1">
                <div className="font-medium">Service:</div>
                <div>{selectedService.name} ({selectedService.durationMinutes} min)</div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoadingAppointments ? (
              <div className="flex justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary/70" />
              </div>
            ) : availableSlots.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                {availableSlots.map((slot) => (
                  <Button
                    key={slot.time.toISOString()}
                    variant="outline"
                    className="py-6 hover:border-primary/50 hover:bg-primary/5 transition-all relative overflow-hidden group"
                    onClick={() => handleTimeSelect(slot.time)}
                  >
                    <span className="relative z-10">{slot.formattedTime}</span>
                    <div className="absolute inset-0 bg-primary/0 group-hover:bg-primary/5 transition-colors"></div>
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-primary scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></div>
                  </Button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 px-4">
                <div className="bg-amber-50 text-amber-800 rounded-lg p-4 inline-flex flex-col items-center">
                  <AlertCircle className="h-8 w-8 mb-2" />
                  <p className="font-medium">No available slots for this date</p>
                  <p className="text-sm mt-1">Please select another date or check back later.</p>
                </div>
                <div className="mt-6">
                  <Button
                    variant="outline"
                    onClick={() => setBookingStep('date')}
                    className="flex items-center gap-2"
                  >
                    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4">
                      <path d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5ZM5.25 9.5C4.97386 9.5 4.75 9.72386 4.75 10C4.75 10.2761 4.97386 10.5 5.25 10.5H9.75C10.0261 10.5 10.25 10.2761 10.25 10C10.25 9.72386 10.0261 9.5 9.75 9.5H5.25Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                    </svg>
                    Choose Another Date
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {bookingStep === 'details' && selectedService && selectedTimeSlot && (
        <Card className="shadow-md border-muted/50">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="text-xl">Your Information</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-1 text-muted-foreground hover:text-foreground"
                onClick={() => setBookingStep('time')}
              >
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4">
                  <path d="M6.85355 3.14645C7.04882 3.34171 7.04882 3.65829 6.85355 3.85355L3.70711 7H12.5C12.7761 7 13 7.22386 13 7.5C13 7.77614 12.7761 8 12.5 8H3.70711L6.85355 11.1464C7.04882 11.3417 7.04882 11.6583 6.85355 11.8536C6.65829 12.0488 6.34171 12.0488 6.14645 11.8536L2.14645 7.85355C1.95118 7.65829 1.95118 7.34171 2.14645 7.14645L6.14645 3.14645C6.34171 2.95118 6.65829 2.95118 6.85355 3.14645Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                </svg>
                Back
              </Button>
            </div>
            <div className="mt-2 text-muted-foreground text-sm">
              <div className="flex items-center gap-2">
                <div className="font-medium">Service:</div>
                <div>{selectedService.name}</div>
              </div>
              <div className="flex items-center gap-2 mt-1">
                <div className="font-medium">Date & Time:</div>
                <div>{format(selectedTimeSlot, 'EEEE, MMMM d, yyyy')} at {format(selectedTimeSlot, 'h:mm a')}</div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleBookingSubmit} className="space-y-5">
              <div className="bg-muted/30 p-4 rounded-lg border border-muted mb-4">
                <h3 className="text-sm font-medium mb-2">Appointment Summary</h3>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <div className="text-muted-foreground">Service</div>
                    <div className="font-medium">{selectedService.name}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Duration</div>
                    <div className="font-medium">{selectedService.durationMinutes} minutes</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Date</div>
                    <div className="font-medium">{format(selectedTimeSlot, 'MMMM d, yyyy')}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Time</div>
                    <div className="font-medium">{format(selectedTimeSlot, 'h:mm a')}</div>
                  </div>
                  {selectedService.price !== undefined && selectedService.price > 0 && (
                    <div className="col-span-2 pt-2 border-t mt-1">
                      <div className="text-muted-foreground">Price</div>
                      <div className="font-medium">{formatCurrency(selectedService.price)}</div>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Contact Information</h3>
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name *</Label>
                    <Input
                      id="name"
                      name="name"
                      value={clientDetails.name}
                      onChange={handleInputChange}
                      required
                      className="border-input/60 focus:border-primary"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={clientDetails.email}
                      onChange={handleInputChange}
                      required
                      className="border-input/60 focus:border-primary"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={clientDetails.phone}
                    onChange={handleInputChange}
                    className="border-input/60 focus:border-primary"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Additional Notes</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    value={clientDetails.notes}
                    onChange={handleInputChange}
                    rows={3}
                    placeholder="Any special requests or information you'd like us to know"
                    className="border-input/60 focus:border-primary"
                  />
                </div>
              </div>

              <div className="pt-4">
                <Button
                  type="submit"
                  className="w-full py-6 text-base"
                  disabled={isSubmitting}
                  onClick={() => console.log('Button clicked, isSubmitting:', isSubmitting)}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Confirm Booking'
                  )}
                </Button>
                <p className="text-xs text-center text-muted-foreground mt-3">
                  By confirming this booking, you agree to our terms of service and cancellation policy.
                </p>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {bookingStep === 'confirmation' && bookingConfirmed && selectedService && selectedTimeSlot && (
        <Card className="shadow-md border-muted/50">
          <CardHeader className="pb-3 text-center">
            <CardTitle className="text-xl">Booking Confirmed!</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-center mb-6">
              <div className="rounded-full bg-green-100 p-4">
                <Check className="h-10 w-10 text-green-600" />
              </div>
            </div>

            <div className="text-center mb-2">
              <p className="text-lg font-medium">Thank you for your booking</p>
              <p className="text-muted-foreground mt-1">A confirmation email has been sent to {clientDetails.email}</p>
            </div>

            <div className="bg-muted/30 p-5 rounded-lg border border-muted">
              <h3 className="text-sm font-medium mb-3 pb-2 border-b">Appointment Details</h3>
              <div className="grid gap-4 text-sm">
                <div className="flex justify-between items-center">
                  <div className="text-muted-foreground">Service</div>
                  <div className="font-medium">{selectedService.name}</div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-muted-foreground">Date</div>
                  <div className="font-medium">{format(selectedTimeSlot, 'EEEE, MMMM d, yyyy')}</div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-muted-foreground">Time</div>
                  <div className="font-medium">{format(selectedTimeSlot, 'h:mm a')}</div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-muted-foreground">Duration</div>
                  <div className="font-medium">{selectedService.durationMinutes} minutes</div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-muted-foreground">Staff</div>
                  <div className="font-medium flex items-center">
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                      Auto-assigned to best available staff
                    </span>
                  </div>
                </div>

                {selectedService.price !== undefined && selectedService.price > 0 && (
                  <div className="flex justify-between items-center pt-2 border-t">
                    <div className="text-muted-foreground">Price</div>
                    <div className="font-medium">{formatCurrency(selectedService.price)}</div>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <div className="flex items-start">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-600 mr-3 mt-0.5">
                  <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20ZM11 7H13V13H11V7ZM11 15H13V17H11V15Z" fill="currentColor"/>
                </svg>
                <div>
                  <h4 className="text-sm font-medium text-blue-800">What happens next?</h4>
                  <p className="text-xs text-blue-700 mt-1">
                    Your appointment has been added to our system and a staff member will be automatically assigned.
                    You'll receive a confirmation email with all the details. The appointment has also been added to our business calendar.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100 mt-4">
              <div className="flex items-start">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-amber-600 mr-3 mt-0.5">
                  <path d="M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z" fill="currentColor"/>
                </svg>
                <div>
                  <h4 className="text-sm font-medium text-amber-800">Add to your personal calendar</h4>
                  <p className="text-xs text-amber-700 mt-1">
                    <strong>Recommended:</strong> While we've added this appointment to our system, we recommend also adding it to your personal calendar. Click the "Add to Google Calendar" button below to ensure you don't miss your appointment.
                  </p>
                </div>
              </div>
            </div>

            <div className="pt-4 space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Button
                  className="w-full py-4 text-xs bg-[#4285F4] hover:bg-[#3367d6] text-white relative overflow-hidden group"
                  onClick={() => {
                    // Create Google Calendar event URL with appointment details
                    const startTime = selectedTimeSlot.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
                    const endTime = new Date(selectedTimeSlot.getTime() + (selectedService.durationMinutes || 60) * 60000)
                      .toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

                    const eventDetails = {
                      text: `${selectedService.name} - ${business?.name || 'Appointment'}`,
                      dates: `${startTime}/${endTime}`,
                      details: `Your appointment with ${business?.name || 'us'} for ${selectedService.name}`,
                      location: business?.address || '',
                    };

                    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(eventDetails.text)}&dates=${eventDetails.dates}&details=${encodeURIComponent(eventDetails.details)}&location=${encodeURIComponent(eventDetails.location)}`;

                    window.open(googleCalendarUrl, '_blank');
                  }}
                >
                  <div className="flex flex-col items-center justify-center w-full">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-1">
                      <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19a2 2 0 0 0 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" fill="white"/>
                    </svg>
                    <span className="text-xs font-medium">Google</span>
                  </div>
                </Button>

                <Button
                  className="w-full py-4 text-xs bg-[#0078D4] hover:bg-[#106EBE] text-white relative overflow-hidden group"
                  onClick={() => {
                    // Create Outlook Calendar event URL with appointment details
                    const startTime = selectedTimeSlot.toISOString();
                    const endTime = new Date(selectedTimeSlot.getTime() + (selectedService.durationMinutes || 60) * 60000).toISOString();

                    const eventDetails = {
                      subject: `${selectedService.name} - ${business?.name || 'Appointment'}`,
                      start: startTime,
                      end: endTime,
                      body: `Your appointment with ${business?.name || 'us'} for ${selectedService.name}`,
                      location: business?.address || '',
                    };

                    const outlookCalendarUrl = `https://outlook.live.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(eventDetails.subject)}&startdt=${encodeURIComponent(eventDetails.start)}&enddt=${encodeURIComponent(eventDetails.end)}&body=${encodeURIComponent(eventDetails.body)}&location=${encodeURIComponent(eventDetails.location)}`;

                    window.open(outlookCalendarUrl, '_blank');
                  }}
                >
                  <div className="flex flex-col items-center justify-center w-full">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-1">
                      <path d="M7 6v3H4V6h3m1-1H3v5h5V5m3 0v5h5V5h-5m4 1v3h-3V6h3M7 13v3H4v-3h3m1-1H3v5h5v-5m3 1v3h3v-3h-3m-1-1h5v5h-5v-5z" fill="white"/>
                    </svg>
                    <span className="text-xs font-medium">Outlook</span>
                  </div>
                </Button>

                <Button
                  className="w-full py-4 text-xs bg-[#6633CC] hover:bg-[#5522AA] text-white relative overflow-hidden group"
                  onClick={() => {
                    // Create Yahoo Calendar event URL with appointment details
                    const startTime = selectedTimeSlot.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
                    const endTime = new Date(selectedTimeSlot.getTime() + (selectedService.durationMinutes || 60) * 60000)
                      .toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

                    const eventDetails = {
                      title: `${selectedService.name} - ${business?.name || 'Appointment'}`,
                      st: startTime,
                      et: endTime,
                      desc: `Your appointment with ${business?.name || 'us'} for ${selectedService.name}`,
                      in_loc: business?.address || '',
                    };

                    const yahooCalendarUrl = `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${encodeURIComponent(eventDetails.title)}&st=${eventDetails.st}&et=${eventDetails.et}&desc=${encodeURIComponent(eventDetails.desc)}&in_loc=${encodeURIComponent(eventDetails.in_loc)}`;

                    window.open(yahooCalendarUrl, '_blank');
                  }}
                >
                  <div className="flex flex-col items-center justify-center w-full">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-1">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="white"/>
                    </svg>
                    <span className="text-xs font-medium">Yahoo</span>
                  </div>
                </Button>

                <Button
                  className="w-full py-4 text-xs bg-[#007AFF] hover:bg-[#0056CC] text-white relative overflow-hidden group"
                  onClick={() => {
                    // Create ICS file for Apple Calendar
                    const startTime = selectedTimeSlot.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
                    const endTime = new Date(selectedTimeSlot.getTime() + (selectedService.durationMinutes || 60) * 60000)
                      .toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

                    const icsContent = [
                      'BEGIN:VCALENDAR',
                      'VERSION:2.0',
                      'PRODID:-//Onpointly//Appointment Calendar//EN',
                      'CALSCALE:GREGORIAN',
                      'METHOD:REQUEST',
                      'BEGIN:VEVENT',
                      `SUMMARY:${selectedService.name} - ${business?.name || 'Appointment'}`,
                      `DTSTART:${startTime}`,
                      `DTEND:${endTime}`,
                      `DESCRIPTION:Your appointment with ${business?.name || 'us'} for ${selectedService.name}`,
                      `LOCATION:${business?.address || ''}`,
                      `ORGANIZER;CN=${business?.name || 'Business'}:mailto:<EMAIL>`,
                      `ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE;CN=${clientDetails.name}:mailto:${clientDetails.email}`,
                      'STATUS:CONFIRMED',
                      `UID:appointment-${Date.now()}@onpointly.com`,
                      'SEQUENCE:0',
                      'BEGIN:VALARM',
                      'TRIGGER:-PT1H',
                      'ACTION:DISPLAY',
                      'DESCRIPTION:Reminder',
                      'END:VALARM',
                      'END:VEVENT',
                      'END:VCALENDAR'
                    ].join('\r\n');

                    const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', `appointment-${startTime}.ics`);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                  }}
                >
                  <div className="flex flex-col items-center justify-center w-full">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-1">
                      <path d="M17.5 3C19.43 3 21 4.57 21 6.5V17.5C21 19.43 19.43 21 17.5 21H6.5C4.57 21 3 19.43 3 17.5V6.5C3 4.57 4.57 3 6.5 3H17.5ZM17.5 5H6.5C5.67 5 5 5.67 5 6.5V17.5C5 18.33 5.67 19 6.5 19H17.5C18.33 19 19 18.33 19 17.5V6.5C19 5.67 18.33 5 17.5 5ZM16 7V9H8V7H16ZM14 11V13H8V11H14ZM12 15V17H8V15H12Z" fill="white"/>
                    </svg>
                    <span className="text-xs font-medium">Apple</span>
                  </div>
                </Button>
              </div>

              <Button
                className="w-full py-4 text-sm bg-[#333333] hover:bg-[#555555] text-white relative overflow-hidden group mt-3"
                onClick={() => {
                  // Create comprehensive ICS file
                  const startTime = selectedTimeSlot.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
                  const endTime = new Date(selectedTimeSlot.getTime() + (selectedService.durationMinutes || 60) * 60000)
                    .toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

                  const icsContent = [
                    'BEGIN:VCALENDAR',
                    'VERSION:2.0',
                    'PRODID:-//Onpointly//Appointment Calendar//EN',
                    'CALSCALE:GREGORIAN',
                    'METHOD:REQUEST',
                    'BEGIN:VEVENT',
                    `SUMMARY:${selectedService.name} - ${business?.name || 'Appointment'}`,
                    `DTSTART:${startTime}`,
                    `DTEND:${endTime}`,
                    `DESCRIPTION:Your appointment with ${business?.name || 'us'} for ${selectedService.name}. Duration: ${selectedService.durationMinutes} minutes.`,
                    `LOCATION:${business?.address || ''}`,
                    `ORGANIZER;CN=${business?.name || 'Business'}:mailto:<EMAIL>`,
                    `ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE;CN=${clientDetails.name}:mailto:${clientDetails.email}`,
                    'STATUS:CONFIRMED',
                    `UID:appointment-${Date.now()}@onpointly.com`,
                    'SEQUENCE:0',
                    'BEGIN:VALARM',
                    'TRIGGER:-PT1H',
                    'ACTION:DISPLAY',
                    'DESCRIPTION:Appointment reminder - 1 hour before',
                    'END:VALARM',
                    'BEGIN:VALARM',
                    'TRIGGER:-PT15M',
                    'ACTION:DISPLAY',
                    'DESCRIPTION:Appointment reminder - 15 minutes before',
                    'END:VALARM',
                    'END:VEVENT',
                    'END:VCALENDAR'
                  ].join('\r\n');

                  const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.setAttribute('download', `${business?.name || 'appointment'}-${format(selectedTimeSlot, 'yyyy-MM-dd-HHmm')}.ics`);
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  URL.revokeObjectURL(url);

                  toast.success('Calendar file downloaded! Open it to add to any calendar app.');
                }}
              >
                <div className="flex items-center justify-center w-full">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z" fill="none" stroke="white" strokeWidth="2"/>
                    <path d="M14 2v6h6" fill="none" stroke="white" strokeWidth="2"/>
                    <path d="M16 13H8" fill="none" stroke="white" strokeWidth="2"/>
                    <path d="M16 17H8" fill="none" stroke="white" strokeWidth="2"/>
                    <path d="M10 9H8" fill="none" stroke="white" strokeWidth="2"/>
                  </svg>
                  <span className="text-sm font-medium">Download .ics File</span>
                </div>
              </Button>

              <Button
                onClick={handleReset}
                variant="outline"
                className="w-full py-6 text-base"
              >
                Book Another Appointment
              </Button>

              <div className="flex justify-center mt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-muted-foreground text-xs"
                  onClick={() => window.print()}
                >
                  <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2">
                    <path d="M12 12.5V9.5H3V12.5H12ZM12 8.5V5H3V8.5H12ZM3 13.5H12C12.5523 13.5 13 13.0523 13 12.5V5C13 4.44772 12.5523 4 12 4H3C2.44772 4 2 4.44772 2 5V12.5C2 13.0523 2.44772 13.5 3 13.5ZM5.5 2.5V1.5H9.5V2.5H5.5ZM5.5 3.5H9.5V4.5H5.5V3.5ZM5.5 0.5H9.5C10.0523 0.5 10.5 0.947715 10.5 1.5V4.5C10.5 5.05228 10.0523 5.5 9.5 5.5H5.5C4.94772 5.5 4.5 5.05228 4.5 4.5V1.5C4.5 0.947715 4.94772 0.5 5.5 0.5Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                  </svg>
                  Print Confirmation
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}