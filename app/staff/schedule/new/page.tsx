'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/components/providers/AuthProvider';
import { getStaffByEmail } from '@/lib/services/staffService';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getServices } from '@/lib/services/serviceService';
import { getStaff } from '@/lib/services/staffService';
import { createAppointment } from '@/lib/services/appointmentService';
import { createRecurringAppointment } from '@/lib/services/recurringAppointmentService';
import { createGroupAppointment } from '@/lib/services/groupAppointmentService';
import { addToWaitlist } from '@/lib/services/waitlistService';
import { applyBufferTimes } from '@/lib/services/bufferTimeService';
import { getBusinessId } from '@/lib/utils/businessUtils';
import { getGoogleTokens } from '@/lib/services/googleAuthService';
import { ServiceDefinition, StaffMember } from '@/lib/types/db';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format } from 'date-fns';
import { CalendarIcon, Loader2, ArrowLeft, Clock, User, Users, Calendar as CalendarIcon2, Repeat, UserPlus, Clock1, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { RecurringAppointmentForm } from '@/components/features/RecurringAppointmentForm';
import { DisabledGroupAppointmentForm } from '@/components/features/DisabledGroupAppointmentForm';
import { DisabledWaitlistForm } from '@/components/features/DisabledWaitlistForm';
import { AllAvailableTimesModal } from '@/components/features/AllAvailableTimesModal';
import { getStaffAvailableSlots } from '@/lib/utils/staffAvailability';
import { getBusiness } from '@/lib/services/businessService';
import { sendAppointmentConfirmationEmailWithICS } from '@/lib/services/emailService';
import { EnhancedAvailabilityService } from '@/lib/services/enhancedAvailabilityService';
import { NotificationPreferences } from '@/components/features/NotificationPreferences';
// Removed CustomDatePicker import

export default function NewAppointmentPage() {
  const router = useRouter();
  const { user, isStaff } = useAuth();
  const [businessId, setBusinessId] = useState<string>(getBusinessId(user?.uid));
  const queryClient = useQueryClient();

  // Debug logging
  console.log('User:', user);
  console.log('Is staff:', isStaff);
  console.log('Initial Business ID:', businessId);

  // State to track current staff member if user is staff
  const [currentStaffMember, setCurrentStaffMember] = useState<any>(null);

  // Form state
  const [appointmentData, setAppointmentData] = useState({
    serviceId: '',
    staffId: '',
    date: new Date(),
    time: '',
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    notes: '',
    clientPreferences: '',
    useRoundRobin: false,

    // Recurring appointment properties
    isRecurring: false,
    recurringPattern: {
      frequency: 'weekly' as 'weekly' | 'daily' | 'monthly',
      interval: 1,
      daysOfWeek: [new Date().getDay()],
      endAfterOccurrences: 10
      // Note: We intentionally don't include endDate here to avoid Firebase errors with undefined values
    },

    // Group appointment properties
    isGroupAppointment: false,
    participants: [{
      name: '',
      email: '',
      phone: undefined,
      notes: undefined
    }],
    maxParticipants: 10,

    // Waitlist properties
    joinWaitlist: false,

    // Buffer time properties
    useBufferTime: false,
    bufferMinutesBefore: 0,
    bufferMinutesAfter: 0,

    // Notification preferences
    notificationMethod: 'email_ics' as 'google_calendar' | 'email_ics',
  });

  // Loading state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingTimeSlots, setIsLoadingTimeSlots] = useState(false);
  const [isCheckingAllTimes, setIsCheckingAllTimes] = useState(false);
  const [availableTimeSlots, setAvailableTimeSlots] = useState<{time: string, formattedTime: string}[]>([]);
  const [businessHours, setBusinessHours] = useState<any>(null);
  const [allAvailableTimes, setAllAvailableTimes] = useState<{staffId: string, staffName: string, slots: {time: string, formattedTime: string}[]}[]>([]);
  const [showAllTimesModal, setShowAllTimesModal] = useState(false);

  // Google Calendar integration
  const [googleCalendarEnabled, setGoogleCalendarEnabled] = useState(false);
  const [googleAccessToken, setGoogleAccessToken] = useState<string | null>(null);
  const [isCheckingGoogleCalendar, setIsCheckingGoogleCalendar] = useState(true);

  // Fetch services
  const {
    data: services,
    isLoading: isLoadingServices,
    error: servicesError
  } = useQuery({
    queryKey: ['services', businessId],
    queryFn: () => getServices(businessId),
    enabled: !!businessId,
  });

  // Fetch staff
  const {
    data: staffMembers,
    isLoading: isLoadingStaff,
    error: staffError
  } = useQuery({
    queryKey: ['staff', businessId],
    queryFn: () => getStaff(businessId),
    enabled: !!businessId,
  });

  // Filter staff based on selected service
  const availableStaff = React.useMemo(() => {
    if (!staffMembers || !appointmentData.serviceId) return [];

    // Debug logging
    console.log('Filtering staff for service:', appointmentData.serviceId);
    staffMembers.forEach(staff => {
      console.log(`Staff ${staff.name} (${staff.id})`, {
        isActive: staff.isActive !== false,
        services: staff.services || [],
        hasService: (staff.services || []).includes(appointmentData.serviceId)
      });
    });

    // Get the selected service to check assignedStaffIds
    const selectedService = services?.find(s => s.id === appointmentData.serviceId);
    console.log('Selected service:', selectedService);

    // If the current user is staff, always include them if they're active
    if (isStaff && currentStaffMember) {
      console.log('Current user is staff:', currentStaffMember.name);
      // Check if the staff member can perform this service
      const canPerformService = (currentStaffMember.services || []).includes(appointmentData.serviceId);

      if (canPerformService) {
        console.log('Staff member can perform this service');
        // Return only the current staff member
        return [currentStaffMember];
      } else {
        console.log('Staff member cannot perform this service');
        // If staff can't perform this service, show an empty list
        return [];
      }
    }

    // For business users, filter by staff.services (correct way)
    const staffByServices = staffMembers.filter(staff =>
      staff.isActive !== false &&
      (staff.services || []).includes(appointmentData.serviceId)
    );

    console.log('Staff filtered by services array:', staffByServices.length);

    // If no staff found, fall back to service.assignedStaffIds (temporary fix)
    if (staffByServices.length === 0 && (selectedService?.assignedStaffIds ?? []).length > 0) {
      console.log('Falling back to assignedStaffIds:', selectedService?.assignedStaffIds);
      return staffMembers.filter(staff =>
        staff.isActive !== false &&
        (selectedService?.assignedStaffIds || []).includes(staff.id)
      );
    }

    return staffByServices;
  }, [staffMembers, appointmentData.serviceId, services, isStaff, currentStaffMember]);

  // Get selected service
  const selectedService = React.useMemo(() => {
    if (!services || !appointmentData.serviceId) return null;
    return services.find(service => service.id === appointmentData.serviceId);
  }, [services, appointmentData.serviceId]);

  // Fetch current staff member if user is staff
  useEffect(() => {
    const fetchCurrentStaffMember = async () => {
      if (isStaff && user?.email) {
        try {
          console.log('Fetching staff member for email:', user.email);
          const staffMember = await getStaffByEmail(user.email);
          console.log('Staff member found:', staffMember);
          setCurrentStaffMember(staffMember);

          // If staff member found, pre-select them in the form
          if (staffMember) {
            console.log('Setting staff ID to:', staffMember.id);
            console.log('Staff member business ID:', staffMember.businessId);

            // Update the business ID to use the staff member's business ID
            if (staffMember.businessId) {
              console.log('Updating business ID to:', staffMember.businessId);
              setBusinessId(staffMember.businessId);
            }

            setAppointmentData(prev => ({
              ...prev,
              staffId: staffMember.id,
              useRoundRobin: false // Staff can't use round-robin for themselves
            }));
          }
        } catch (error) {
          console.error('Error fetching staff member:', error);
        }
      }
    };

    fetchCurrentStaffMember();
  }, [isStaff, user?.email]);

  // Fetch business hours and check Google Calendar
  const [businessTimezone, setBusinessTimezone] = useState<string>('UTC');

  useEffect(() => {
    const fetchBusinessData = async () => {
      if (businessId) {
        try {
          setIsCheckingGoogleCalendar(true);

          // Fetch business data including hours
          const businessData = await getBusiness(businessId);
          if (businessData) {
            if (businessData.businessHours) {
              setBusinessHours(businessData.businessHours);
            }

            if (businessData.timeZone) {
              setBusinessTimezone(businessData.timeZone);
              console.log(`Business timezone: ${businessData.timeZone}`);
            }
          }

          // Check Google Calendar connection - for business pages, use the business ID
          try {
            console.log('Getting Google tokens for business ID:', businessId);
            const tokens = await getGoogleTokens(businessId, 'business');

            if (tokens && tokens.access_token) {
              setGoogleCalendarEnabled(true);
              setGoogleAccessToken(tokens.access_token);
              console.log('Google Calendar is connected for business');
            } else {
              setGoogleCalendarEnabled(false);
              setGoogleAccessToken(null);
              console.log('Google Calendar is not connected for business');
            }
          } catch (calendarError) {
            console.error('Error connecting to Google Calendar:', calendarError);
            setGoogleCalendarEnabled(false);
            setGoogleAccessToken(null);
            // Don't show an error to the user - just disable Google Calendar
          }
        } catch (error) {
          console.error('Error fetching business data:', error);
          setGoogleCalendarEnabled(false);
          setGoogleAccessToken(null);
        } finally {
          setIsCheckingGoogleCalendar(false);
        }
      }
    };

    fetchBusinessData();
  }, [businessId]);

  // Fetch available time slots when staff, date, or service changes
  useEffect(() => {
    const fetchAvailableTimeSlots = async () => {
      // Only fetch if we have all required data
      if (
        !businessId ||
        !appointmentData.date ||
        !appointmentData.staffId ||
        !appointmentData.serviceId ||
        !businessHours ||
        appointmentData.useRoundRobin // Skip for round-robin scheduling
      ) {
        setAvailableTimeSlots([]);
        return;
      }

      try {
        setIsLoadingTimeSlots(true);

        // Get the selected service to determine duration
        const selectedService = services?.find(s => s.id === appointmentData.serviceId);
        if (!selectedService) {
          console.error('Selected service not found');
          return;
        }

        // Get available slots for the selected staff member
        const slots = await getStaffAvailableSlots(
          businessId,
          appointmentData.staffId,
          appointmentData.date,
          selectedService.durationMinutes,
          businessHours,
          businessTimezone // Use the business timezone
        );

        // Format slots for the UI
        const formattedSlots = slots.map(slot => ({
          time: format(slot.time, 'HH:mm'),
          formattedTime: slot.formattedTime
        }));

        setAvailableTimeSlots(formattedSlots);
      } catch (error) {
        console.error('Error fetching available time slots:', error);
        toast.error('Failed to load available time slots');
        setAvailableTimeSlots([]);
      } finally {
        setIsLoadingTimeSlots(false);
      }
    };

    fetchAvailableTimeSlots();
  }, [businessId, appointmentData.date, appointmentData.staffId, appointmentData.serviceId, appointmentData.useRoundRobin, businessHours, services]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!businessId) {
      toast(`Business ID is required`);
      return;
    }

    if (!appointmentData.serviceId) {
      toast(`Please select a service`);
      return;
    }

    if (!appointmentData.useRoundRobin && !appointmentData.staffId) {
      toast(`Please select a staff member or use round-robin scheduling`);
      return;
    }

    if (!appointmentData.date || !appointmentData.time) {
      toast(`Please select a date and time`);
      return;
    }

    // Validate client name and email
    if (!appointmentData.clientName || appointmentData.clientName.trim() === '') {
      toast(`Client name is required`);
      return;
    }

    if (!appointmentData.clientEmail || appointmentData.clientEmail.trim() === '') {
      toast(`Client email is required`);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(appointmentData.clientEmail)) {
      toast(`Please enter a valid email address`);
      return;
    }

    // Validate phone number format if provided
    if (appointmentData.clientPhone && appointmentData.clientPhone.trim() !== '') {
      // Allow various phone formats but ensure it has at least 10 digits
      const phoneDigits = appointmentData.clientPhone.replace(/\D/g, '');
      if (phoneDigits.length < 10) {
        toast(`Please enter a valid phone number with at least 10 digits`);
        return;
      }
    }

    setIsSubmitting(true);

    try {
      // Parse the date and time
      const [hours, minutes] = appointmentData.time.split(':').map(Number);
      const startTime = new Date(appointmentData.date);
      startTime.setHours(hours, minutes, 0, 0);

      // Calculate end time based on service duration
      const durationMinutes = selectedService?.durationMinutes || 60;
      const endTime = new Date(startTime.getTime() + durationMinutes * 60 * 1000);

      // Create the appointment
      const appointmentPayload: any = {
        businessId,
        serviceId: appointmentData.serviceId,
        staffId: appointmentData.useRoundRobin ? undefined : appointmentData.staffId,
        startTime,
        endTime,
        clientName: appointmentData.clientName,
        clientEmail: appointmentData.clientEmail,
        status: 'scheduled',
        useRoundRobin: appointmentData.useRoundRobin, // Explicitly add the useRoundRobin flag
        createdBy: {
          id: user?.uid || null,
          email: user?.email || null,
          name: user?.displayName || null,
          role: isStaff ? 'staff' : 'business',
        },
        paymentStatus: 'unpaid', // Add payment tracking
        revenue: selectedService?.price || 0, // Track potential revenue
      };

      // Add recurring appointment properties if enabled
      if (appointmentData.isRecurring) {
        appointmentPayload.isRecurring = true;
        appointmentPayload.recurringPattern = appointmentData.recurringPattern;
      }

      // Group appointment functionality is temporarily disabled
      // if (appointmentData.isGroupAppointment) {
      //   appointmentPayload.isGroupAppointment = true;
      //   appointmentPayload.maxParticipants = appointmentData.maxParticipants;
      //   appointmentPayload.currentParticipants = appointmentData.participants.length;
      //
      //   // Format participants data
      //   const formattedParticipants = appointmentData.participants.map(participant => ({
      //     name: participant.name.trim(),
      //     email: participant.email.trim(),
      //     phone: participant.phone?.trim() || undefined,
      //     notes: participant.notes?.trim() || undefined
      //   }));
      //
      //   appointmentPayload.participants = formattedParticipants;
      // }

      // Waitlist functionality is temporarily disabled
      // if (appointmentData.joinWaitlist) {
      //   appointmentPayload.hasWaitlist = true;
      //   appointmentPayload.waitlistLimit = selectedService?.waitlistLimit || 10;
      // }

      // Add buffer time properties if enabled
      if (appointmentData.useBufferTime) {
        appointmentPayload.bufferTimeBefore = appointmentData.bufferMinutesBefore;
        appointmentPayload.bufferTimeAfter = appointmentData.bufferMinutesAfter;
      } else if (selectedService?.useBufferTime) {
        // Use service default buffer times if service has them enabled
        appointmentPayload.bufferTimeBefore = selectedService.bufferMinutesBefore;
        appointmentPayload.bufferTimeAfter = selectedService.bufferMinutesAfter;
      }

      // Debug logging
      console.log('Creating appointment with payload:', {
        ...appointmentPayload,
        startTime: appointmentPayload.startTime.toISOString(),
        endTime: appointmentPayload.endTime.toISOString(),
        useRoundRobin: appointmentPayload.useRoundRobin,
        isRecurring: appointmentPayload.isRecurring,
        isGroupAppointment: appointmentPayload.isGroupAppointment,
        hasWaitlist: appointmentPayload.hasWaitlist
      });

      // Format and add optional fields if they have values
      if (appointmentData.clientPhone && appointmentData.clientPhone.trim() !== '') {
        // Format phone number to E.164 format if possible
        const phoneDigits = appointmentData.clientPhone.replace(/\D/g, '');
        if (phoneDigits.length >= 10) {
          // For US numbers, format as +1XXXXXXXXXX
          if (phoneDigits.length === 10) {
            appointmentPayload.clientPhone = `+1${phoneDigits}`;
          } else {
            // For international numbers, just add + prefix
            appointmentPayload.clientPhone = `+${phoneDigits}`;
          }
        } else {
          // If we can't format properly, just use as-is
          appointmentPayload.clientPhone = appointmentData.clientPhone.trim();
        }
      }

      if (appointmentData.notes && appointmentData.notes.trim() !== '') {
        appointmentPayload.notes = appointmentData.notes.trim();
      }

      // Add client preferences if available
      if (appointmentData.clientPreferences && appointmentData.clientPreferences.trim() !== '') {
        appointmentPayload.clientPreferences = appointmentData.clientPreferences.trim();
      }

      // Add service and staff information for Google Calendar
      if (selectedService) {
        appointmentPayload.serviceName = selectedService.name;
      }

      if (appointmentData.staffId && !appointmentData.useRoundRobin) {
        const selectedStaff = staffMembers?.find(s => s.id === appointmentData.staffId);
        if (selectedStaff) {
          appointmentPayload.staffName = selectedStaff.name;
          appointmentPayload.staffEmail = selectedStaff.email;
        }
      }

      // Handle different appointment types
      let appointmentRef;

      try {
        if (appointmentData.isRecurring) {
          // Create recurring appointment
          appointmentRef = await createRecurringAppointment(
            appointmentPayload,
            appointmentPayload.recurringPattern,
            appointmentData.notificationMethod === 'google_calendar' && googleCalendarEnabled && googleAccessToken ? googleAccessToken : undefined,
            businessTimezone
          );
          console.log('Created recurring appointment with ID:', appointmentRef);
        } else {
          // Create regular appointment with notification method
          appointmentRef = await createAppointment(appointmentPayload, appointmentData.notificationMethod);
          console.log('Created appointment with ID:', appointmentRef);
        }
      } catch (appointmentError) {
        console.error('Error creating appointment:', appointmentError);
        throw appointmentError; // Re-throw to be caught by the outer try/catch
      }

      // Invalidate appointments query to trigger a refresh
      queryClient.invalidateQueries({ queryKey: ['appointments'] });

      // Handle client notification based on selected method
      try {
        // Get business data for notifications
        const businessData = await getBusiness(businessId);
        const businessName = businessData?.name || 'Business';
        const businessAddress = businessData?.address;
        const businessTimezone = businessData?.timeZone || 'UTC';

        if (appointmentData.notificationMethod === 'google_calendar' && googleCalendarEnabled) {
          // Google Calendar integration - this should be handled by the appointment service
          console.log('Using Google Calendar integration for client notification');
          toast.success('Appointment created and Google Calendar invitation sent');
        } else {
          // Email with ICS file
          console.log('Using email with ICS file for client notification');

          // Create appointment object for email
          const appointmentForEmail = {
            ...appointmentPayload,
            id: appointmentRef,
            startTime: appointmentPayload.startTime, // Already a Date object
            endTime: appointmentPayload.endTime // Already a Date object
          };

          await sendAppointmentConfirmationEmailWithICS(
            appointmentForEmail,
            businessName,
            businessTimezone,
            businessAddress,
            true // Send to all parties (client, staff, business owner)
          );

          console.log('Appointment confirmation email with ICS sent successfully');
          toast.success('Appointment created and confirmation email with calendar file sent');
        }
      } catch (notificationError) {
        console.error('Error sending client notification:', notificationError);
        // Don't fail the appointment creation if notification fails
        toast.warning('Appointment created but client notification failed to send');
      }

      // Redirect to the staff schedule page
      router.push('/staff/schedule');
    } catch (error) {
      console.error('Error creating appointment:', error);
      toast(`Error creating appointment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle service change
  const handleServiceChange = (serviceId: string) => {
    setAppointmentData(prev => ({
      ...prev,
      serviceId,
      staffId: '', // Reset staff selection when service changes
      time: '', // Reset time selection when service changes
    }));
  };

  // Check if a staff member is available for the selected date
  const [staffAvailability, setStaffAvailability] = useState<Record<string, boolean>>({});

  // Function to check staff availability for the selected date
  const checkStaffAvailability = async () => {
    if (!businessId || !appointmentData.date || !appointmentData.serviceId || !businessHours || !staffMembers) {
      return;
    }

    const selectedService = services?.find(s => s.id === appointmentData.serviceId);
    if (!selectedService) return;

    const availability: Record<string, boolean> = {};

    // Check availability for each staff member
    for (const staff of availableStaff) {
      try {
        const slots = await getStaffAvailableSlots(
          businessId,
          staff.id,
          appointmentData.date,
          selectedService.durationMinutes,
          businessHours,
          businessTimezone
        );

        availability[staff.id] = slots.length > 0;
      } catch (error) {
        console.error(`Error checking availability for staff ${staff.id}:`, error);
        availability[staff.id] = false;
      }
    }

    setStaffAvailability(availability);
  };

  // Check staff availability when date or service changes
  useEffect(() => {
    if (appointmentData.date && appointmentData.serviceId && !appointmentData.useRoundRobin) {
      checkStaffAvailability();
    }
  }, [appointmentData.date, appointmentData.serviceId, appointmentData.useRoundRobin, availableStaff.length]);

  // Function to check all available times for all staff members
  const checkAllAvailableTimes = async () => {
    if (!businessId || !appointmentData.date || !appointmentData.serviceId || !businessHours || !staffMembers) {
      toast.error('Missing required information');
      return;
    }

    try {
      setIsCheckingAllTimes(true);
      setAllAvailableTimes([]);

      // Get the selected service to determine duration
      const selectedService = services?.find(s => s.id === appointmentData.serviceId);
      if (!selectedService) {
        toast.error('Selected service not found');
        return;
      }

      // If the user is a staff member, only check availability for themselves
      // Otherwise, check for all eligible staff
      let eligibleStaff;
      if (isStaff && currentStaffMember) {
        console.log('Staff user is checking availability for themselves only');
        eligibleStaff = [currentStaffMember];
      } else {
        // Get all staff who can perform this service
        eligibleStaff = staffMembers.filter(staff =>
          staff.isActive !== false &&
          (staff.services || []).includes(appointmentData.serviceId)
        );
      }

      if (eligibleStaff.length === 0) {
        toast.error('No staff members available for this service');
        return;
      }

      // Check availability for each staff member
      const staffAvailability = [];

      for (const staff of eligibleStaff) {
        const slots = await getStaffAvailableSlots(
          businessId,
          staff.id,
          appointmentData.date,
          selectedService.durationMinutes,
          businessHours,
          businessTimezone
        );

        // Format slots for the UI
        const formattedSlots = slots.map(slot => ({
          time: format(slot.time, 'HH:mm'),
          formattedTime: slot.formattedTime
        }));

        if (formattedSlots.length > 0) {
          staffAvailability.push({
            staffId: staff.id,
            staffName: staff.name,
            slots: formattedSlots
          });
        }
      }

      setAllAvailableTimes(staffAvailability);

      if (staffAvailability.length === 0) {
        toast.info('No available times found for any staff member on the selected date');
      } else {
        setShowAllTimesModal(true);
      }
    } catch (error) {
      console.error('Error checking all available times:', error);
      toast.error('Failed to check availability');
    } finally {
      setIsCheckingAllTimes(false);
    }
  };

  // Function to generate time slots based on business hours
  const generateBusinessHourTimeSlots = (date: Date, businessHours: any, _timezone: string) => {
    if (!date || !businessHours) {
      return [];
    }

    // Get day of week (0 = Sunday, 1 = Monday, etc.)
    const dayOfWeek = date.getDay();
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayName = dayNames[dayOfWeek];

    // Check if business is open on this day
    if (!businessHours[dayName] || !businessHours[dayName].isOpen) {
      console.log(`Business is closed on ${dayName}`);
      return [];
    }

    // Get business hours for this day
    const dayHours = businessHours[dayName];
    const startTime = dayHours.start || '09:00';
    const endTime = dayHours.end || '17:00';

    console.log(`Business hours on ${dayName}: ${startTime} - ${endTime}`);

    // Parse start and end hours
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);

    // Calculate total minutes in the day
    const startTotalMinutes = startHour * 60 + startMinute;
    const endTotalMinutes = endHour * 60 + endMinute;

    // Generate time slots in 30-minute intervals
    const slots = [];
    for (let minutes = startTotalMinutes; minutes < endTotalMinutes; minutes += 30) {
      const hour = Math.floor(minutes / 60);
      const minute = minutes % 60;

      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

      // Create a date object for display formatting
      const slotDate = new Date(date);
      slotDate.setHours(hour, minute, 0, 0);

      // Format the time for display
      const displayTime = format(slotDate, 'h:mm a');

      slots.push({
        time,
        displayTime
      });
    }

    return slots;
  };

  // Handle round-robin toggle
  const handleRoundRobinToggle = (checked: boolean) => {
    // Log available staff for debugging
    console.log('Available staff for round-robin:', availableStaff);

    // Check if business owner is in staff list
    const businessOwnerAsStaff = staffMembers?.find(staff => staff.isOwner === true);
    if (businessOwnerAsStaff) {
      console.log('Business owner is available as staff:', businessOwnerAsStaff);
    } else {
      console.log('Business owner is not available as staff');
    }

    setAppointmentData(prev => ({
      ...prev,
      useRoundRobin: checked,
      staffId: checked ? '' : prev.staffId, // Clear staff selection if round-robin is enabled
    }));
  };

  const isLoading = isLoadingServices || isLoadingStaff;
  const error = servicesError || staffError;

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" onClick={() => router.back()} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">New Appointment</h1>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>There was a problem loading the required data</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">{error instanceof Error ? error.message : 'Unknown error'}</p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={() => router.back()}>Go Back</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="p-3 sm:p-0 bg-background dark:bg-slate-900 border-b sm:border-0 mb-2 sm:mb-0">
        <div className="flex items-center gap-2">
          <Button variant="ghost" onClick={() => router.back()} className="h-9 px-2 sm:h-10 sm:px-3">
            <ArrowLeft className="h-4 w-4 mr-1" />
            <span className="text-sm">Back</span>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold tracking-tight">New Appointment</h1>
        </div>
      </div>

      {/* Google Calendar Status Alert - Only show if we've tried to load tokens and failed */}
      {!isCheckingGoogleCalendar && !googleCalendarEnabled && (
        <div className="bg-amber-50 border border-amber-200 text-amber-800 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Note:</strong>
          <span className="block sm:inline"> Google Calendar integration is currently disconnected. Appointments will still be saved to the system but won't appear in Google Calendar.</span>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Schedule a New Appointment</CardTitle>
          <CardDescription>Fill out the form below to create a new appointment</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <>
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">1. Select Service</h3>

                  {isStaff && currentStaffMember ? (
                    // For staff users, only show services they can perform
                    <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                      {services && services.length > 0 ? (
                        // Filter services to only show those the staff member can perform
                        services
                          .filter(service => (currentStaffMember.services || []).includes(service.id))
                          .map(service => (
                            <div
                              key={service.id}
                              className={`relative flex flex-col overflow-hidden rounded-md border p-3 sm:p-4 cursor-pointer transition-colors ${
                                appointmentData.serviceId === service.id
                                  ? 'border-primary bg-primary/5'
                                  : 'hover:border-primary/50'
                              }`}
                              onClick={() => handleServiceChange(service.id)}
                            >
                              <div className="h-1.5 w-full absolute top-0 left-0" style={{ backgroundColor: service.color || '#4f46e5' }} />
                              <div className="font-medium">{service.name}</div>
                              <div className="text-sm text-muted-foreground mt-1 flex items-center gap-1">
                                <Clock className="h-3.5 w-3.5" />
                                <span>{service.durationMinutes} min</span>
                                {service.price !== undefined && service.price > 0 && (
                                  <span className="ml-2 text-sm text-muted-foreground">
                                    ${service.price.toFixed(2)}
                                  </span>
                                )}
                              </div>
                              {service.description && (
                                <div className="mt-2 text-xs text-muted-foreground line-clamp-2">{service.description}</div>
                              )}
                            </div>
                          ))
                      ) : (
                        <div className="col-span-full text-center py-4 space-y-3">
                          <p className="text-muted-foreground">No services available.</p>
                          <p className="text-xs text-muted-foreground">Please contact your administrator to add services.</p>
                        </div>
                      )}

                      {services && services.length > 0 &&
                       services.filter(service => (currentStaffMember.services || []).includes(service.id)).length === 0 && (
                        <div className="col-span-full p-4 border border-amber-200 bg-amber-50 rounded-md">
                          <div className="flex items-center text-amber-800 mb-2">
                            <AlertCircle className="h-4 w-4 mr-2" />
                            <span className="font-medium">No services assigned</span>
                          </div>
                          <p className="text-sm text-amber-700">
                            You don't have any services assigned to your profile. Please contact your administrator to assign services to you.
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    // For business users, show all services
                    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                      {services && services.length > 0 ? (
                        services.map(service => (
                          <div
                            key={service.id}
                            className={`relative flex flex-col overflow-hidden rounded-md border p-4 cursor-pointer transition-colors ${
                              appointmentData.serviceId === service.id
                                ? 'border-primary bg-primary/5'
                                : 'hover:border-primary/50'
                            }`}
                            onClick={() => handleServiceChange(service.id)}
                          >
                            <div className="h-1.5 w-full absolute top-0 left-0" style={{ backgroundColor: service.color || '#4f46e5' }} />
                            <div className="font-medium">{service.name}</div>
                            <div className="text-sm text-muted-foreground mt-1 flex items-center gap-1">
                              <Clock className="h-3.5 w-3.5" />
                              <span>{service.durationMinutes} min</span>
                              {service.price !== undefined && service.price > 0 && (
                                <span className="ml-2 text-sm text-muted-foreground">
                                  ${service.price.toFixed(2)}
                                </span>
                              )}
                            </div>
                            {service.description && (
                              <div className="mt-2 text-xs text-muted-foreground line-clamp-2">{service.description}</div>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="col-span-full text-center py-4 space-y-3">
                          <p className="text-muted-foreground">No services available.</p>
                          <p className="text-xs text-muted-foreground">Please contact your administrator to add services.</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {appointmentData.serviceId && (
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">2. Select Date</h3>
                    <div className="space-y-2">
                      <Label htmlFor="appointment-date">Date</Label>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <div className="flex-1">
                          <div className="border rounded-md p-4">
                            <Calendar
                              mode="single"
                              selected={appointmentData.date}
                              onSelect={(date) => {
                                if (date) {
                                  setAppointmentData(prev => ({ ...prev, date, staffId: '', time: '' }));
                                }
                              }}
                              disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                              className="rounded-md border-0"
                            />
                          </div>
                        </div>

                        {appointmentData.date && (
                          <Button
                            type="button"
                            variant="secondary"
                            className="flex items-center gap-1 sm:gap-2 text-sm sm:text-base justify-center whitespace-nowrap"
                            onClick={checkAllAvailableTimes}
                            disabled={isCheckingAllTimes}
                          >
                            {isCheckingAllTimes ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span className="sm:inline hidden">Checking availability...</span>
                                <span className="inline sm:hidden">Checking...</span>
                              </>
                            ) : (
                              <>
                                <Clock className="h-4 w-4" />
                                <span className="sm:inline hidden">Check Available Times</span>
                                <span className="inline sm:hidden">Check Times</span>
                              </>
                            )}
                          </Button>
                        )}
                        </div>
                      </div>
                    </div>
                )}

                {appointmentData.serviceId && appointmentData.date && (
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">3. Select Staff</h3>

                    {!isStaff && (
                      <div className="flex items-center space-x-2 mb-4">
                        <Checkbox
                          id="use-round-robin"
                          checked={appointmentData.useRoundRobin}
                          onCheckedChange={handleRoundRobinToggle}
                        />
                        <Label htmlFor="use-round-robin" className="font-normal">
                          Use round-robin scheduling (automatically assign available staff)
                        </Label>
                      </div>
                    )}

                    {isStaff && currentStaffMember && (
                      <div className="mb-4 p-3 bg-muted/30 rounded-md">
                        <p className="text-sm">As a staff member, you can only create appointments assigned to yourself.</p>
                      </div>
                    )}

                    {!appointmentData.useRoundRobin && (
                      <div className="grid gap-2 sm:gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                        {availableStaff.length > 0 ? (
                          // For staff users, only show their own profile
                          // For business users, show all available staff
                          (isStaff ? availableStaff.filter(staff => staff.id === currentStaffMember?.id) : availableStaff).map(staff => (
                            <div
                              key={staff.id}
                              className={`relative flex items-start gap-2 sm:gap-3 rounded-md border p-2 sm:p-3 md:p-4 cursor-pointer transition-colors ${
                                appointmentData.staffId === staff.id
                                  ? 'border-primary bg-primary/5'
                                  : staffAvailability[staff.id] === false
                                  ? 'border-muted bg-muted/30 opacity-70'
                                  : 'hover:border-primary/50'
                              }`}
                              onClick={() => {
                                // Only allow selection if staff is available or we don't know yet
                                if (staffAvailability[staff.id] !== false) {
                                  setAppointmentData(prev => ({ ...prev, staffId: staff.id, time: '' }));
                                } else {
                                  toast.info(`${staff.name} is not available on the selected date`);
                                }
                              }}
                            >
                              {staff.photoURL ? (
                                <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full overflow-hidden flex-shrink-0">
                                  <img src={staff.photoURL} alt={staff.name} className="h-full w-full object-cover" />
                                </div>
                              ) : (
                                <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-muted flex items-center justify-center flex-shrink-0">
                                  <User className="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
                                </div>
                              )}
                              <div className="flex-1">
                                <div className="font-medium">{staff.name}</div>
                                <div className="text-sm text-muted-foreground">{staff.role}</div>

                                {/* Availability indicator */}
                                <div className="mt-1">
                                  {staffAvailability[staff.id] === undefined ? (
                                    <div className="flex items-center text-xs text-muted-foreground">
                                      <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                      <span>Checking availability...</span>
                                    </div>
                                  ) : staffAvailability[staff.id] ? (
                                    <div className="flex items-center text-xs text-green-600">
                                      <Clock className="h-3 w-3 mr-1" />
                                      <span>Available on selected date</span>
                                    </div>
                                  ) : (
                                    <div className="flex items-center text-xs text-amber-600">
                                      <AlertCircle className="h-3 w-3 mr-1" />
                                      <span>Not available on selected date</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))
                        ) : (
                          <div className="col-span-full text-center py-4 text-muted-foreground">
                            No staff members available for this service.
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {(appointmentData.staffId || appointmentData.useRoundRobin) && appointmentData.serviceId && appointmentData.date && (
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium">4. Select Time</h3>
                    <div className="grid gap-6 sm:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="appointment-time">Time</Label>
                        {appointmentData.useRoundRobin ? (
                          // For round-robin, show all possible time slots
                          <Select
                            value={appointmentData.time}
                            onValueChange={(time) => setAppointmentData(prev => ({ ...prev, time }))}
                          >
                            <SelectTrigger id="appointment-time">
                              <SelectValue placeholder="Select a time" />
                            </SelectTrigger>
                            <SelectContent>
                              {/* Generate business hours time slots in 30-minute intervals */}
                              {generateBusinessHourTimeSlots(appointmentData.date, businessHours, businessTimezone).map((slot) => {
                                return (
                                  <SelectItem key={slot.time} value={slot.time}>
                                    {slot.displayTime}
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>
                        ) : (
                          // For specific staff, show only available time slots
                          <div>
                            {isLoadingTimeSlots ? (
                              <div className="flex items-center justify-center py-2">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                <span className="text-sm">Loading available times...</span>
                              </div>
                            ) : availableTimeSlots.length > 0 ? (
                              <Select
                                value={appointmentData.time}
                                onValueChange={(time) => setAppointmentData(prev => ({ ...prev, time }))}
                              >
                                <SelectTrigger id="appointment-time">
                                  <SelectValue placeholder="Select a time" />
                                </SelectTrigger>
                                <SelectContent>
                                  {availableTimeSlots.map((slot) => (
                                    <SelectItem key={slot.time} value={slot.time}>
                                      {slot.formattedTime}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            ) : (
                              <div className="border rounded-md p-3 bg-muted/30">
                                <div className="flex items-center text-amber-600">
                                  <AlertCircle className="h-4 w-4 mr-2" />
                                  <span className="text-sm font-medium">No available slots</span>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                  This staff member has no available slots for the selected date.
                                  Please try another date or staff member.
                                </p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {appointmentData.time && (
                  <div className="space-y-6">
                    <Tabs defaultValue="basic" className="w-full">
                      <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="basic" className="flex items-center justify-center gap-1 text-xs sm:text-sm py-1.5 px-1 sm:px-2">
                          <User className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                          <span className="hidden sm:inline">Basic</span>
                        </TabsTrigger>
                        <TabsTrigger value="recurring" className="flex items-center justify-center gap-1 text-xs sm:text-sm py-1.5 px-1 sm:px-2">
                          <Repeat className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                          <span className="hidden sm:inline">Recurring</span>
                        </TabsTrigger>
                        <TabsTrigger value="group" className="flex items-center justify-center gap-1 text-xs sm:text-sm py-1.5 px-1 sm:px-2">
                          <UserPlus className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                          <span className="hidden sm:inline">Group</span>
                        </TabsTrigger>
                        <TabsTrigger value="advanced" className="flex items-center justify-center gap-1 text-xs sm:text-sm py-1.5 px-1 sm:px-2">
                          <Clock1 className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                          <span className="hidden sm:inline">Advanced</span>
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="basic" className="mt-4">
                        <div className="space-y-4">
                          <h3 className="text-sm font-medium">4. Client Information</h3>
                          <div className="grid gap-3 sm:gap-4 sm:grid-cols-2">
                            <div className="space-y-1.5 sm:space-y-2">
                              <Label htmlFor="client-name" className="text-xs sm:text-sm">Name</Label>
                              <Input
                                id="client-name"
                                placeholder="Client's full name"
                                value={appointmentData.clientName}
                                onChange={(e) => setAppointmentData(prev => ({ ...prev, clientName: e.target.value }))}
                                required
                                className="h-8 sm:h-10 text-sm"
                              />
                            </div>
                            <div className="space-y-1.5 sm:space-y-2">
                              <Label htmlFor="client-email" className="text-xs sm:text-sm">Email</Label>
                              <Input
                                id="client-email"
                                type="email"
                                placeholder="<EMAIL>"
                                value={appointmentData.clientEmail}
                                onChange={(e) => setAppointmentData(prev => ({ ...prev, clientEmail: e.target.value }))}
                                required
                                className="h-8 sm:h-10 text-sm"
                              />
                            </div>
                            <div className="space-y-1.5 sm:space-y-2">
                              <Label htmlFor="client-phone" className="text-xs sm:text-sm">Phone (Optional)</Label>
                              <Input
                                id="client-phone"
                                type="tel"
                                placeholder="(*************"
                                value={appointmentData.clientPhone}
                                onChange={(e) => setAppointmentData(prev => ({ ...prev, clientPhone: e.target.value }))}
                                className="h-8 sm:h-10 text-sm"
                              />
                            </div>
                            <div className="space-y-1.5 sm:space-y-2 sm:col-span-2">
                              <Label htmlFor="appointment-notes" className="text-xs sm:text-sm">Notes (Optional)</Label>
                              <Textarea
                                id="appointment-notes"
                                placeholder="Any special requests or additional information..."
                                value={appointmentData.notes}
                                onChange={(e) => setAppointmentData(prev => ({ ...prev, notes: e.target.value }))}
                                rows={3}
                                className="text-sm"
                              />
                            </div>

                            <div className="space-y-1.5 sm:space-y-2 sm:col-span-2">
                              <Label htmlFor="client-preferences" className="text-xs sm:text-sm">Client Preferences (Optional)</Label>
                              <Textarea
                                id="client-preferences"
                                placeholder="Client preferences, allergies, or special accommodations..."
                                value={appointmentData.clientPreferences}
                                onChange={(e) => setAppointmentData(prev => ({ ...prev, clientPreferences: e.target.value }))}
                                rows={2}
                                className="text-sm"
                              />
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="recurring" className="mt-4">
                        <div className="space-y-4">
                          <h3 className="text-sm font-medium">Recurring Appointment Settings</h3>
                          <RecurringAppointmentForm
                            isEnabled={appointmentData.isRecurring}
                            onToggle={(enabled) => setAppointmentData(prev => ({ ...prev, isRecurring: enabled }))}
                            recurringPattern={appointmentData.recurringPattern}
                            onPatternChange={(pattern) => {
                              // Convert any custom frequency to weekly to avoid type errors
                              // Ensure all required properties are present
                              const safePattern = {
                                ...pattern,
                                frequency: pattern.frequency === 'custom' ? 'weekly' : pattern.frequency,
                                daysOfWeek: pattern.daysOfWeek || [new Date().getDay()],
                                endAfterOccurrences: pattern.endAfterOccurrences || 10
                              };
                              setAppointmentData(prev => ({ ...prev, recurringPattern: safePattern }));
                            }}
                            maxEndDate={new Date(new Date().setMonth(new Date().getMonth() + 6))}
                            maxOccurrences={52}
                            allowedFrequencies={selectedService?.allowRecurring ?
                              (selectedService.recurringOptions?.frequencies || ['daily', 'weekly', 'monthly']) :
                              ['weekly']}
                          />
                        </div>
                      </TabsContent>

                      <TabsContent value="group" className="mt-4">
                        <div className="space-y-4">
                          <h3 className="text-sm font-medium">Group Appointment Settings</h3>
                          <DisabledGroupAppointmentForm
                            isEnabled={appointmentData.isGroupAppointment}
                            onToggle={(enabled) => setAppointmentData(prev => ({ ...prev, isGroupAppointment: enabled }))}
                            service={selectedService || null}
                            participants={appointmentData.participants}
                            onParticipantsChange={(participants) => {
                              // Convert participants to match our expected type
                              const safeParticipants = participants.map(p => ({
                                name: p.name,
                                email: p.email,
                                phone: undefined,
                                notes: undefined
                              }));
                              setAppointmentData(prev => ({ ...prev, participants: safeParticipants }));
                            }}
                            maxParticipants={selectedService?.maxParticipants || 10}
                            minParticipants={selectedService?.minParticipants || 2}
                          />
                        </div>
                      </TabsContent>

                      <TabsContent value="advanced" className="mt-4">
                        <div className="space-y-6">
                          <div className="space-y-4">
                            <h3 className="text-sm font-medium">Buffer Time Settings</h3>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="use-buffer-time"
                                checked={appointmentData.useBufferTime}
                                onCheckedChange={(checked) => setAppointmentData(prev => ({ ...prev, useBufferTime: !!checked }))}
                              />
                              <Label htmlFor="use-buffer-time" className="font-medium">
                                Add custom buffer time for this appointment
                              </Label>
                            </div>

                            {appointmentData.useBufferTime && (
                              <div className="grid gap-4 sm:grid-cols-2 mt-2">
                                <div className="space-y-2">
                                  <Label htmlFor="buffer-before">Buffer time before (minutes)</Label>
                                  <Input
                                    id="buffer-before"
                                    type="number"
                                    min="0"
                                    max="120"
                                    value={appointmentData.bufferMinutesBefore}
                                    onChange={(e) => setAppointmentData(prev => ({
                                      ...prev,
                                      bufferMinutesBefore: parseInt(e.target.value) || 0
                                    }))}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="buffer-after">Buffer time after (minutes)</Label>
                                  <Input
                                    id="buffer-after"
                                    type="number"
                                    min="0"
                                    max="120"
                                    value={appointmentData.bufferMinutesAfter}
                                    onChange={(e) => setAppointmentData(prev => ({
                                      ...prev,
                                      bufferMinutesAfter: parseInt(e.target.value) || 0
                                    }))}
                                  />
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="space-y-4">
                            <h3 className="text-sm font-medium">Waitlist Settings</h3>
                            <DisabledWaitlistForm
                              isEnabled={appointmentData.joinWaitlist}
                              onToggle={(enabled) => setAppointmentData(prev => ({ ...prev, joinWaitlist: enabled }))}
                              service={selectedService || undefined}
                              businessId={businessId}
                              onWaitlistAdded={() => console.log('Waitlist preferences saved for new appointment')}
                            />
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>

                    {/* Notification Preferences */}
                    <div className="pt-6 border-t mt-6">
                      <NotificationPreferences
                        value={appointmentData.notificationMethod}
                        onChange={(method) => setAppointmentData(prev => ({ ...prev, notificationMethod: method }))}
                        googleCalendarConnected={googleCalendarEnabled}
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => router.back()} className="h-9 sm:h-10 text-xs sm:text-sm">
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || isLoading} className="h-9 sm:h-10 text-xs sm:text-sm">
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-1.5 sm:mr-2 h-3.5 w-3.5 sm:h-4 sm:w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Appointment'
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>

      {/* Modal for showing all available times */}
      <AllAvailableTimesModal
        isOpen={showAllTimesModal}
        onClose={() => setShowAllTimesModal(false)}
        availableTimes={allAvailableTimes}
        onSelectTimeSlot={handleSelectTimeSlot}
        selectedDate={appointmentData.date}
      />
    </div>
  );

  // Handle selecting a time slot from the modal
  function handleSelectTimeSlot(staffId: string, time: string) {
    // Find the staff member
    const staff = staffMembers?.find(s => s.id === staffId);
    if (!staff) return;

    // Find the formatted time
    const staffAvailability = allAvailableTimes.find(s => s.staffId === staffId);
    const slot = staffAvailability?.slots.find(s => s.time === time);
    if (!slot) return;

    // Update the appointment data
    setAppointmentData(prev => ({
      ...prev,
      staffId,
      time,
      useRoundRobin: false
    }));

    // Close the modal
    setShowAllTimesModal(false);

    // Show a success message
    toast.success(`Selected ${slot.formattedTime} with ${staff.name}`);
  }
}
