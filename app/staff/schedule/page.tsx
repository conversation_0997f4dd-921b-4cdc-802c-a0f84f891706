'use client';

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { AppointmentList } from '@/components/features/AppointmentList';
import { AddAppointmentDialog } from '@/components/features/AddAppointmentDialog';
import { AppointmentFilter, AppointmentFilters } from '@/components/features/AppointmentFilter';
import { getBusinessAppointments, getStaffAppointments, createAppointment } from '@/lib/services/appointmentService';
import { getGoogleTokens } from '@/lib/services/googleAuthService';
import { getServices } from '@/lib/services/serviceService';
import { getStaff } from '@/lib/services/staffService';
import { getBusiness } from '@/lib/services/businessService';
import { useAuth } from '@/components/providers/AuthProvider';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import { Appointment, ServiceDefinition, StaffMember } from '@/lib/types/db';

export default function SchedulePage() {
  const { user, isStaff, businessId: authBusinessId } = useAuth();
  const queryClient = useQueryClient();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past'>('upcoming');
  const [filters, setFilters] = useState<AppointmentFilters>({
    serviceIds: [],
    staffIds: [],
    statuses: []
  });

  // Fetch business data (mainly for timezone)
  const { data: business, isLoading: isLoadingBusiness } = useQuery({
    queryKey: ['business', authBusinessId],
    queryFn: () => getBusiness(authBusinessId || ''),
    enabled: !!authBusinessId,
  });

  // Fetch staff members
  const { data: staffMembers, isLoading: isLoadingStaff, error: errorStaff } = useQuery({
    queryKey: ['staff', authBusinessId],
    queryFn: () => getStaff(authBusinessId || ''),
    enabled: !!authBusinessId,
  });

  // Find the current staff member if user is staff
  const currentStaffMember = React.useMemo(() => {
    if (!staffMembers || !user?.email) return null;
    return staffMembers.find(staff => staff.email === user.email) || null;
  }, [staffMembers, user?.email]);

  // Fetch appointments (all for business owner, only assigned ones for staff)
  const { data: appointments, isLoading: isLoadingAppointments, error: errorAppointments } = useQuery({
    queryKey: ['appointments', authBusinessId, isStaff, currentStaffMember?.id],
    queryFn: () => {
      // If user is staff and we have their staff ID, fetch only their appointments
      if (isStaff && currentStaffMember?.id) {
        console.log(`Fetching appointments for staff member: ${currentStaffMember.id}`);
        return getStaffAppointments(authBusinessId || '', currentStaffMember.id);
      } else {
        // Otherwise fetch all business appointments
        console.log(`Fetching all appointments for business: ${authBusinessId}`);
        return getBusinessAppointments(authBusinessId || '');
      }
    },
    enabled: !!authBusinessId && (!isStaff || !!currentStaffMember?.id),
    staleTime: 1000 * 60, // Consider appointments data slightly less stale (e.g., 1 min)
  });

  // Fetch services
  const { data: services, isLoading: isLoadingServices, error: errorServices } = useQuery({
    queryKey: ['services', authBusinessId],
    queryFn: () => getServices(authBusinessId || ''),
    enabled: !!authBusinessId,
  });

  // Get current staff member ID
  const currentStaffId = currentStaffMember?.id;

  // Get Google Calendar token - handle errors gracefully
  const { data: googleTokens, isLoading: isLoadingGoogleTokens } = useQuery({
    queryKey: ['googleTokens', currentStaffId],
    queryFn: async () => {
      try {
        // For staff pages, we need to use the staff ID, not the business ID
        if (currentStaffId) {
          console.log('Getting Google tokens for staff ID:', currentStaffId);
          return await getGoogleTokens(currentStaffId, 'staff');
        } else {
          console.log('No staff ID available, falling back to business ID');
          return await getGoogleTokens(authBusinessId || '');
        }
      } catch (error) {
        console.error('Error getting Google tokens:', error);
        return null; // Return null instead of throwing an error
      }
    },
    enabled: !!(currentStaffId || authBusinessId),
  });

  const googleAccessToken = googleTokens?.access_token || null;

  const isLoading = isLoadingBusiness || isLoadingAppointments || isLoadingServices || isLoadingStaff || isLoadingGoogleTokens;
  const error = errorAppointments || errorServices || errorStaff;

  // Apply filters to appointments
  const filteredAppointments = useMemo(() => {
    if (!appointments) return [];

    return appointments.filter(appointment => {
      // Filter by service
      if (filters.serviceIds.length > 0 && !filters.serviceIds.includes(appointment.serviceId)) {
        return false;
      }

      // Filter by staff
      if (filters.staffIds.length > 0) {
        // Handle round-robin appointments
        if (appointment.useRoundRobin && !appointment.staffId) {
          // If filtering by staff and this is an unassigned round-robin appointment, hide it
          return false;
        }

        // If appointment has a staffId, check if it's in the filter
        if (appointment.staffId && !filters.staffIds.includes(appointment.staffId)) {
          return false;
        }
      }

      // Filter by status
      if (filters.statuses.length > 0 && !filters.statuses.includes(appointment.status)) {
        return false;
      }

      return true;
    });
  }, [appointments, filters]);

  const handleAddAppointment = async (appointmentData: any) => {
    try {
      // Remove properties that are not part of the Appointment type
      const { addToGoogleCalendar, useRoundRobin, ...appointmentDataForFirebase } = appointmentData;

      console.log('Creating appointment with data:', appointmentDataForFirebase);
      console.log('Business ID:', authBusinessId);
      console.log('Google Access Token:', googleAccessToken ? 'Present' : 'Not present');
      console.log('Add to Google Calendar:', addToGoogleCalendar);

      // Ensure all required fields are present
      if (!appointmentDataForFirebase.businessId) {
        throw new Error('Business ID is required');
      }

      if (!appointmentDataForFirebase.serviceId) {
        throw new Error('Service ID is required');
      }

      if (!appointmentDataForFirebase.startTime || !appointmentDataForFirebase.endTime) {
        throw new Error('Start time and end time are required');
      }

      // Create appointment in Firebase
      const docRef = await createAppointment(appointmentDataForFirebase, 'email_ics'); // Default to email_ics

      // console.log('Appointment created in Firebase with ID:', docRef.id);

      // Invalidate appointments query to trigger a refresh
      queryClient.invalidateQueries({ queryKey: ['appointments'] });

      // Show success message with Google Calendar info
      if (addToGoogleCalendar && googleAccessToken) {
        alert('Appointment created successfully and added to Google Calendar!');
      } else {
        alert('Appointment created successfully!');
      }
    } catch (error) {
      console.error('Error creating appointment:', error);

      // Log more detailed error information
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      } else {
        console.error('Error details:', JSON.stringify(error, null, 2));
      }

      // Show error message
      alert('Failed to create appointment: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  return (
    <div className="space-y-6">
      <div className="p-3 sm:p-0 bg-background dark:bg-slate-900 border-b sm:border-0 mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <h1 className="text-xl sm:text-2xl md:text-3xl font-bold tracking-tight">
            <span className="sm:hidden block">My</span>
            <span className="sm:hidden block">Appointment</span>
            <span className="sm:hidden block">Schedule</span>
            <span className="hidden sm:inline">My Appointment Schedule</span>
          </h1>
          <div className="flex gap-2 mt-1 sm:mt-0">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 sm:flex-none text-xs sm:text-sm py-1 h-9 sm:h-10"
              onClick={() => window.location.href = '/staff/schedule/new'}
            >
              Advanced Booking
            </Button>
            <Button
              size="sm"
              className="flex-1 sm:flex-none text-xs sm:text-sm py-1 h-9 sm:h-10"
              onClick={() => setIsAddDialogOpen(true)}
            >
              Quick Add
            </Button>
          </div>
        </div>
      </div>

      {/* Google Calendar Status Alert - Only show if we've tried to load tokens and failed */}
      {!isLoadingGoogleTokens && googleAccessToken === null && (
        <div className="bg-amber-50 border border-amber-200 text-amber-800 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Note:</strong>
          <span className="block sm:inline"> Google Calendar integration is currently disconnected. Appointments will still be saved to the system but won't appear in Google Calendar.</span>
        </div>
      )}

      <div className="space-y-2 sm:space-y-4 px-3 sm:px-0">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 sm:gap-3">
          <div className="flex space-x-2 border-b pb-1 sm:pb-2 overflow-x-auto">
            <Button
              variant={activeTab === 'upcoming' ? 'default' : 'ghost'}
              onClick={() => setActiveTab('upcoming')}
              className="rounded-b-none text-xs sm:text-sm h-8 sm:h-9"
              size="sm"
            >
              Upcoming
            </Button>
            <Button
              variant={activeTab === 'past' ? 'default' : 'ghost'}
              onClick={() => setActiveTab('past')}
              className="rounded-b-none text-xs sm:text-sm h-8 sm:h-9"
              size="sm"
            >
              Past
            </Button>
          </div>

          {/* Filter component */}
          {!isLoading && services && staffMembers && (
            <div className="self-end sm:self-auto">
              <AppointmentFilter
                services={services}
                staffMembers={staffMembers}
                filters={filters}
                onFiltersChange={setFilters}
              />
            </div>
          )}
        </div>
      </div>

      <AddAppointmentDialog
        isOpen={isAddDialogOpen}
        onClose={() => setIsAddDialogOpen(false)}
        onAddAppointment={handleAddAppointment}
        services={services || []}
        staffMembers={staffMembers || []}
        businessId={authBusinessId || ''}
      />

      {process.env.NODE_ENV === 'development' && (
        <div className="mb-4 p-4 bg-muted rounded-md text-xs">
          <p className="font-semibold">Debug Info:</p>
          <p>Appointments loaded: {appointments?.length || 0}</p>
          <p>Services loaded: {services?.length || 0}</p>
          <p>Staff members loaded: {staffMembers?.length || 0}</p>
          <p>Current user: {user?.email || 'Not logged in'}</p>
          <p>Is staff view: {isStaff ? 'Yes' : 'No'}</p>
          <p>Current staff ID: {currentStaffMember?.id || 'N/A'}</p>
          <p>Loading state: {isLoading ? 'Loading...' : 'Loaded'}</p>
          <p>Error: {error ? (error instanceof Error ? error.message : String(error)) : 'None'}</p>
          <p>Google Calendar token: {googleAccessToken ? 'Present' : 'Not present'}</p>
        </div>
      )}

      {/* Debug info for filters */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mb-4 p-2 bg-muted/50 rounded-md text-xs">
          <p>Active filters: {filters.serviceIds.length + filters.staffIds.length + filters.statuses.length}</p>
          {filters.serviceIds.length > 0 && (
            <p>Services: {filters.serviceIds.map(id => services?.find(s => s.id === id)?.name).join(', ')}</p>
          )}
          {filters.staffIds.length > 0 && (
            <p>Staff: {filters.staffIds.map(id => staffMembers?.find(s => s.id === id)?.name).join(', ')}</p>
          )}
          {filters.statuses.length > 0 && (
            <p>Statuses: {filters.statuses.join(', ')}</p>
          )}
        </div>
      )}

      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading appointments...</span>
        </div>
      )}

      {!isLoading && (
        <AppointmentList
          appointments={filteredAppointments}
          services={services || []}
          staffMembers={staffMembers || []}
          isLoading={false}
          error={error instanceof Error ? error : null}
          businessTimezone={business?.timeZone || 'UTC'}
          onAppointmentUpdated={() => {
            // This callback is used for reloading data
            queryClient.invalidateQueries({ queryKey: ['appointments'] });
          }}
          openAddDialogOnUpdate={false}
          googleAccessToken={googleAccessToken}
          showPastAppointments={activeTab === 'past'}
        />
      )}
    </div>
  );
}
