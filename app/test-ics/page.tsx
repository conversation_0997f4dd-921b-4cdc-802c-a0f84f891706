'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { generateICSContent, generateICSFileName, downloadICSFile } from '@/lib/services/icsService';
import { Appointment } from '@/lib/types/db';
import { Timestamp } from 'firebase/firestore';
import { Download, Calendar } from 'lucide-react';

export default function TestICSPage() {
  const [formData, setFormData] = useState({
    clientName: 'John Doe',
    clientEmail: '<EMAIL>',
    serviceName: 'Hair Cut',
    staffName: '<PERSON>',
    businessName: 'The Hair Studio',
    businessAddress: '123 Main St, City, State 12345',
    date: new Date().toISOString().split('T')[0],
    startTime: '10:00',
    endTime: '11:00',
    notes: 'Please arrive 10 minutes early'
  });

  const [icsContent, setIcsContent] = useState<string>('');

  const generateTestICS = () => {
    // Create a mock appointment object
    const startDateTime = new Date(`${formData.date}T${formData.startTime}:00`);
    const endDateTime = new Date(`${formData.date}T${formData.endTime}:00`);

    const mockAppointment: Appointment = {
      id: 'test-appointment-123',
      businessId: 'test-business',
      clientName: formData.clientName,
      clientEmail: formData.clientEmail,
      serviceName: formData.serviceName,
      staffName: formData.staffName,
      startTime: Timestamp.fromDate(startDateTime),
      endTime: Timestamp.fromDate(endDateTime),
      notes: formData.notes,
      status: 'confirmed',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    // Generate ICS content
    const icsContent = generateICSContent(
      mockAppointment,
      formData.businessName,
      formData.businessAddress
    );

    setIcsContent(icsContent);
  };

  const downloadTestICS = () => {
    if (!icsContent) {
      generateTestICS();
      return;
    }

    const fileName = `test_appointment_${formData.date}.ics`;
    downloadICSFile(icsContent, fileName);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear ICS content when form changes
    setIcsContent('');
  };

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">ICS File Generator Test</h1>
          <p className="text-gray-600">Test the ICS file generation functionality</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>Appointment Details</CardTitle>
              <CardDescription>
                Fill out the form to generate a test ICS file
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="clientName">Client Name</Label>
                  <Input
                    id="clientName"
                    value={formData.clientName}
                    onChange={(e) => handleInputChange('clientName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="clientEmail">Client Email</Label>
                  <Input
                    id="clientEmail"
                    type="email"
                    value={formData.clientEmail}
                    onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="serviceName">Service Name</Label>
                  <Input
                    id="serviceName"
                    value={formData.serviceName}
                    onChange={(e) => handleInputChange('serviceName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="staffName">Staff Name</Label>
                  <Input
                    id="staffName"
                    value={formData.staffName}
                    onChange={(e) => handleInputChange('staffName', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessName">Business Name</Label>
                <Input
                  id="businessName"
                  value={formData.businessName}
                  onChange={(e) => handleInputChange('businessName', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessAddress">Business Address</Label>
                <Input
                  id="businessAddress"
                  value={formData.businessAddress}
                  onChange={(e) => handleInputChange('businessAddress', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="startTime">Start Time</Label>
                  <Input
                    id="startTime"
                    type="time"
                    value={formData.startTime}
                    onChange={(e) => handleInputChange('startTime', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endTime">End Time</Label>
                  <Input
                    id="endTime"
                    type="time"
                    value={formData.endTime}
                    onChange={(e) => handleInputChange('endTime', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="flex gap-3">
                <Button onClick={generateTestICS} className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Generate ICS
                </Button>
                <Button 
                  onClick={downloadTestICS} 
                  variant="outline" 
                  className="flex items-center gap-2"
                  disabled={!icsContent}
                >
                  <Download className="h-4 w-4" />
                  Download ICS
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* ICS Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Generated ICS Content</CardTitle>
              <CardDescription>
                Preview of the generated ICS file content
              </CardDescription>
            </CardHeader>
            <CardContent>
              {icsContent ? (
                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <pre className="text-xs overflow-auto max-h-96 whitespace-pre-wrap">
                      {icsContent}
                    </pre>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p><strong>File size:</strong> {new Blob([icsContent]).size} bytes</p>
                    <p><strong>Lines:</strong> {icsContent.split('\n').length}</p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Click "Generate ICS" to see the ICS file content</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">1</div>
                <p>Fill out the appointment details in the form above</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">2</div>
                <p>Click "Generate ICS" to create the calendar file content</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">3</div>
                <p>Review the generated ICS content in the preview panel</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">4</div>
                <p>Click "Download ICS" to download the file and test it with your calendar application</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">5</div>
                <p>Open the downloaded .ics file with Google Calendar, Outlook, Apple Calendar, or any other calendar app</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
