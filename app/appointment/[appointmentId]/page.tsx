'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getAppointmentById, cancelAppointment } from '@/lib/services/appointmentService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  Clock,
  Loader2,
  MapPin,
  User,
  XCircle,
  CheckCircle,
  CalendarX,
  Download,
  Star
} from 'lucide-react';
import { format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import { toast } from 'sonner';
import { downloadICSFile, generateICSContent, generateICSFileName } from '@/lib/services/icsService';

export default function PublicAppointmentPage({
  params
}: {
  params: Promise<{ appointmentId: string }>
}) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { appointmentId } = React.use(params);

  const [cancelReason, setCancelReason] = useState('');
  const [isCancelling, setIsCancelling] = useState(false);

  // Fetch appointment - no authentication required
  const {
    data: appointment,
    isLoading: isLoadingAppointment,
    error: appointmentError
  } = useQuery({
    queryKey: ['appointment', appointmentId],
    queryFn: () => getAppointmentById(appointmentId),
    enabled: !!appointmentId
  });

  // Cancel appointment mutation
  const cancelAppointmentMutation = useMutation({
    mutationFn: (data: { appointmentId: string; reason: string }) => {
      return cancelAppointment(data.appointmentId, data.reason);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
      toast.success('Appointment cancelled successfully');
      setIsCancelling(false);
      setCancelReason('');
    },
    onError: (error: Error) => {
      toast.error(`Error cancelling appointment: ${error.message}`);
      setIsCancelling(false);
    }
  });

  // Format date
  const formatDate = (timestamp: Timestamp) => {
    return format(timestamp.toDate(), 'EEEE, MMMM d, yyyy');
  };

  // Format time
  const formatTime = (timestamp: Timestamp) => {
    return format(timestamp.toDate(), 'h:mm a');
  };

  // Get appointment status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Completed</Badge>;
      case 'scheduled':
        return <Badge variant="outline" className="flex items-center"><Calendar className="h-3 w-3 mr-1" /> Scheduled</Badge>;
      case 'confirmed':
        return <Badge variant="default" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Confirmed</Badge>;
      case 'cancelled':
        return <Badge variant="destructive" className="flex items-center"><XCircle className="h-3 w-3 mr-1" /> Cancelled</Badge>;
      case 'no-show':
        return <Badge variant="destructive" className="flex items-center"><AlertCircle className="h-3 w-3 mr-1" /> No-show</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  // Handle cancel appointment
  const handleCancelAppointment = () => {
    if (!appointmentId) return;

    setIsCancelling(true);

    cancelAppointmentMutation.mutate({
      appointmentId,
      reason: cancelReason
    });
  };

  // Handle download ICS file
  const handleDownloadICS = () => {
    if (!appointment) return;

    // Get business name from appointment or use a default
    const businessName = 'Business'; // This should be fetched from the business data

    const icsContent = generateICSContent(appointment, businessName);
    const fileName = generateICSFileName(appointment, businessName);
    downloadICSFile(icsContent, fileName);

    toast.success('Calendar file downloaded successfully');
  };

  // Check if appointment can be cancelled
  const canCancel = appointment &&
    (appointment.status === 'scheduled' || appointment.status === 'confirmed');

  // Check if appointment can be rescheduled
  const canReschedule = appointment &&
    (appointment.status === 'scheduled' || appointment.status === 'confirmed');

  if (isLoadingAppointment) {
    return (
      <div className="flex justify-center items-center py-32">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (appointmentError || !appointment) {
    return (
      <div className="flex justify-center items-center p-4 py-32">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-destructive mb-4" />
            <h3 className="text-lg font-medium">Appointment Not Found</h3>
            <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
              {appointmentError
                ? (appointmentError as Error).message
                : "We couldn't find the appointment you're looking for."}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Appointment Details</h1>
              <p className="text-sm text-muted-foreground">View and manage your appointment</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleDownloadICS}>
                <Download className="h-4 w-4 mr-2" />
                Download Calendar File
              </Button>

              {appointment.status === 'completed' && (
                <Button variant="outline" asChild>
                  <Link href={`/review/${appointment.id}`}>
                    <Star className="h-4 w-4 mr-2" />
                    Leave Review
                  </Link>
                </Button>
              )}

              {canReschedule && (
                <Button variant="outline" asChild>
                  <Link href={`/appointment/${appointment.id}/reschedule`}>
                    <Calendar className="h-4 w-4 mr-2" />
                    Reschedule
                  </Link>
                </Button>
              )}

              {canCancel && (
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="destructive">
                      <CalendarX className="h-4 w-4 mr-2" />
                      Cancel Appointment
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Cancel Appointment</DialogTitle>
                      <DialogDescription>
                        Are you sure you want to cancel this appointment? This action cannot be undone.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Appointment Details:</p>
                        <p className="text-sm">
                          {appointment.serviceName} on {formatDate(appointment.startTime)} at {formatTime(appointment.startTime)}
                        </p>
                      </div>
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Reason for cancellation (optional):</p>
                        <Textarea
                          value={cancelReason}
                          onChange={(e) => setCancelReason(e.target.value)}
                          placeholder="Please provide a reason for cancellation..."
                          rows={3}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setCancelReason('')}>
                        Keep Appointment
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleCancelAppointment}
                        disabled={isCancelling}
                      >
                        {isCancelling && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Cancel Appointment
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">{appointment.serviceName}</CardTitle>
                  <CardDescription>Appointment Information</CardDescription>
                </div>
                {getStatusBadge(appointment.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium mb-1">Date & Time</h3>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{formatDate(appointment.startTime)}</span>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{formatTime(appointment.startTime)} - {formatTime(appointment.endTime)}</span>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-1">Staff</h3>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>{appointment.staffName || 'Any available staff'}</span>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-1">Client</h3>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>{appointment.clientName}</span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {appointment.clientEmail}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium mb-1">Service Details</h3>
                    <p className="text-sm">{appointment.serviceDescription || 'Professional service appointment.'}</p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-1">Duration</h3>
                    <p className="text-sm">{appointment.durationMinutes} minutes</p>
                  </div>

                  {appointment.price !== undefined && (
                    <div>
                      <h3 className="text-sm font-medium mb-1">Price</h3>
                      <p className="text-sm">${appointment.price.toFixed(2)}</p>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {appointment.clientPreferences && (
                <div>
                  <h3 className="text-sm font-medium mb-1">Notes</h3>
                  <p className="text-sm">{appointment.clientPreferences}</p>
                </div>
              )}

              {appointment.status === 'cancelled' && appointment.cancelReason && (
                <div className="rounded-md bg-destructive/10 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <XCircle className="h-5 w-5 text-destructive" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-destructive">Cancellation Reason</h3>
                      <div className="mt-2 text-sm">
                        <p>{appointment.cancelReason}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Add to Your Calendar</CardTitle>
              <CardDescription>
                Add this appointment to your personal calendar to ensure you don't miss it
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Button
                  variant="outline"
                  className="w-full py-4 text-xs hover:bg-[#4285F4] hover:text-white hover:border-[#4285F4] transition-all"
                  onClick={() => {
                    const startTime = appointment.startTime.toDate().toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
                    const endTime = appointment.endTime.toDate().toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

                    const eventDetails = {
                      text: `${appointment.serviceName} - Appointment`,
                      dates: `${startTime}/${endTime}`,
                      details: `Your appointment for ${appointment.serviceName}`,
                      location: '',
                    };

                    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(eventDetails.text)}&dates=${eventDetails.dates}&details=${encodeURIComponent(eventDetails.details)}&location=${encodeURIComponent(eventDetails.location)}`;
                    window.open(googleCalendarUrl, '_blank');
                  }}
                >
                  <div className="flex flex-col items-center justify-center w-full">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-1">
                      <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19a2 2 0 0 0 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" fill="currentColor"/>
                    </svg>
                    <span className="text-xs font-medium">Google</span>
                  </div>
                </Button>

                <Button
                  variant="outline"
                  className="w-full py-4 text-xs hover:bg-[#0078D4] hover:text-white hover:border-[#0078D4] transition-all"
                  onClick={() => {
                    const startTime = appointment.startTime.toDate().toISOString();
                    const endTime = appointment.endTime.toDate().toISOString();

                    const eventDetails = {
                      subject: `${appointment.serviceName} - Appointment`,
                      start: startTime,
                      end: endTime,
                      body: `Your appointment for ${appointment.serviceName}`,
                      location: '',
                    };

                    const outlookCalendarUrl = `https://outlook.live.com/calendar/0/deeplink/compose?subject=${encodeURIComponent(eventDetails.subject)}&startdt=${encodeURIComponent(eventDetails.start)}&enddt=${encodeURIComponent(eventDetails.end)}&body=${encodeURIComponent(eventDetails.body)}&location=${encodeURIComponent(eventDetails.location)}`;
                    window.open(outlookCalendarUrl, '_blank');
                  }}
                >
                  <div className="flex flex-col items-center justify-center w-full">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-1">
                      <path d="M7 6v3H4V6h3m1-1H3v5h5V5m3 0v5h5V5h-5m4 1v3h-3V6h3M7 13v3H4v-3h3m1-1H3v5h5v-5m3 1v3h3v-3h-3m-1-1h5v5h-5v-5z" fill="currentColor"/>
                    </svg>
                    <span className="text-xs font-medium">Outlook</span>
                  </div>
                </Button>

                <Button
                  variant="outline"
                  className="w-full py-4 text-xs hover:bg-[#6633CC] hover:text-white hover:border-[#6633CC] transition-all"
                  onClick={() => {
                    const startTime = appointment.startTime.toDate().toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
                    const endTime = appointment.endTime.toDate().toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

                    const eventDetails = {
                      title: `${appointment.serviceName} - Appointment`,
                      st: startTime,
                      et: endTime,
                      desc: `Your appointment for ${appointment.serviceName}`,
                      in_loc: '',
                    };

                    const yahooCalendarUrl = `https://calendar.yahoo.com/?v=60&view=d&type=20&title=${encodeURIComponent(eventDetails.title)}&st=${eventDetails.st}&et=${eventDetails.et}&desc=${encodeURIComponent(eventDetails.desc)}&in_loc=${encodeURIComponent(eventDetails.in_loc)}`;
                    window.open(yahooCalendarUrl, '_blank');
                  }}
                >
                  <div className="flex flex-col items-center justify-center w-full">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-1">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
                    </svg>
                    <span className="text-xs font-medium">Yahoo</span>
                  </div>
                </Button>

                <Button
                  variant="outline"
                  className="w-full py-4 text-xs hover:bg-[#007AFF] hover:text-white hover:border-[#007AFF] transition-all"
                  onClick={handleDownloadICS}
                >
                  <div className="flex flex-col items-center justify-center w-full">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mb-1">
                      <path d="M17.5 3C19.43 3 21 4.57 21 6.5V17.5C21 19.43 19.43 21 17.5 21H6.5C4.57 21 3 19.43 3 17.5V6.5C3 4.57 4.57 3 6.5 3H17.5ZM17.5 5H6.5C5.67 5 5 5.67 5 6.5V17.5C5 18.33 5.67 19 6.5 19H17.5C18.33 19 19 18.33 19 17.5V6.5C19 5.67 18.33 5 17.5 5ZM16 7V9H8V7H16ZM14 11V13H8V11H14ZM12 15V17H8V15H12Z" fill="currentColor"/>
                    </svg>
                    <span className="text-xs font-medium">Apple/.ics</span>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Important Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="rounded-md bg-muted p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertCircle className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium">Appointment Guidelines</h3>
                      <div className="mt-2 text-sm text-muted-foreground">
                        <p>
                          Please arrive 10 minutes before your scheduled appointment time.
                          If you need to cancel or reschedule, please do so at least 24 hours in advance.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">What to Expect</h3>
                  <p className="text-sm text-muted-foreground">
                    Your appointment has been confirmed. You will receive a reminder email 24 hours before your appointment.
                    If you have any questions or need to make changes, please contact us.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
