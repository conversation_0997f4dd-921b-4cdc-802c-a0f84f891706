'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getAppointmentById } from '@/lib/services/appointmentService';
import { getBusiness } from '@/lib/services/businessService';
import { sendAppointmentRescheduleEmailWithICS } from '@/lib/services/emailService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CustomDatePicker } from '@/components/ui/custom-date-picker';
import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  Clock,
  Loader2,
  User,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { format, isBefore, startOfDay } from 'date-fns';
import { Timestamp, doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { toast, Toaster } from 'sonner';

export default function PublicReschedulePage({
  params
}: {
  params: Promise<{ appointmentId: string }>
}) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { appointmentId } = React.use(params);

  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [availableSlots, setAvailableSlots] = useState<Array<{time: string, formattedTime: string}>>([]);
  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [isRescheduling, setIsRescheduling] = useState(false);
  const [businessData, setBusinessData] = useState<any>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Fetch appointment - no authentication required
  const {
    data: appointment,
    isLoading: isLoadingAppointment,
    error: appointmentError
  } = useQuery({
    queryKey: ['appointment', appointmentId],
    queryFn: () => getAppointmentById(appointmentId),
    enabled: !!appointmentId
  });

  // Fetch business data when appointment is loaded
  useEffect(() => {
    if (appointment?.businessId) {
      console.log('🏢 Loading business data for:', appointment.businessId);
      getBusiness(appointment.businessId)
        .then((data) => {
          console.log('🏢 Business data loaded:', data);
          setBusinessData(data);
        })
        .catch((error) => {
          console.error('❌ Error loading business data:', error);
        });
    }
  }, [appointment?.businessId]);

  // Reschedule mutation
  const rescheduleMutation = useMutation({
    mutationFn: async ({ newStartTime, newEndTime, durationMinutes }: { newStartTime: Date; newEndTime: Date; durationMinutes: number }) => {
      try {
        console.log('🔄 Starting reschedule mutation...');

        if (!appointment) {
          throw new Error('Appointment data not found');
        }

        if (!businessData) {
          throw new Error('Business data not found');
        }

        // Validate the new times
        if (isNaN(newStartTime.getTime()) || isNaN(newEndTime.getTime())) {
          throw new Error('Invalid date/time values provided');
        }

        if (newStartTime >= newEndTime) {
          throw new Error('Start time must be before end time');
        }

        console.log('📝 Updating appointment in database...');
        console.log('New start time:', newStartTime.toISOString());
        console.log('New end time:', newEndTime.toISOString());

        // Update the appointment with new times
        const appointmentRef = doc(db, 'appointments', appointmentId);
        await updateDoc(appointmentRef, {
          startTime: Timestamp.fromDate(newStartTime),
          endTime: Timestamp.fromDate(newEndTime),
          updatedAt: Timestamp.now()
        });

        console.log('✅ Database updated successfully');

        // Send email notification about the reschedule
        try {
          const updatedAppointment = {
            ...appointment,
            startTime: Timestamp.fromDate(newStartTime),
            endTime: Timestamp.fromDate(newEndTime),
            durationMinutes: durationMinutes,
            id: appointmentId // Ensure ID is included
          };

          console.log('📧 Sending reschedule notification email...');
          console.log('📧 Email details:', {
            clientEmail: appointment.clientEmail,
            businessName: businessData.name,
            appointmentId: appointmentId
          });
          console.log('Updated appointment times:', {
            originalStart: appointment.startTime,
            originalEnd: appointment.endTime,
            newStart: newStartTime,
            newEnd: newEndTime,
            duration: durationMinutes
          });

          await sendAppointmentRescheduleEmailWithICS(
            updatedAppointment,
            businessData.name || 'Business',
            businessData.timeZone || 'UTC',
            businessData.address,
            true // Send to all parties
          );

          console.log('✅ Reschedule notification email sent successfully');
          return { success: true, emailSent: true };
        } catch (emailError) {
          console.error('❌ Error sending reschedule notification email:', emailError);
          // Don't fail the reschedule if email fails, but show a warning
          return { success: true, emailSent: false, emailError: emailError instanceof Error ? emailError.message : 'Unknown email error' };
        }
      } catch (error) {
        console.error('❌ Reschedule mutation failed:', error);
        throw new Error(error instanceof Error ? error.message : 'Failed to reschedule appointment');
      }
    },
    onSuccess: (result) => {
      console.log('✅ Reschedule mutation completed:', result);
      queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
      setIsRescheduling(false);

      if (result.emailSent) {
        toast.success('🎉 Appointment rescheduled successfully!', {
          description: 'Confirmation email with updated calendar file has been sent.',
          duration: 5000
        });
      } else {
        toast.success('✅ Appointment rescheduled successfully!', {
          description: 'Your appointment time has been updated.',
          duration: 5000
        });
        if (result.emailError) {
          toast.warning(`⚠️ Email notification failed: ${result.emailError}`, {
            duration: 7000
          });
        }
      }

      // Redirect after a short delay to let user see the success message
      setTimeout(() => {
        router.push(`/appointment/${appointmentId}`);
      }, 2000);
    },
    onError: (error: any) => {
      console.error('❌ Reschedule mutation error:', error);
      setIsRescheduling(false);
      const errorMessage = error?.message || error?.toString() || 'Unknown error occurred';
      toast.error('❌ Failed to reschedule appointment', {
        description: errorMessage,
        duration: 7000
      });
    }
  });

  // Load available time slots when date is selected - FAST VERSION
  const loadAvailableSlots = async (date: Date) => {
    console.log('🕐 Loading available slots for date:', date);

    if (!appointment) {
      console.log('❌ Missing appointment data');
      return;
    }

    setIsLoadingSlots(true);
    try {
      // Use simple fallback approach for speed
      console.log('🚀 Using fast slot generation...');
      const slots = generateFallbackTimeSlots(date);
      console.log('✅ Generated slots:', slots.length);

      const formattedSlots = slots.map(slot => ({
        time: format(slot.time, 'HH:mm'),
        formattedTime: format(slot.time, 'h:mm a')
      }));

      console.log('✅ Formatted slots:', formattedSlots);
      setAvailableSlots(formattedSlots);
    } catch (error) {
      console.error('❌ Error loading available slots:', error);
      toast.error('Failed to load available times');
      setAvailableSlots([]);
    } finally {
      setIsLoadingSlots(false);
    }
  };

  // Fast function to generate time slots for reschedule
  const generateFallbackTimeSlots = (date: Date) => {
    const slots = [];
    const now = new Date();

    // Business hours: 8 AM to 6 PM
    const startHour = 8;
    const endHour = 18;
    const intervalMinutes = 30;

    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += intervalMinutes) {
        const slotTime = new Date(date);
        slotTime.setHours(hour, minute, 0, 0);

        // Skip past times (only if it's today)
        const isToday = date.toDateString() === now.toDateString();
        if (isToday && slotTime <= now) continue;

        // Skip lunch hour (12:00 PM - 1:00 PM)
        if (hour === 12) continue;

        slots.push({
          time: slotTime,
          formattedTime: format(slotTime, 'h:mm a'),
          isAvailable: true
        });
      }
    }

    console.log(`Generated ${slots.length} time slots for ${date.toDateString()}`);
    return slots;
  };

  // Load slots when date changes
  useEffect(() => {
    if (selectedDate && businessData) {
      loadAvailableSlots(selectedDate);
    }
    // Reset confirmation when date changes
    setShowConfirmation(false);
  }, [selectedDate, businessData, appointment]);

  // Reset confirmation when time changes
  useEffect(() => {
    setShowConfirmation(false);
  }, [selectedTime]);

  // Handle reschedule submission
  const handleReschedule = () => {
    if (!selectedDate || !selectedTime || !appointment) {
      toast.error('Please select a date and time');
      return;
    }

    try {
      console.log('🔄 Creating new appointment times...');
      console.log('Selected date:', selectedDate);
      console.log('Selected time:', selectedTime);
      console.log('Full appointment object:', appointment);
      // console.log('Duration from appointment:', appointment.durationMinutes);
      console.log('All appointment keys:', Object.keys(appointment));

      // Get duration from appointment, with fallback
      // Calculate duration from startTime and endTime if available
      let durationMinutes = 60;
      if (appointment.startTime && appointment.endTime) {
        const start = appointment.startTime instanceof Timestamp
          ? appointment.startTime.toDate()
          : new Date(appointment.startTime);
        const end = appointment.endTime instanceof Timestamp
          ? appointment.endTime.toDate()
          : new Date(appointment.endTime);
        durationMinutes = Math.round((end.getTime() - start.getTime()) / 60000);
      }

      // If duration is still invalid, use a reasonable default
      if (!durationMinutes || durationMinutes <= 0) {
        console.warn('No valid duration found, using 60 minutes default');
        durationMinutes = 60;
      }

      console.log('Using duration:', durationMinutes, 'minutes');

      const [hours, minutes] = selectedTime.split(':').map(Number);

      // Validate time components
      if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        throw new Error('Invalid time format');
      }

      // Create new start time
      const newStartTime = new Date(selectedDate.getTime()); // Clone the date
      newStartTime.setHours(hours, minutes, 0, 0);

      // Validate the created date
      if (isNaN(newStartTime.getTime())) {
        throw new Error('Invalid start time created');
      }

      // Create new end time using simple approach
      console.log('Creating end time with duration:', durationMinutes, 'minutes');

      const newEndTime = new Date(newStartTime);
      newEndTime.setMinutes(newEndTime.getMinutes() + durationMinutes);

      console.log('End time created:', {
        startTime: newStartTime.toISOString(),
        endTime: newEndTime.toISOString(),
        duration: durationMinutes
      });

      // Validate both times
      if (isNaN(newStartTime.getTime()) || isNaN(newEndTime.getTime())) {
        throw new Error('Invalid date/time values');
      }

      console.log('✅ New times created:', {
        newStartTime: newStartTime.toISOString(),
        newEndTime: newEndTime.toISOString()
      });

      setIsRescheduling(true);
      rescheduleMutation.mutate({ newStartTime, newEndTime, durationMinutes });
    } catch (error) {
      console.error('❌ Error creating new appointment times:', error);
      toast.error('Invalid date or time selected. Please try again.');
    }
  };

  // Format date
  const formatDate = (timestamp: Timestamp) => {
    return format(timestamp.toDate(), 'EEEE, MMMM d, yyyy');
  };

  // Format time
  const formatTime = (timestamp: Timestamp) => {
    return format(timestamp.toDate(), 'h:mm a');
  };

  // Get appointment status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Completed</Badge>;
      case 'scheduled':
        return <Badge variant="outline" className="flex items-center"><Calendar className="h-3 w-3 mr-1" /> Scheduled</Badge>;
      case 'confirmed':
        return <Badge variant="default" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Confirmed</Badge>;
      case 'cancelled':
        return <Badge variant="destructive" className="flex items-center"><XCircle className="h-3 w-3 mr-1" /> Cancelled</Badge>;
      case 'no-show':
        return <Badge variant="destructive" className="flex items-center"><AlertCircle className="h-3 w-3 mr-1" /> No-show</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoadingAppointment) {
    return (
      <div className="flex justify-center items-center py-32">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (appointmentError || !appointment) {
    return (
      <div className="min-h-screen flex justify-center items-center p-4 bg-background">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-destructive mb-4" />
            <h3 className="text-lg font-medium">Appointment Not Found</h3>
            <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
              {appointmentError
                ? (appointmentError as Error).message
                : "We couldn't find the appointment you're looking for."}
            </p>
            <Button asChild>
              <Link href="/">Go Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check if appointment can be rescheduled
  const canReschedule = appointment &&
    (appointment.status === 'scheduled' || appointment.status === 'confirmed');

  if (!canReschedule) {
    return (
      <div className="min-h-screen bg-background py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            <div className="flex items-center gap-4">
              <Button variant="ghost" asChild>
                <Link href={`/appointment/${appointmentId}`}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Appointment
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-bold">Reschedule Appointment</h1>
                <p className="text-sm text-muted-foreground">Modify your appointment time</p>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-amber-500" />
                  Cannot Reschedule
                </CardTitle>
                <CardDescription>
                  This appointment cannot be rescheduled due to its current status.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <h3 className="font-medium">{appointment.serviceName}</h3>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(appointment.startTime)} at {formatTime(appointment.startTime)}
                    </p>
                  </div>
                  {getStatusBadge(appointment.status)}
                </div>

                <div className="text-sm text-muted-foreground">
                  <p>
                    Only appointments with "Scheduled" or "Confirmed" status can be rescheduled.
                    If you need to make changes to this appointment, please contact us directly.
                  </p>
                </div>

                <div className="flex gap-2">
                  <Button asChild>
                    <Link href={`/appointment/${appointmentId}`}>
                      View Appointment Details
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        <Toaster />
      </div>
    );
  }

  return (
    <div className="py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" asChild>
              <Link href={`/appointment/${appointmentId}`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Appointment
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Reschedule Appointment</h1>
              <p className="text-sm text-muted-foreground">Select a new time for your appointment</p>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Current Appointment</CardTitle>
              <CardDescription>Your current appointment details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                <div className="space-y-2">
                  <h3 className="font-medium">{appointment.serviceName}</h3>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(appointment.startTime)}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{formatTime(appointment.startTime)} - {formatTime(appointment.endTime)}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <User className="h-4 w-4" />
                    <span>{appointment.staffName || 'Any available staff'}</span>
                  </div>
                </div>
                {getStatusBadge(appointment.status)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Select New Date & Time</CardTitle>
              <CardDescription>
                Choose a new date and time for your appointment. Available times are shown from 8:00 AM to 6:00 PM (excluding lunch hour).
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="date">Select Date</Label>
                  <CustomDatePicker
                    date={selectedDate}
                    onDateChange={setSelectedDate}
                    placeholder="Pick a new date"
                    disabled={(date: Date) => isBefore(date, startOfDay(new Date()))}
                    align="start"
                    closeOnSelect={true}
                  />
                </div>

                {selectedDate && (
                  <div>
                    <Label htmlFor="time">Select Time</Label>
                    {isLoadingSlots ? (
                      <div className="flex items-center gap-2 py-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm text-muted-foreground">Loading available times...</span>
                      </div>
                    ) : availableSlots.length > 0 ? (
                      <Select value={selectedTime} onValueChange={setSelectedTime}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a time" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableSlots.map((slot) => (
                            <SelectItem key={slot.time} value={slot.time}>
                              {slot.formattedTime}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="text-sm text-muted-foreground py-2">
                        No available times on this date. Please select a different date.
                      </div>
                    )}
                  </div>
                )}
              </div>

              {selectedDate && selectedTime && !showConfirmation && (
                <div className="flex gap-2">
                  <Button
                    onClick={() => setShowConfirmation(true)}
                    disabled={!selectedDate || !selectedTime}
                  >
                    Preview Reschedule
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href={`/appointment/${appointmentId}`}>
                      Cancel
                    </Link>
                  </Button>
                </div>
              )}

              {showConfirmation && selectedDate && selectedTime && (
                <div className="space-y-4">
                  <div className="rounded-md bg-green-50 dark:bg-green-950 p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <CheckCircle className="h-5 w-5 text-green-400" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-green-800 dark:text-green-200">Ready to Reschedule?</h3>
                        <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                          <p>
                            <strong>New time:</strong> {format(selectedDate, 'EEEE, MMMM d, yyyy')} at{' '}
                            {availableSlots.find(slot => slot.time === selectedTime)?.formattedTime}
                          </p>
                          <p className="mt-2">
                            <strong>Email notification will be sent to:</strong> {appointment?.clientEmail || 'client'}
                          </p>
                          <p className="mt-1 text-xs">
                            Click "Confirm Reschedule" to update your appointment and send the notification.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      onClick={handleReschedule}
                      disabled={isRescheduling}
                    >
                      {isRescheduling && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Confirm Reschedule
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setShowConfirmation(false)}
                      disabled={isRescheduling}
                    >
                      Back to Selection
                    </Button>
                    <Button variant="outline" asChild>
                      <Link href={`/appointment/${appointmentId}`}>
                        Cancel
                      </Link>
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
