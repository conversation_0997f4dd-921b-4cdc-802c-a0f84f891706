'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar, Clock, Users, Globe, CheckCircle, Smartphone, ArrowRight, Menu, X, Star, Shield, Zap, Mail, Phone, Instagram, Twitter } from 'lucide-react';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { Logo } from '@/components/ui/Logo';
import { useAuth } from '@/components/providers/AuthProvider';
import {
  COMPANY_NAME,
  COMPANY_TAGLINE,
  CONTACT_EMAIL,
  CONTACT_PHONE,
  TWITTER_URL,
  INSTAGRAM_URL,
  //FACEBOOK_URL,
  //LINKEDIN_URL
} from '@/lib/constants';

export default function HomePage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, isStaff } = useAuth();

  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <header className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Logo size="md" />
            <span className="font-bold text-xl">Onpointly</span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-6">
            <Link href="#features" className="text-sm font-medium hover:text-primary transition-colors">
              Features
            </Link>
            <Link href="#pricing" className="text-sm font-medium hover:text-primary transition-colors">
              Pricing
            </Link>
            <Link href="#testimonials" className="text-sm font-medium hover:text-primary transition-colors">
              Testimonials
            </Link>
            {!user && (
              <Link href="/auth/login" className="text-sm font-medium hover:text-primary transition-colors">
                Login
              </Link>
            )}
            {user && (
              <Link
                href={isStaff ? "/staff/dashboard" : "/dashboard"}
                className="text-sm font-medium text-primary hover:text-primary/80 transition-colors"
              >
                Dashboard
              </Link>
            )}
          </nav>

          {/* Desktop CTA */}
          <div className="hidden md:flex items-center gap-4">
            <ThemeToggle />
            {!user ? (
              <Button asChild>
                <Link href="/auth/register">Get Started</Link>
              </Button>
            ) : (
              <Button variant="outline" asChild>
                <Link href={isStaff ? "/staff/dashboard" : "/dashboard"}>
                  Go to Dashboard
                </Link>
              </Button>
            )}
          </div>

          {/* Mobile Actions */}
          <div className="md:hidden flex items-center gap-1">
            <ThemeToggle />
            <button
              className="p-2 rounded-md hover:bg-muted transition-colors"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              aria-label="Toggle menu"
            >
              {mobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden py-4 px-4 bg-background border-t">
            <nav className="flex flex-col space-y-4">
              <Link
                href="#features"
                className="text-sm font-medium py-2 hover:text-primary transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Features
              </Link>
              <Link
                href="#pricing"
                className="text-sm font-medium py-2 hover:text-primary transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Pricing
              </Link>
              <Link
                href="#testimonials"
                className="text-sm font-medium py-2 hover:text-primary transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Testimonials
              </Link>

              {!user ? (
                <>
                  <Link
                    href="/auth/login"
                    className="text-sm font-medium py-2 hover:text-primary transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Login
                  </Link>
                  <Button className="w-full mt-4" asChild>
                    <Link href="/auth/register">Get Started</Link>
                  </Button>
                </>
              ) : (
                <>
                  <Link
                    href={isStaff ? "/staff/dashboard" : "/dashboard"}
                    className="text-sm font-medium py-2 text-primary hover:text-primary/80 transition-colors"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Dashboard
                  </Link>
                  <Button className="w-full mt-4" variant="outline" asChild>
                    <Link href={isStaff ? "/staff/dashboard" : "/dashboard"}>
                      Go to Dashboard
                    </Link>
                  </Button>
                </>
              )}
            </nav>
          </div>
        )}
      </header>

      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-16 md:py-24 lg:py-32 bg-gradient-to-b from-muted/50 to-background overflow-hidden">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="text-center lg:text-left">
                <div className="flex flex-col items-center lg:items-start mb-6">
                  <Logo size="xl" className="mb-4" />
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium">
                    <Star className="h-4 w-4 mr-1" /> Trusted by 1000+ businesses
                  </div>
                </div>
                <h1 className="text-4xl font-bold tracking-tight mb-6 sm:text-5xl md:text-6xl lg:text-7xl">
                  Effortless <span className="text-primary">Scheduling</span> for Your Business
                </h1>
                <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto lg:mx-0">
                  Focus on your clients, not your calendar. Onpointly provides simple, customizable appointment scheduling that integrates seamlessly with your workflow.
                </p>
                <div className="flex flex-col sm:flex-row justify-center lg:justify-start gap-4">
                  <Button size="lg" className="rounded-full" asChild>
                    <Link href="/auth/register">Start Free Trial</Link>
                  </Button>
                  <Button size="lg" variant="outline" className="rounded-full" asChild>
                    <Link href="#features">See How It Works</Link>
                  </Button>
                </div>
                <div className="mt-8 flex flex-wrap justify-center lg:justify-start gap-6">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="text-sm">No credit card required</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="text-sm">14-day free trial</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="text-sm">Cancel anytime</span>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="absolute -z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[200%] aspect-square bg-gradient-to-r from-primary/20 to-purple-500/20 rounded-full blur-3xl opacity-30"></div>
                <div className="bg-gradient-to-br from-background to-muted border rounded-2xl shadow-xl overflow-hidden max-w-xl mx-auto lg:mx-0 lg:ml-auto">
                  <div className="p-4 bg-muted/80 border-b flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Logo size="sm" />
                      <span className="font-medium">Onpointly Dashboard</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full bg-red-500"></div>
                      <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                      <div className="h-3 w-3 rounded-full bg-green-500"></div>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div className="bg-primary/5 rounded-lg p-4 text-center">
                        <h3 className="text-2xl font-bold text-primary">24</h3>
                        <p className="text-xs text-muted-foreground">Appointments Today</p>
                      </div>
                      <div className="bg-primary/5 rounded-lg p-4 text-center">
                        <h3 className="text-2xl font-bold text-primary">87%</h3>
                        <p className="text-xs text-muted-foreground">Booking Rate</p>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 p-3 bg-background rounded-lg border">
                        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                          <Clock className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate">Haircut & Styling</p>
                          <p className="text-xs text-muted-foreground">Today, 10:00 AM - 11:00 AM</p>
                        </div>
                        <div className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs font-medium">
                          Confirmed
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-background rounded-lg border">
                        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                          <Clock className="h-5 w-5 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium truncate">Massage Therapy</p>
                          <p className="text-xs text-muted-foreground">Today, 2:00 PM - 3:30 PM</p>
                        </div>
                        <div className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs font-medium">
                          Pending
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 md:py-28 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
                <Zap className="h-4 w-4 mr-1" /> Powerful Features
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Everything You Need to Grow Your Business</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Streamline your appointment scheduling and focus on what matters most - your clients and your business.
              </p>
            </div>

            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              <div className="bg-card p-6 rounded-xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300">
                <div className="h-14 w-14 rounded-xl bg-primary/10 flex items-center justify-center mb-5">
                  <Calendar className="h-7 w-7 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Smart Scheduling</h3>
                <p className="text-muted-foreground">
                  Intelligent appointment scheduling with customizable availability, buffer times, and automatic time zone detection.
                </p>
                <div className="mt-4 pt-4 border-t">
                  <Link href="#" className="text-primary text-sm font-medium inline-flex items-center hover:underline">
                    Learn more <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>

              <div className="bg-card p-6 rounded-xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300">
                <div className="h-14 w-14 rounded-xl bg-primary/10 flex items-center justify-center mb-5">
                  <Users className="h-7 w-7 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Team Management</h3>
                <p className="text-muted-foreground">
                  Manage your entire team's schedule with role-based permissions, service assignments, and round-robin booking.
                </p>
                <div className="mt-4 pt-4 border-t">
                  <Link href="#" className="text-primary text-sm font-medium inline-flex items-center hover:underline">
                    Learn more <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>

              <div className="bg-card p-6 rounded-xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300">
                <div className="h-14 w-14 rounded-xl bg-primary/10 flex items-center justify-center mb-5">
                  <Globe className="h-7 w-7 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Calendar Integration</h3>
                <p className="text-muted-foreground">
                  Seamless integration with Google Calendar to prevent double-booking and keep all your appointments in sync.
                </p>
                <div className="mt-4 pt-4 border-t">
                  <Link href="#" className="text-primary text-sm font-medium inline-flex items-center hover:underline">
                    Learn more <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>

              <div className="bg-card p-6 rounded-xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300">
                <div className="h-14 w-14 rounded-xl bg-primary/10 flex items-center justify-center mb-5">
                  <Smartphone className="h-7 w-7 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Mobile Friendly</h3>
                <p className="text-muted-foreground">
                  Fully responsive design works on any device, allowing you and your clients to manage appointments on the go.
                </p>
                <div className="mt-4 pt-4 border-t">
                  <Link href="#" className="text-primary text-sm font-medium inline-flex items-center hover:underline">
                    Learn more <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>

              <div className="bg-card p-6 rounded-xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300">
                <div className="h-14 w-14 rounded-xl bg-primary/10 flex items-center justify-center mb-5">
                  <Clock className="h-7 w-7 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Automated Reminders</h3>
                <p className="text-muted-foreground">
                  Reduce no-shows with automated email and SMS reminders sent to clients before their appointments.
                </p>
                <div className="mt-4 pt-4 border-t">
                  <Link href="#" className="text-primary text-sm font-medium inline-flex items-center hover:underline">
                    Learn more <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>

              <div className="bg-card p-6 rounded-xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300">
                <div className="h-14 w-14 rounded-xl bg-primary/10 flex items-center justify-center mb-5">
                  <Shield className="h-7 w-7 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-3">Custom Branding</h3>
                <p className="text-muted-foreground">
                  Personalize your booking page with your logo, colors, and branding to provide a seamless client experience.
                </p>
                <div className="mt-4 pt-4 border-t">
                  <Link href="#" className="text-primary text-sm font-medium inline-flex items-center hover:underline">
                    Learn more <ArrowRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-20 md:py-28">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
                <Shield className="h-4 w-4 mr-1" /> Simple Pricing
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Choose the Perfect Plan for Your Business</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                All plans include a 14-day free trial with no credit card required. Cancel anytime.
              </p>
              {/* Testing Mode Notice */}
              <div className="mt-8 mb-4 inline-block px-6 py-3 rounded-lg bg-yellow-100 border border-yellow-300 text-yellow-900 font-medium text-base shadow-sm">
                <strong>Testing Mode:</strong> Onpointly is currently in open beta! We are accepting businesses for free in exchange for your feedback. If you run a business and want to help shape the future of Onpointly, sign up and let us know your thoughts!
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {/* Starter Plan */}
              <div className="bg-card p-8 rounded-2xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300 flex flex-col">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold">Starter</h3>
                  <p className="text-sm text-muted-foreground mt-2">Perfect for solo entrepreneurs</p>
                  <div className="mt-4 flex items-baseline">
                    <span className="text-4xl font-bold">Free</span>
                    <span className="text-muted-foreground ml-1">(Beta)</span>
                  </div>
                </div>
                <ul className="space-y-4 mb-8 flex-1">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>1 Staff Member</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Unlimited Appointments</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Email Reminders</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Google Calendar Integration</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>All Beta Features</span>
                  </li>
                </ul>
                <Button className="w-full rounded-full" variant="outline" asChild>
                  <Link href="/auth/register?plan=starter">Join Beta</Link>
                </Button>
              </div>

              {/* Professional Plan */}
              <div className="bg-card p-8 rounded-2xl shadow-md border border-primary relative flex flex-col transform scale-105 md:scale-110 z-10">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-4 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </div>
                <div className="mb-6">
                  <h3 className="text-xl font-semibold">Professional</h3>
                  <p className="text-sm text-muted-foreground mt-2">For growing teams and businesses</p>
                  <div className="mt-4 flex items-baseline">
                    <span className="text-4xl font-bold">Free</span>
                    <span className="text-muted-foreground ml-1">(Beta)</span>
                  </div>
                </div>
                <ul className="space-y-4 mb-8 flex-1">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Up to 5 Staff Members</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Unlimited Appointments</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Email & SMS Reminders</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Calendar Integrations</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Custom Branding (Beta)</span>
                  </li>
                </ul>
                <Button className="w-full rounded-full" asChild>
                  <Link href="/auth/register?plan=professional">Join Beta</Link>
                </Button>
              </div>

              {/* Business Plan */}
              <div className="bg-card p-8 rounded-2xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300 flex flex-col">
                <div className="mb-6">
                  <h3 className="text-xl font-semibold">Business</h3>
                  <p className="text-sm text-muted-foreground mt-2">For established and larger teams</p>
                  <div className="mt-4 flex items-baseline">
                    <span className="text-4xl font-bold">Free</span>
                    <span className="text-muted-foreground ml-1">(Beta)</span>
                  </div>
                </div>
                <ul className="space-y-4 mb-8 flex-1">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Unlimited Staff Members</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Unlimited Appointments</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>All Beta Features</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Direct Feedback Channel</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                    <span>Help Shape Future Features</span>
                  </li>
                </ul>
                <Button className="w-full rounded-full" variant="outline" asChild>
                  <Link href="/auth/register?plan=business">Join Beta</Link>
                </Button>
              </div>
            </div>
            <div className="mt-6 text-center text-muted-foreground text-sm">
              <span>Feature set is evolving. Beta users get early access and can help shape the platform.</span>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="py-20 md:py-28 bg-muted/30 overflow-hidden">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-4">
                <Star className="h-4 w-4 mr-1" /> Testimonials
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Customers Say</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Businesses of all sizes trust Onpointly to manage their appointments and grow their client base.
              </p>
            </div>

            {/* Testimonial Cards */}
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8 relative">
              {/* Background Decoration */}
              <div className="absolute -z-10 top-1/2 left-1/4 w-72 h-72 bg-primary/5 rounded-full blur-3xl"></div>
              <div className="absolute -z-10 bottom-0 right-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl"></div>

              {/* Testimonial 1 */}
              <div className="bg-card p-8 rounded-2xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300">
                <div className="flex items-center gap-1 text-amber-400 mb-6">
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                </div>
                <p className="text-foreground mb-6 italic">
                  "Onpointly has transformed how we manage our salon. Our clients love the easy booking process, and we've seen a significant reduction in no-shows."
                </p>
                <div className="flex items-center">
                  <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="font-medium text-primary">JD</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">John Doe</h4>
                    <p className="text-sm text-muted-foreground">Hair Salon Owner</p>
                  </div>
                </div>
              </div>

              {/* Testimonial 2 */}
              <div className="bg-card p-8 rounded-2xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300 lg:translate-y-8">
                <div className="flex items-center gap-1 text-amber-400 mb-6">
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                </div>
                <p className="text-foreground mb-6 italic">
                  "The calendar integration is seamless, and the automated reminders have cut our no-show rate by 60%. Worth every penny! I can't imagine running my practice without it now."
                </p>
                <div className="flex items-center">
                  <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="font-medium text-primary">JS</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Jane Smith</h4>
                    <p className="text-sm text-muted-foreground">Dental Practice</p>
                  </div>
                </div>
              </div>

              {/* Testimonial 3 */}
              <div className="bg-card p-8 rounded-2xl shadow-sm border border-muted hover:border-primary/20 hover:shadow-md transition-all duration-300">
                <div className="flex items-center gap-1 text-amber-400 mb-6">
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                </div>
                <p className="text-foreground mb-6 italic">
                  "Managing multiple trainers' schedules used to be a nightmare. With Onpointly, it's all automated and our clients can book with their preferred trainer instantly."
                </p>
                <div className="flex items-center">
                  <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="font-medium text-primary">RJ</span>
                  </div>
                  <div>
                    <h4 className="font-semibold">Robert Johnson</h4>
                    <p className="text-sm text-muted-foreground">Fitness Studio</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Brands Section */}
            <div className="mt-20">
              <p className="text-center text-sm text-muted-foreground mb-8">TRUSTED BY BUSINESSES WORLDWIDE</p>
              <div className="flex flex-wrap justify-center items-center gap-8 md:gap-16 opacity-70">
                <div className="h-8 w-24 bg-muted/50 rounded-md"></div>
                <div className="h-8 w-28 bg-muted/50 rounded-md"></div>
                <div className="h-8 w-20 bg-muted/50 rounded-md"></div>
                <div className="h-8 w-32 bg-muted/50 rounded-md"></div>
                <div className="h-8 w-24 bg-muted/50 rounded-md"></div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 md:py-28 overflow-hidden dark:bg-[#13151e] bg-[#f8fafc] dark:text-white text-[#13151e]">
          <div className="container mx-auto px-4 relative">
            {/* Background Elements */}
            <div className="absolute -top-24 -right-24 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-32 -left-32 w-96 h-96 bg-primary/10 rounded-full blur-3xl"></div>
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full max-w-4xl max-h-96 bg-gradient-to-r from-primary/10 to-primary/5 rounded-full blur-3xl opacity-20"></div>

            {/* Content */}
            <div className="relative z-10 flex flex-col lg:flex-row items-center justify-between gap-12">
              <div className="text-center lg:text-left max-w-2xl">
                <div className="inline-flex items-center px-2 py-1 rounded-full dark:bg-white/5 bg-black/5 dark:text-white/80 text-black/80 text-xs font-medium mb-6">
                  <Star className="h-3 w-3 mr-1" /> Trusted by 1000+ businesses
                </div>
                <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                  Ready to Streamline Your Scheduling?
                </h2>
                <p className="text-xl md:text-2xl mb-8 opacity-90 font-light">
                  Join thousands of businesses that trust Onpointly for their appointment scheduling needs.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <Button size="lg" className="rounded-full text-sm px-6 py-2 h-auto font-medium dark:bg-white bg-primary dark:text-[#13151e] text-white hover:opacity-90 transition-all duration-300" asChild>
                    <Link href="/auth/register" className="flex items-center">
                      Get Started Today
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                  <Button size="lg" variant="outline" className="rounded-full text-sm px-6 py-2 h-auto font-medium bg-transparent dark:border-white/20 border-black/20 dark:text-white text-[#13151e] dark:hover:bg-white/5 hover:bg-black/5 transition-all duration-300" asChild>
                    <Link href="#features">Learn More</Link>
                  </Button>
                </div>
                <div className="mt-6 flex flex-wrap justify-center lg:justify-start gap-6">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                    <span className="text-xs dark:text-white/80 text-black/80">14-day free trial</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                    <span className="text-xs dark:text-white/80 text-black/80">No credit card required</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                    <span className="text-xs dark:text-white/80 text-black/80">Cancel anytime</span>
                  </div>
                </div>
              </div>

              {/* Mockup */}
              <div className="hidden lg:block relative">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/30 to-primary/20 rounded-xl blur opacity-20"></div>
                <div className="relative w-80 dark:bg-[#1e2130] bg-white rounded-xl dark:border-white/10 border-black/10 border p-4 shadow-xl">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="h-10 w-10 rounded-full dark:bg-white/5 bg-black/5 flex items-center justify-center">
                      <Logo size="sm" />
                    </div>
                    <div>
                      <h3 className="font-medium dark:text-white text-black text-sm">Start scheduling today</h3>
                      <p className="text-xs dark:text-white/60 text-black/60">No credit card required</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-2 dark:bg-[#13151e] bg-gray-50 rounded-lg dark:border-white/5 border-black/5 border">
                      <div className="h-8 w-8 rounded-full dark:bg-white/5 bg-black/5 flex items-center justify-center flex-shrink-0">
                        <Clock className="h-4 w-4 dark:text-white/70 text-black/70" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium dark:text-white text-black text-xs truncate">Haircut & Styling</p>
                        <p className="text-[10px] dark:text-white/50 text-black/50">Today, 10:00 AM - 11:00 AM</p>
                      </div>
                      <div className="px-2 py-0.5 dark:bg-green-600/20 bg-green-100 dark:border-green-600/30 border-green-300 border dark:text-green-400 text-green-700 rounded text-[10px] font-medium">
                        Confirmed
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-2 dark:bg-[#13151e] bg-gray-50 rounded-lg dark:border-white/5 border-black/5 border">
                      <div className="h-8 w-8 rounded-full dark:bg-white/5 bg-black/5 flex items-center justify-center flex-shrink-0">
                        <Clock className="h-4 w-4 dark:text-white/70 text-black/70" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium dark:text-white text-black text-xs truncate">Massage Therapy</p>
                        <p className="text-[10px] dark:text-white/50 text-black/50">Tomorrow, 2:00 PM</p>
                      </div>
                      <div className="px-2 py-0.5 dark:bg-yellow-600/20 bg-yellow-100 dark:border-yellow-600/30 border-yellow-300 border dark:text-yellow-400 text-yellow-700 rounded text-[10px] font-medium">
                        Upcoming
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-muted/50 border-t py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8 md:gap-12">
            <div className="col-span-2 lg:col-span-2">
              <div className="flex items-center gap-2 mb-4">
                <Logo size="md" />
                <span className="font-bold text-xl">{COMPANY_NAME}</span>
              </div>
              <p className="text-muted-foreground mb-4 max-w-xs">
                Simplifying appointment scheduling for businesses worldwide. Boost your efficiency and client satisfaction.
              </p>
              <p className="text-muted-foreground text-sm">
                <span className="block">Dixon Rd, Etobicoke, ON</span>
                <span className="block mt-1">{CONTACT_EMAIL}</span>
              </p>
              <div className="flex items-center gap-4 mt-6">
                {/* Facebook and LinkedIn not available for now */}
                <Link href={TWITTER_URL} className="text-muted-foreground hover:text-primary transition-colors">
                  <Twitter className="h-5 w-5" />
                </Link>
                <Link href={INSTAGRAM_URL} className="text-muted-foreground hover:text-primary transition-colors">
                  <Instagram className="h-5 w-5" />
                </Link>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-foreground">Product</h4>
              <ul className="space-y-3 text-sm">
                <li><Link href="#features" className="text-muted-foreground hover:text-primary transition-colors">Features</Link></li>
                <li><Link href="#pricing" className="text-muted-foreground hover:text-primary transition-colors">Pricing</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-primary transition-colors">Integrations</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-primary transition-colors">FAQ</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-foreground">Company</h4>
              <ul className="space-y-3 text-sm">
                <li><Link href="#" className="text-muted-foreground hover:text-primary transition-colors">About Us</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-primary transition-colors">Blog</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-primary transition-colors">Careers</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-primary transition-colors">Contact</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4 text-foreground">Legal</h4>
              <ul className="space-y-3 text-sm">
                <li><Link href="/privacy-policy" className="text-muted-foreground hover:text-primary transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="text-muted-foreground hover:text-primary transition-colors">Terms of Service</Link></li>
                <li><Link href="/privacy-policy#cookies" className="text-muted-foreground hover:text-primary transition-colors">Cookie Policy</Link></li>
                <li><Link href="/privacy-policy#gdpr" className="text-muted-foreground hover:text-primary transition-colors">GDPR</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-muted-foreground">© {new Date().getFullYear()} Onpointly. All rights reserved.</p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Link href="/privacy-policy" className="text-xs text-muted-foreground hover:text-primary transition-colors">Privacy</Link>
              <Link href="/terms" className="text-xs text-muted-foreground hover:text-primary transition-colors">Terms</Link>
              <Link href="/privacy-policy#cookies" className="text-xs text-muted-foreground hover:text-primary transition-colors">Cookies</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
