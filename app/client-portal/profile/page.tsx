// 'use client';

// import React, { useState, useEffect } from 'react';
// import { useRouter } from 'next/navigation';
// import Link from 'next/link';
// import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// import { useAuth } from '@/components/providers/AuthProvider';
// import { getClientByEmail, updateClient } from '@/lib/services/clientService';
// import { collection, query, getDocs } from 'firebase/firestore';
// import { db } from '@/lib/firebase/config';
// import { getClientCategories } from '@/lib/services/clientService';
// import { getStaff } from '@/lib/services/staffService';
// import { getServices } from '@/lib/services/serviceService';
// import { Button } from '@/components/ui/button';
// import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import { Label } from '@/components/ui/label';
// import { Textarea } from '@/components/ui/textarea';
// import { Checkbox } from '@/components/ui/checkbox';
// import { Separator } from '@/components/ui/separator';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// import { Badge } from '@/components/ui/badge';
// import {
//   AlertCircle,
//   Calendar,
//   Loader2,
//   Save,
//   User,
//   Mail,
//   Phone,
//   MapPin,
//   Tag
// } from 'lucide-react';
// import { toast } from 'sonner';
// import { format } from 'date-fns';
// import { Timestamp } from 'firebase/firestore';

// export default function ClientProfilePage() {
//   const router = useRouter();
//   const { user } = useAuth();
//   const queryClient = useQueryClient();

//   // Form state
//   const [formData, setFormData] = useState({
//     name: '',
//     email: '',
//     phone: '',
//     address: '',
//     birthday: '',
//     notes: '',
//     preferredStaffIds: [] as string[],
//     preferredServices: [] as string[],
//     communicationPreferences: {
//       email: true,
//       sms: false,
//       push: false
//     },
//     tags: [] as string[]
//   });

//   const [newTag, setNewTag] = useState('');
//   const [isUpdating, setIsUpdating] = useState(false);

//   // Fetch client data
//   const {
//     data: client,
//     isLoading: isLoadingClient,
//     error: clientError
//   } = useQuery({
//     queryKey: ['clientByEmail', user?.email],
//     queryFn: async () => {
//       // Since we don't know the businessId yet, we need to search across all businesses
//       const allBusinessesQuery = query(collection(db, 'businesses'));
//       const businessesSnapshot = await getDocs(allBusinessesQuery);

//       for (const businessDoc of businessesSnapshot.docs) {
//         const businessId = businessDoc.id;
//         const client = await getClientByEmail(businessId, user?.email || '');
//         if (client) return client;
//       }

//       return null;
//     },
//     enabled: !!user?.email
//   });

//   // Fetch categories
//   const {
//     data: categories,
//     isLoading: isLoadingCategories
//   } = useQuery({
//     queryKey: ['clientCategories', client?.businessId],
//     queryFn: () => getClientCategories(client?.businessId || ''),
//     enabled: !!client?.businessId
//   });

//   // Fetch staff
//   const {
//     data: staff,
//     isLoading: isLoadingStaff
//   } = useQuery({
//     queryKey: ['staff', client?.businessId],
//     queryFn: () => getStaff(client?.businessId || ''),
//     enabled: !!client?.businessId
//   });

//   // Fetch services
//   const {
//     data: services,
//     isLoading: isLoadingServices
//   } = useQuery({
//     queryKey: ['services', client?.businessId],
//     queryFn: () => getServices(client?.businessId || ''),
//     enabled: !!client?.businessId
//   });

//   // Update client mutation
//   const updateClientMutation = useMutation({
//     mutationFn: (data: { clientId: string; clientData: Partial<any> }) => {
//       return updateClient(data.clientId, data.clientData);
//     },
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: ['clientByEmail', user?.email] });
//       toast.success('Profile updated successfully');
//       setIsUpdating(false);
//     },
//     onError: (error: Error) => {
//       toast.error(`Error updating profile: ${error.message}`);
//       setIsUpdating(false);
//     }
//   });

//   // Initialize form data when client data is loaded
//   useEffect(() => {
//     if (client) {
//       setFormData({
//         name: client.name || '',
//         email: client.email || '',
//         phone: client.phone || '',
//         address: client.address || '',
//         birthday: client.birthday
//           ? format(client.birthday.toDate(), 'yyyy-MM-dd')
//           : '',
//         notes: client.notes || '',
//         preferredStaffIds: client.preferredStaffIds || [],
//         preferredServices: client.preferredServices || [],
//         communicationPreferences: client.communicationPreferences || {
//           email: true,
//           sms: false,
//           push: false
//         },
//         tags: client.tags || []
//       });
//     }
//   }, [client]);

//   // Handle form input change
//   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
//     const { name, value } = e.target;
//     setFormData(prev => ({
//       ...prev,
//       [name]: value
//     }));
//   };

//   // Handle communication preference toggle
//   const handleCommunicationToggle = (type: 'email' | 'sms' | 'push', checked: boolean) => {
//     setFormData(prev => ({
//       ...prev,
//       communicationPreferences: {
//         ...prev.communicationPreferences,
//         [type]: checked
//       }
//     }));
//   };

//   // Handle staff preference toggle
//   const handleStaffToggle = (staffId: string, checked: boolean) => {
//     setFormData(prev => {
//       if (checked) {
//         return {
//           ...prev,
//           preferredStaffIds: [...prev.preferredStaffIds, staffId]
//         };
//       } else {
//         return {
//           ...prev,
//           preferredStaffIds: prev.preferredStaffIds.filter(id => id !== staffId)
//         };
//       }
//     });
//   };

//   // Handle service preference toggle
//   const handleServiceToggle = (serviceId: string, checked: boolean) => {
//     setFormData(prev => {
//       if (checked) {
//         return {
//           ...prev,
//           preferredServices: [...prev.preferredServices, serviceId]
//         };
//       } else {
//         return {
//           ...prev,
//           preferredServices: prev.preferredServices.filter(id => id !== serviceId)
//         };
//       }
//     });
//   };

//   // Handle adding a tag
//   const handleAddTag = () => {
//     if (!newTag.trim()) return;

//     setFormData(prev => ({
//       ...prev,
//       tags: [...prev.tags, newTag.trim()]
//     }));

//     setNewTag('');
//   };

//   // Handle removing a tag
//   const handleRemoveTag = (tag: string) => {
//     setFormData(prev => ({
//       ...prev,
//       tags: prev.tags.filter(t => t !== tag)
//     }));
//   };

//   // Handle form submission
//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();

//     if (!client) return;

//     setIsUpdating(true);

//     // Convert birthday string to Timestamp if provided
//     const clientData = {
//       ...formData,
//       birthday: formData.birthday
//         ? Timestamp.fromDate(new Date(formData.birthday))
//         : undefined
//     };

//     updateClientMutation.mutate({
//       clientId: client.id,
//       clientData: clientData
//     });
//   };

//   const isLoading = isLoadingClient || isLoadingCategories || isLoadingStaff || isLoadingServices;

//   if (isLoading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <Loader2 className="h-8 w-8 animate-spin text-primary" />
//       </div>
//     );
//   }

//   if (clientError || !client) {
//     return (
//       <Card>
//         <CardContent className="flex flex-col items-center justify-center py-12">
//           <AlertCircle className="h-12 w-12 text-destructive mb-4" />
//           <h3 className="text-lg font-medium">Error Loading Profile</h3>
//           <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
//             {clientError
//               ? (clientError as Error).message
//               : "We couldn't find your client profile. Please contact support for assistance."}
//           </p>
//           <Button asChild>
//             <Link href="/client-portal">Return to Dashboard</Link>
//           </Button>
//         </CardContent>
//       </Card>
//     );
//   }

//   return (
//     <div className="space-y-6">
//       <div>
//         <h1 className="text-2xl md:text-3xl font-bold tracking-tight">My Profile</h1>
//         <p className="text-muted-foreground">
//           View and update your profile information
//         </p>
//       </div>

//       <Tabs defaultValue="personal" className="space-y-4">
//         <TabsList>
//           <TabsTrigger value="personal">Personal Information</TabsTrigger>
//           <TabsTrigger value="preferences">Preferences</TabsTrigger>
//           <TabsTrigger value="communication">Communication</TabsTrigger>
//         </TabsList>

//         <form onSubmit={handleSubmit}>
//           <TabsContent value="personal" className="space-y-4">
//             <Card>
//               <CardHeader>
//                 <CardTitle>Personal Information</CardTitle>
//                 <CardDescription>
//                   Update your personal details
//                 </CardDescription>
//               </CardHeader>
//               <CardContent className="space-y-4">
//                 <div className="grid gap-4 sm:grid-cols-2">
//                   <div className="space-y-2">
//                     <Label htmlFor="name">Full Name</Label>
//                     <Input
//                       id="name"
//                       name="name"
//                       value={formData.name}
//                       onChange={handleInputChange}
//                       required
//                     />
//                   </div>

//                   <div className="space-y-2">
//                     <Label htmlFor="email">Email Address</Label>
//                     <Input
//                       id="email"
//                       name="email"
//                       type="email"
//                       value={formData.email}
//                       onChange={handleInputChange}
//                       required
//                       disabled
//                     />
//                     <p className="text-xs text-muted-foreground">
//                       Email cannot be changed. Contact support for assistance.
//                     </p>
//                   </div>

//                   <div className="space-y-2">
//                     <Label htmlFor="phone">Phone Number</Label>
//                     <Input
//                       id="phone"
//                       name="phone"
//                       value={formData.phone}
//                       onChange={handleInputChange}
//                     />
//                   </div>

//                   <div className="space-y-2">
//                     <Label htmlFor="birthday">Birthday</Label>
//                     <Input
//                       id="birthday"
//                       name="birthday"
//                       type="date"
//                       value={formData.birthday}
//                       onChange={handleInputChange}
//                     />
//                   </div>

//                   <div className="space-y-2 sm:col-span-2">
//                     <Label htmlFor="address">Address</Label>
//                     <Textarea
//                       id="address"
//                       name="address"
//                       value={formData.address}
//                       onChange={handleInputChange}
//                       rows={3}
//                     />
//                   </div>

//                   <div className="space-y-2 sm:col-span-2">
//                     <Label htmlFor="notes">Notes (Only visible to you)</Label>
//                     <Textarea
//                       id="notes"
//                       name="notes"
//                       value={formData.notes}
//                       onChange={handleInputChange}
//                       rows={3}
//                       placeholder="Add any notes or special requests for your appointments"
//                     />
//                   </div>
//                 </div>

//                 <div className="space-y-2">
//                   <Label>Tags</Label>
//                   <div className="flex items-center gap-2">
//                     <Input
//                       placeholder="Add a tag..."
//                       value={newTag}
//                       onChange={(e) => setNewTag(e.target.value)}
//                       onKeyDown={(e) => {
//                         if (e.key === 'Enter') {
//                           e.preventDefault();
//                           handleAddTag();
//                         }
//                       }}
//                     />
//                     <Button
//                       type="button"
//                       variant="outline"
//                       onClick={handleAddTag}
//                       disabled={!newTag.trim()}
//                     >
//                       Add
//                     </Button>
//                   </div>

//                   {formData.tags.length > 0 && (
//                     <div className="flex flex-wrap gap-1 mt-2">
//                       {formData.tags.map((tag) => (
//                         <div
//                           key={tag}
//                           className="flex items-center gap-1 bg-muted text-muted-foreground px-2 py-1 rounded-md text-sm"
//                         >
//                           <Tag className="h-3 w-3 mr-1" />
//                           {tag}
//                           <button
//                             type="button"
//                             className="text-muted-foreground hover:text-foreground"
//                             onClick={() => handleRemoveTag(tag)}
//                           >
//                             &times;
//                           </button>
//                         </div>
//                       ))}
//                     </div>
//                   )}
//                 </div>
//               </CardContent>
//             </Card>
//           </TabsContent>

//           <TabsContent value="preferences" className="space-y-4">
//             <Card>
//               <CardHeader>
//                 <CardTitle>Preferences</CardTitle>
//                 <CardDescription>
//                   Set your preferences for appointments
//                 </CardDescription>
//               </CardHeader>
//               <CardContent className="space-y-6">
//                 <div className="space-y-4">
//                   <h3 className="text-sm font-medium">Preferred Staff</h3>
//                   <div className="grid gap-2 sm:grid-cols-2">
//                     {staff?.map((staffMember) => (
//                       <div key={staffMember.id} className="flex items-center space-x-2">
//                         <Checkbox
//                           id={`staff-${staffMember.id}`}
//                           checked={formData.preferredStaffIds.includes(staffMember.id)}
//                           onCheckedChange={(checked) =>
//                             handleStaffToggle(staffMember.id, !!checked)
//                           }
//                         />
//                         <Label htmlFor={`staff-${staffMember.id}`} className="font-normal">
//                           {staffMember.name}
//                         </Label>
//                       </div>
//                     ))}
//                     {(!staff || staff.length === 0) && (
//                       <div className="text-sm text-muted-foreground">
//                         No staff members available.
//                       </div>
//                     )}
//                   </div>
//                 </div>

//                 <Separator />

//                 <div className="space-y-4">
//                   <h3 className="text-sm font-medium">Preferred Services</h3>
//                   <div className="grid gap-2 sm:grid-cols-2">
//                     {services?.map((service) => (
//                       <div key={service.id} className="flex items-center space-x-2">
//                         <Checkbox
//                           id={`service-${service.id}`}
//                           checked={formData.preferredServices.includes(service.id)}
//                           onCheckedChange={(checked) =>
//                             handleServiceToggle(service.id, !!checked)
//                           }
//                         />
//                         <Label htmlFor={`service-${service.id}`} className="font-normal">
//                           {service.name}
//                         </Label>
//                       </div>
//                     ))}
//                     {(!services || services.length === 0) && (
//                       <div className="text-sm text-muted-foreground">
//                         No services available.
//                       </div>
//                     )}
//                   </div>
//                 </div>
//               </CardContent>
//             </Card>
//           </TabsContent>

//           <TabsContent value="communication" className="space-y-4">
//             <Card>
//               <CardHeader>
//                 <CardTitle>Communication Preferences</CardTitle>
//                 <CardDescription>
//                   Choose how you want to receive notifications
//                 </CardDescription>
//               </CardHeader>
//               <CardContent className="space-y-4">
//                 <div className="space-y-4">
//                   <div className="flex items-center space-x-2">
//                     <Checkbox
//                       id="comm-email"
//                       checked={formData.communicationPreferences.email}
//                       onCheckedChange={(checked) =>
//                         handleCommunicationToggle('email', !!checked)
//                       }
//                     />
//                     <Label htmlFor="comm-email" className="font-normal">
//                       Email Notifications
//                     </Label>
//                   </div>
//                   <div className="flex items-center space-x-2">
//                     <Checkbox
//                       id="comm-sms"
//                       checked={formData.communicationPreferences.sms}
//                       onCheckedChange={(checked) =>
//                         handleCommunicationToggle('sms', !!checked)
//                       }
//                     />
//                     <Label htmlFor="comm-sms" className="font-normal">
//                       SMS Notifications
//                     </Label>
//                   </div>
//                   <div className="flex items-center space-x-2">
//                     <Checkbox
//                       id="comm-push"
//                       checked={formData.communicationPreferences.push}
//                       onCheckedChange={(checked) =>
//                         handleCommunicationToggle('push', !!checked)
//                       }
//                     />
//                     <Label htmlFor="comm-push" className="font-normal">
//                       Push Notifications
//                     </Label>
//                   </div>
//                 </div>

//                 <div className="rounded-md bg-muted p-4">
//                   <div className="flex">
//                     <div className="flex-shrink-0">
//                       <AlertCircle className="h-5 w-5 text-muted-foreground" />
//                     </div>
//                     <div className="ml-3">
//                       <h3 className="text-sm font-medium">Important Note</h3>
//                       <div className="mt-2 text-sm text-muted-foreground">
//                         <p>
//                           You will always receive important notifications about your appointments,
//                           such as confirmations and reminders, via email. Other notification methods
//                           are optional.
//                         </p>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               </CardContent>
//             </Card>
//           </TabsContent>

//           <div className="mt-6 flex justify-end gap-4">
//             <Button
//               type="button"
//               variant="outline"
//               onClick={() => router.push('/client-portal')}
//             >
//               Cancel
//             </Button>
//             <Button
//               type="submit"
//               disabled={isUpdating}
//             >
//               {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
//               <Save className="mr-2 h-4 w-4" />
//               Save Changes
//             </Button>
//           </div>
//         </form>
//       </Tabs>
//     </div>
//   );
// }
