'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/components/providers/AuthProvider';
import { getClientByEmail } from '@/lib/services/clientService';
import { collection, query, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { getServices } from '@/lib/services/serviceService';
import { getStaff } from '@/lib/services/staffService';
import { getBusinessHours, getBusinessById } from '@/lib/services/businessService';
import { getAvailableTimeSlots, createAppointment } from '@/lib/services/appointmentService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  Check,
  Clock,
  Loader2,
  User,
  RefreshCw
} from 'lucide-react';
import { format, addMinutes, parse, isAfter, isBefore, addDays } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import { toast } from 'sonner';

export default function BookAppointmentPage() {
  const router = useRouter();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Form state
  const [selectedServiceId, setSelectedServiceId] = useState<string>('');
  const [selectedStaffId, setSelectedStaffId] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [showConfirmation, setShowConfirmation] = useState<boolean>(false);

  // Fetch client data
  const {
    data: client,
    isLoading: isLoadingClient,
    error: clientError
  } = useQuery({
    queryKey: ['clientByEmail', user?.email],
    queryFn: async () => {
      // Since we don't know the businessId yet, we need to search across all businesses
      const allBusinessesQuery = query(collection(db, 'businesses'));
      const businessesSnapshot = await getDocs(allBusinessesQuery);

      for (const businessDoc of businessesSnapshot.docs) {
        const businessId = businessDoc.id;
        const client = await getClientByEmail(businessId, user?.email || '');
        if (client) return client;
      }

      return null;
    },
    enabled: !!user?.email
  });

  // Fetch business data
  const {
    data: business,
    isLoading: isLoadingBusiness
  } = useQuery({
    queryKey: ['business', client?.businessId],
    queryFn: () => getBusinessById(client?.businessId || ''),
    enabled: !!client?.businessId
  });

  // Fetch services
  const {
    data: services,
    isLoading: isLoadingServices
  } = useQuery({
    queryKey: ['services', client?.businessId],
    queryFn: () => getServices(client?.businessId || ''),
    enabled: !!client?.businessId
  });

  // Fetch staff
  const {
    data: staff,
    isLoading: isLoadingStaff
  } = useQuery({
    queryKey: ['staff', client?.businessId],
    queryFn: () => getStaff(client?.businessId || ''),
    enabled: !!client?.businessId
  });

  // Fetch business hours
  const {
    data: businessHours,
    isLoading: isLoadingBusinessHours
  } = useQuery({
    queryKey: ['businessHours', client?.businessId],
    queryFn: () => getBusinessHours(client?.businessId || ''),
    enabled: !!client?.businessId
  });

  // Get selected service
  const selectedService = services?.find(service => service.id === selectedServiceId);

  // Get selected staff
  const selectedStaffMember = staff?.find(staffMember => staffMember.id === selectedStaffId);

  // Get staff who can perform the selected service
  const eligibleStaff = staff?.filter(staffMember =>
    staffMember.services.includes(selectedServiceId)
  );

  // Fetch available time slots
  const {
    data: availableTimeSlots,
    isLoading: isLoadingTimeSlots,
    refetch: refetchTimeSlots
  } = useQuery({
    queryKey: ['availableTimeSlots', client?.businessId, selectedServiceId, selectedStaffId, selectedDate],
    queryFn: () => {
      if (!client?.businessId || !selectedServiceId || !selectedDate) return [];

      const date = parse(selectedDate, 'yyyy-MM-dd', new Date());

      return getAvailableTimeSlots(
        client.businessId,
        selectedServiceId,
        date,
        selectedStaffId || undefined
      );
    },
    enabled: !!client?.businessId && !!selectedServiceId && !!selectedDate
  });

  // Create appointment mutation
  const createAppointmentMutation = useMutation({
    mutationFn: (appointmentData: any) => createAppointment(appointmentData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clientAppointments', client?.id] });
      setShowConfirmation(true);
      setIsSubmitting(false);
    },
    onError: (error: Error) => {
      toast.error(`Error booking appointment: ${error.message}`);
      setIsSubmitting(false);
    }
  });

  // Generate available dates (next 30 days)
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 0; i < 30; i++) {
      const date = addDays(today, i);
      const dateString = format(date, 'yyyy-MM-dd');
      const dayOfWeek = format(date, 'EEEE');

      dates.push({
        date: dateString,
        dayOfWeek,
        displayDate: format(date, 'EEEE, MMMM d, yyyy')
      });
    }

    return dates;
  };

  // Handle service selection
  const handleServiceSelect = (serviceId: string) => {
    setSelectedServiceId(serviceId);
    setSelectedStaffId('');
    setSelectedDate('');
    setSelectedTime('');
  };

  // Handle staff selection
  const handleStaffSelect = (staffId: string) => {
    setSelectedStaffId(staffId);
    setSelectedDate('');
    setSelectedTime('');
  };

  // Handle date selection
  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    setSelectedTime('');
  };

  // Handle time selection
  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!client || !selectedServiceId || !selectedDate || !selectedTime) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);

    // Parse date and time
    const date = parse(selectedDate, 'yyyy-MM-dd', new Date());
    const time = parse(selectedTime, 'HH:mm', new Date());

    // Combine date and time
    const startTime = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      time.getHours(),
      time.getMinutes()
    );

    // Calculate end time based on service duration
    const endTime = addMinutes(startTime, selectedService?.durationMinutes || 60);

    // Create appointment data
    const appointmentData = {
      businessId: client.businessId,
      clientId: client.id,
      clientName: client.name,
      clientEmail: client.email,
      clientPhone: client.phone,
      serviceId: selectedServiceId,
      serviceName: selectedService?.name,
      staffId: selectedStaffId || null,
      staffName: selectedStaffMember?.name || null,
      startTime: Timestamp.fromDate(startTime),
      endTime: Timestamp.fromDate(endTime),
      status: 'scheduled',
      price: selectedService?.price || 0,
      clientPreferences: notes || null,
      useRoundRobin: !selectedStaffId
    };

    // Create appointment
    createAppointmentMutation.mutate(appointmentData);
  };

  // Handle confirmation close
  const handleConfirmationClose = () => {
    setShowConfirmation(false);
    router.push('/client-portal/appointments');
  };

  const isLoading =
    isLoadingClient ||
    isLoadingBusiness ||
    isLoadingServices ||
    isLoadingStaff ||
    isLoadingBusinessHours;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (clientError || !client) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Error Loading Profile</h3>
          <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
            {clientError
              ? (clientError as Error).message
              : "We couldn't find your client profile. Please contact support for assistance."}
          </p>
          <Button asChild>
            <Link href="/client-portal">Return to Dashboard</Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => router.back()} className="gap-1">
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Book Appointment</h1>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 md:grid-cols-2">
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Select Service</CardTitle>
              <CardDescription>
                Choose the service you want to book
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {services?.map((service) => (
                  <div
                    key={service.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedServiceId === service.id
                        ? 'border-primary bg-primary/5'
                        : 'hover:border-primary/50'
                    }`}
                    onClick={() => handleServiceSelect(service.id)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{service.name}</h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          {service.durationMinutes} minutes
                        </p>
                      </div>
                      {selectedServiceId === service.id && (
                        <div className="rounded-full bg-primary text-primary-foreground p-1">
                          <Check className="h-4 w-4" />
                        </div>
                      )}
                    </div>
                    <p className="text-sm mt-2">{service.description}</p>
                    <p className="font-medium mt-2">${service.price.toFixed(2)}</p>
                  </div>
                ))}
                {(!services || services.length === 0) && (
                  <div className="md:col-span-2 lg:col-span-3 text-center py-8">
                    <p className="text-muted-foreground">No services available</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {selectedServiceId && (
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Select Staff</CardTitle>
                <CardDescription>
                  Choose a staff member or select "Any Available Staff" for automatic assignment
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup
                  value={selectedStaffId}
                  onValueChange={handleStaffSelect}
                  className="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
                >
                  <div
                    className={`flex items-center space-x-2 border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedStaffId === ''
                        ? 'border-primary bg-primary/5'
                        : 'hover:border-primary/50'
                    }`}
                    onClick={() => handleStaffSelect('')}
                  >
                    <RadioGroupItem value="" id="staff-any" className="sr-only" />
                    <Label htmlFor="staff-any" className="flex-1 cursor-pointer">
                      <div className="flex items-center gap-2">
                        <User className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Any Available Staff</div>
                          <div className="text-sm text-muted-foreground">
                            Automatically assign the best available staff member
                          </div>
                        </div>
                      </div>
                    </Label>
                  </div>

                  {eligibleStaff?.map((staffMember) => (
                    <div
                      key={staffMember.id}
                      className={`flex items-center space-x-2 border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedStaffId === staffMember.id
                          ? 'border-primary bg-primary/5'
                          : 'hover:border-primary/50'
                      }`}
                      onClick={() => handleStaffSelect(staffMember.id)}
                    >
                      <RadioGroupItem
                        value={staffMember.id}
                        id={`staff-${staffMember.id}`}
                        className="sr-only"
                      />
                      <Label htmlFor={`staff-${staffMember.id}`} className="flex-1 cursor-pointer">
                        <div className="flex items-center gap-2">
                          <User className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{staffMember.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {staffMember.role}
                            </div>
                          </div>
                        </div>
                      </Label>
                    </div>
                  ))}

                  {(!eligibleStaff || eligibleStaff.length === 0) && (
                    <div className="md:col-span-2 lg:col-span-3 text-center py-4">
                      <p className="text-muted-foreground">
                        No staff members available for this service
                      </p>
                    </div>
                  )}
                </RadioGroup>
              </CardContent>
            </Card>
          )}

          {selectedServiceId && (
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Select Date & Time</CardTitle>
                <CardDescription>
                  Choose a date and time for your appointment
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="date" className="space-y-4">
                  <TabsList>
                    <TabsTrigger value="date">
                      <Calendar className="h-4 w-4 mr-2" />
                      Date
                    </TabsTrigger>
                    <TabsTrigger value="time" disabled={!selectedDate}>
                      <Clock className="h-4 w-4 mr-2" />
                      Time
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="date" className="space-y-4">
                    <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                      {getAvailableDates().map((dateObj) => (
                        <div
                          key={dateObj.date}
                          className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                            selectedDate === dateObj.date
                              ? 'border-primary bg-primary/5'
                              : 'hover:border-primary/50'
                          }`}
                          onClick={() => handleDateSelect(dateObj.date)}
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="text-sm text-muted-foreground">
                                {dateObj.dayOfWeek}
                              </div>
                              <div className="font-medium">
                                {format(parse(dateObj.date, 'yyyy-MM-dd', new Date()), 'MMMM d, yyyy')}
                              </div>
                            </div>
                            {selectedDate === dateObj.date && (
                              <div className="rounded-full bg-primary text-primary-foreground p-1">
                                <Check className="h-4 w-4" />
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="time" className="space-y-4">
                    {isLoadingTimeSlots ? (
                      <div className="flex justify-center items-center h-32">
                        <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      </div>
                    ) : availableTimeSlots && availableTimeSlots.length > 0 ? (
                      <div className="grid gap-2 md:grid-cols-3 lg:grid-cols-4">
                        {availableTimeSlots.map((slot) => (
                          <div
                            key={slot}
                            className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                              selectedTime === slot
                                ? 'border-primary bg-primary/5'
                                : 'hover:border-primary/50'
                            }`}
                            onClick={() => handleTimeSelect(slot)}
                          >
                            <div className="flex justify-between items-center">
                              <div className="font-medium">
                                {format(parse(slot, 'HH:mm', new Date()), 'h:mm a')}
                              </div>
                              {selectedTime === slot && (
                                <div className="rounded-full bg-primary text-primary-foreground p-1">
                                  <Check className="h-3 w-3" />
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground">
                          No available time slots for the selected date
                        </p>
                        <Button
                          variant="outline"
                          className="mt-4"
                          onClick={() => refetchTimeSlots()}
                        >
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Refresh
                        </Button>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}

          {selectedServiceId && selectedDate && selectedTime && (
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Additional Information</CardTitle>
                <CardDescription>
                  Add any special requests or notes for your appointment
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes (Optional)</Label>
                    <Textarea
                      id="notes"
                      placeholder="Add any special requests or notes for your appointment..."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      rows={3}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {selectedServiceId && selectedDate && selectedTime && (
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Appointment Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="text-sm font-medium">Service</h3>
                      <p>{selectedService?.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {selectedService?.durationMinutes} minutes - ${selectedService?.price.toFixed(2)}
                      </p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium">Staff</h3>
                      <p>{selectedStaffMember?.name || 'Any Available Staff'}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium">Date & Time</h3>
                      <p>
                        {format(parse(selectedDate, 'yyyy-MM-dd', new Date()), 'EEEE, MMMM d, yyyy')}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {format(parse(selectedTime, 'HH:mm', new Date()), 'h:mm a')}
                      </p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium">Client</h3>
                      <p>{client.name}</p>
                      <p className="text-sm text-muted-foreground">{client.email}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Book Appointment
                </Button>
              </CardFooter>
            </Card>
          )}
        </div>
      </form>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Appointment Booked!</DialogTitle>
            <DialogDescription>
              Your appointment has been successfully booked.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="rounded-md bg-primary/10 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <Check className="h-5 w-5 text-primary" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium">Booking Confirmed</h3>
                  <div className="mt-2 text-sm">
                    <p>
                      You will receive a confirmation email shortly. You can view and manage your appointment in the appointments section.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Appointment Details</h3>
              <p className="text-sm">
                {selectedService?.name} on {format(parse(selectedDate, 'yyyy-MM-dd', new Date()), 'EEEE, MMMM d, yyyy')} at {format(parse(selectedTime, 'HH:mm', new Date()), 'h:mm a')}
              </p>
              <p className="text-sm">
                With: {selectedStaffMember?.name || 'Any Available Staff'}
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleConfirmationClose}>
              View My Appointments
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
