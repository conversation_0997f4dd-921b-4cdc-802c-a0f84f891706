// 'use client';

// import React, { useState } from 'react';
// import Link from 'next/link';
// import { useQuery } from '@tanstack/react-query';
// import { useAuth } from '@/components/providers/AuthProvider';
// import { getClientByEmail } from '@/lib/services/clientService';
// import { collection, query, getDocs } from 'firebase/firestore';
// import { db } from '@/lib/firebase/config';
// // import { getAppointmentsForClient } from '@/lib/services/appointmentService';
// import { Button } from '@/components/ui/button';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// import { Badge } from '@/components/ui/badge';
// import { Input } from '@/components/ui/input';
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow
// } from '@/components/ui/table';
// import {
//   Calendar,
//   Clock,
//   Loader2,
//   Plus,
//   Search,
//   AlertCircle,
//   CheckCircle,
//   XCircle,
//   Filter
// } from 'lucide-react';
// import { format } from 'date-fns';
// import { Timestamp } from 'firebase/firestore';
// import type { Appointment } from '@/lib/types/db';

// export default function ClientAppointmentsPage() {
//   const { user } = useAuth();
//   const [searchTerm, setSearchTerm] = useState('');
//   const [activeTab, setActiveTab] = useState('upcoming');

//   // Fetch client data
//   const {
//     data: client,
//     isLoading: isLoadingClient,
//     error: clientError
//   } = useQuery({
//     queryKey: ['clientByEmail', user?.email],
//     queryFn: async () => {
//       // Since we don't know the businessId yet, we need to search across all businesses
//       const allBusinessesQuery = query(collection(db, 'businesses'));
//       const businessesSnapshot = await getDocs(allBusinessesQuery);

//       for (const businessDoc of businessesSnapshot.docs) {
//         const businessId = businessDoc.id;
//         const client = await getClientByEmail(businessId, user?.email || '');
//         if (client) return client;
//       }

//       return null;
//     },
//     enabled: !!user?.email
//   });

//   // Fetch appointments
//   const {
//     data: appointments,
//     isLoading: isLoadingAppointments
//   } = useQuery({
//     queryKey: ['clientAppointments', client?.id],
//     queryFn: () => {
//       if (!client?.id || !client?.businessId) return [];
//       return getAppointmentsForClient(client.businessId, client.id);
//     },
//     enabled: !!client?.id && !!client?.businessId
//   });

//   // Format date
//   const formatDate = (timestamp: Timestamp) => {
//     return format(timestamp.toDate(), 'EEEE, MMMM d, yyyy');
//   };

//   // Format time
//   const formatTime = (timestamp: Timestamp) => {
//     return format(timestamp.toDate(), 'h:mm a');
//   };

//   // Get appointment status badge
//   const getStatusBadge = (status: string) => {
//     switch (status) {
//       case 'completed':
//         return <Badge variant="success" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Completed</Badge>;
//       case 'scheduled':
//         return <Badge variant="outline" className="flex items-center"><Calendar className="h-3 w-3 mr-1" /> Scheduled</Badge>;
//       case 'confirmed':
//         return <Badge variant="default" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Confirmed</Badge>;
//       case 'cancelled':
//         return <Badge variant="destructive" className="flex items-center"><XCircle className="h-3 w-3 mr-1" /> Cancelled</Badge>;
//       case 'no-show':
//         return <Badge variant="destructive" className="flex items-center"><AlertCircle className="h-3 w-3 mr-1" /> No-show</Badge>;
//       default:
//         return <Badge variant="secondary">{status}</Badge>;
//     }
//   };

//   // Filter appointments based on tab and search term
//   // ...

//   const filteredAppointments = (appointments as Appointment[] | undefined)?.filter((appointment) => {
//     const matchesSearch =
//       searchTerm === '' ||
//       appointment.serviceName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
//       appointment.staffName?.toLowerCase().includes(searchTerm.toLowerCase());

//     const appointmentTime =
//       appointment.startTime instanceof Date
//         ? appointment.startTime
//         : appointment.startTime.toDate();

//     const now = new Date();

//     if (activeTab === 'upcoming') {
//       return (
//         matchesSearch &&
//         appointmentTime >= now &&
//         (appointment.status === 'scheduled' || appointment.status === 'confirmed')
//       );
//     } else if (activeTab === 'past') {
//       return (
//         matchesSearch &&
//         (appointmentTime < now ||
//           appointment.status === 'completed' ||
//           appointment.status === 'cancelled' ||
//           appointment.status === 'no-show')
//       );
//     } else {
//       return matchesSearch;
//     }
//   }) || [];

//   // Sort appointments by date
//   const sortedAppointments = [...filteredAppointments].sort((a, b) => {
//     const aTime = a.startTime instanceof Date ? a.startTime : a.startTime.toDate();
//     const bTime = b.startTime instanceof Date ? b.startTime : b.startTime.toDate();

//     return activeTab === 'upcoming'
//       ? aTime.getTime() - bTime.getTime() // Ascending for upcoming
//       : bTime.getTime() - aTime.getTime(); // Descending for past
//   });

//   const isLoading = isLoadingClient || isLoadingAppointments;

//   if (isLoading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <Loader2 className="h-8 w-8 animate-spin text-primary" />
//       </div>
//     );
//   }

//   if (clientError || !client) {
//     return (
//       <Card>
//         <CardContent className="flex flex-col items-center justify-center py-12">
//           <AlertCircle className="h-12 w-12 text-destructive mb-4" />
//           <h3 className="text-lg font-medium">Error Loading Profile</h3>
//           <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
//             {clientError
//               ? (clientError as Error).message
//               : "We couldn't find your client profile. Please contact support for assistance."}
//           </p>
//           <Button asChild>
//             <Link href="/auth/login">Return to Login</Link>
//           </Button>
//         </CardContent>
//       </Card>
//     );
//   }

//   return (
//     <div className="space-y-6">
//       <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
//         <div>
//           <h1 className="text-2xl md:text-3xl font-bold tracking-tight">My Appointments</h1>
//           <p className="text-muted-foreground">
//             View and manage your appointments
//           </p>
//         </div>
//         <Button asChild>
//           <Link href="/client-portal/appointments/new">
//             <Plus className="h-4 w-4 mr-2" />
//             Book Appointment
//           </Link>
//         </Button>
//       </div>

//       <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
//         <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
//           <TabsList className="grid w-full grid-cols-3">
//             <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
//             <TabsTrigger value="past">Past</TabsTrigger>
//             <TabsTrigger value="all">All</TabsTrigger>
//           </TabsList>
//         </Tabs>

//         <div className="relative w-full md:w-64">
//           <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
//           <Input
//             type="search"
//             placeholder="Search appointments..."
//             className="pl-8"
//             value={searchTerm}
//             onChange={(e) => setSearchTerm(e.target.value)}
//           />
//         </div>
//       </div>

//       <Card>
//         <CardHeader>
//           <CardTitle>
//             {activeTab === 'upcoming' ? 'Upcoming Appointments' :
//              activeTab === 'past' ? 'Past Appointments' : 'All Appointments'}
//           </CardTitle>
//           <CardDescription>
//             {activeTab === 'upcoming' ? 'Your scheduled appointments' :
//              activeTab === 'past' ? 'Your appointment history' : 'All your appointments'}
//           </CardDescription>
//         </CardHeader>
//         <CardContent>
//           {sortedAppointments.length > 0 ? (
//             <Table>
//               <TableHeader>
//                 <TableRow>
//                   <TableHead>Date & Time</TableHead>
//                   <TableHead>Service</TableHead>
//                   <TableHead>Staff</TableHead>
//                   <TableHead>Status</TableHead>
//                   <TableHead className="text-right">Actions</TableHead>
//                 </TableRow>
//               </TableHeader>
//               <TableBody>
//                 {sortedAppointments.map((appointment) => (
//                   <TableRow key={appointment.id}>
//                     <TableCell>
//                       <div className="font-medium">
//                         {formatDate(appointment.startTime)}
//                       </div>
//                       <div className="text-sm text-muted-foreground">
//                         {formatTime(appointment.startTime)}
//                       </div>
//                     </TableCell>
//                     <TableCell>{appointment.serviceName}</TableCell>
//                     <TableCell>{appointment.staffName || 'Any available staff'}</TableCell>
//                     <TableCell>{getStatusBadge(appointment.status)}</TableCell>
//                     <TableCell className="text-right">
//                       <div className="flex justify-end gap-2">
//                         <Button size="sm" variant="outline" asChild>
//                           <Link href={`/client-portal/appointments/${appointment.id}`}>
//                             View
//                           </Link>
//                         </Button>
//                         {(appointment.status === 'scheduled' || appointment.status === 'confirmed') && (
//                           <Button size="sm" variant="outline" asChild>
//                             <Link href={`/client-portal/appointments/${appointment.id}/reschedule`}>
//                               Reschedule
//                             </Link>
//                           </Button>
//                         )}
//                       </div>
//                     </TableCell>
//                   </TableRow>
//                 ))}
//               </TableBody>
//             </Table>
//           ) : (
//             <div className="flex flex-col items-center justify-center py-12 text-center">
//               <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
//               <h3 className="text-lg font-medium">No appointments found</h3>
//               <p className="text-sm text-muted-foreground mt-1 mb-4">
//                 {activeTab === 'upcoming'
//                   ? "You don't have any upcoming appointments."
//                   : activeTab === 'past'
//                   ? "You don't have any past appointments."
//                   : "You don't have any appointments."}
//               </p>
//               <Button asChild>
//                 <Link href="/client-portal/appointments/new">
//                   <Plus className="h-4 w-4 mr-2" />
//                   Book Appointment
//                 </Link>
//               </Button>
//             </div>
//           )}
//         </CardContent>
//       </Card>
//     </div>
//   );
// }
