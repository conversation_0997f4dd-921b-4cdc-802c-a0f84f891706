'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// Removed auth requirement for public appointment viewing
// import { useAuth } from '@/components/providers/AuthProvider';
// import { getClientByEmail } from '@/lib/services/clientService';
import { collection, query, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { getAppointmentById, cancelAppointment } from '@/lib/services/appointmentService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  Clock,
  Loader2,
  MapPin,
  User,
  XCircle,
  CheckCircle,
  CalendarX,
  Download,
  Star
} from 'lucide-react';
import { format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import { toast } from 'sonner';
import { downloadICSFile, generateICSContent, generateICSFileName } from '@/lib/services/icsService';

export default function AppointmentDetailsPage({
  params
}: {
  params: { appointmentId: string }
}) {
  const router = useRouter();
  // Removed auth requirement for public appointment viewing
  // const { user } = useAuth();
  const queryClient = useQueryClient();
  const { appointmentId } = params;

  const [cancelReason, setCancelReason] = useState('');
  const [isCancelling, setIsCancelling] = useState(false);

  // Removed client data fetching for public appointment viewing
  // This page is now accessible without authentication

  // Fetch appointment
  const {
    data: appointment,
    isLoading: isLoadingAppointment,
    error: appointmentError
  } = useQuery({
    queryKey: ['appointment', appointmentId],
    queryFn: () => getAppointmentById(appointmentId),
    enabled: !!appointmentId
  });

  // Cancel appointment mutation
  const cancelAppointmentMutation = useMutation({
    mutationFn: (data: { appointmentId: string; reason: string }) => {
      return cancelAppointment(data.appointmentId, data.reason);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
      // Removed client-specific query invalidation for public viewing
      toast.success('Appointment cancelled successfully');
      setIsCancelling(false);
      setCancelReason('');
    },
    onError: (error: Error) => {
      toast.error(`Error cancelling appointment: ${error.message}`);
      setIsCancelling(false);
    }
  });

  // Format date
  const formatDate = (timestamp: Timestamp) => {
    return format(timestamp.toDate(), 'EEEE, MMMM d, yyyy');
  };

  // Format time
  const formatTime = (timestamp: Timestamp) => {
    return format(timestamp.toDate(), 'h:mm a');
  };

  // Get appointment status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Completed</Badge>;
      case 'scheduled':
        return <Badge variant="outline" className="flex items-center"><Calendar className="h-3 w-3 mr-1" /> Scheduled</Badge>;
      case 'confirmed':
        return <Badge variant="default" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Confirmed</Badge>;
      case 'cancelled':
        return <Badge variant="destructive" className="flex items-center"><XCircle className="h-3 w-3 mr-1" /> Cancelled</Badge>;
      case 'no-show':
        return <Badge variant="destructive" className="flex items-center"><AlertCircle className="h-3 w-3 mr-1" /> No-show</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  // Handle cancel appointment
  const handleCancelAppointment = () => {
    if (!appointmentId) return;

    setIsCancelling(true);

    cancelAppointmentMutation.mutate({
      appointmentId,
      reason: cancelReason
    });
  };

  // Handle download ICS file
  const handleDownloadICS = () => {
    if (!appointment) return;

    // Get business name from appointment or use a default
    const businessName = 'Business'; // This should be fetched from the business data

    const icsContent = generateICSContent(appointment, businessName);
    const fileName = generateICSFileName(appointment, businessName);
    downloadICSFile(icsContent, fileName);

    toast.success('Calendar file downloaded successfully');
  };

  // Check if appointment can be cancelled
  const canCancel = appointment &&
    (appointment.status === 'scheduled' || appointment.status === 'confirmed');

  // Check if appointment can be rescheduled
  const canReschedule = appointment &&
    (appointment.status === 'scheduled' || appointment.status === 'confirmed');

  const isLoading = isLoadingAppointment; // Removed client loading for public viewing

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (appointmentError || !appointment) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <AlertCircle className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Appointment Not Found</h3>
          <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
            {appointmentError
              ? (appointmentError as Error).message
              : "We couldn't find the appointment you're looking for."}
          </p>
          <Button asChild>
            <Link href="/client-portal/appointments">Back to Appointments</Link>
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Removed client permission check for public appointment viewing
  // This allows clients to view appointments via email links without authentication

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => router.back()} className="gap-1">
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleDownloadICS}>
            <Download className="h-4 w-4 mr-2" />
            Download Calendar File
          </Button>

          {appointment.status === 'completed' && (
            <Button variant="outline" asChild>
              <Link href={`/review/${appointment.id}`}>
                <Star className="h-4 w-4 mr-2" />
                Leave Review
              </Link>
            </Button>
          )}

          {canReschedule && (
            <Button variant="outline" asChild>
              <Link href={`/client-portal/appointments/${appointmentId}/reschedule`}>
                <Calendar className="h-4 w-4 mr-2" />
                Reschedule
              </Link>
            </Button>
          )}

          {canCancel && (
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="destructive">
                  <CalendarX className="h-4 w-4 mr-2" />
                  Cancel Appointment
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Cancel Appointment</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to cancel this appointment? This action cannot be undone.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Appointment Details:</p>
                    <p className="text-sm">
                      {appointment.serviceName} on {formatDate(appointment.startTime)} at {formatTime(appointment.startTime)}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Reason for cancellation (optional):</p>
                    <Textarea
                      value={cancelReason}
                      onChange={(e) => setCancelReason(e.target.value)}
                      placeholder="Please provide a reason for cancellation..."
                      rows={3}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setCancelReason('')}>
                    Keep Appointment
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleCancelAppointment}
                    disabled={isCancelling}
                  >
                    {isCancelling && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Cancel Appointment
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="md:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl">{appointment.serviceName}</CardTitle>
                <CardDescription>Appointment Details</CardDescription>
              </div>
              {getStatusBadge(appointment.status)}
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-1">Date & Time</h3>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{formatDate(appointment.startTime)}</span>
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{formatTime(appointment.startTime)} - {formatTime(appointment.endTime)}</span>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-1">Staff</h3>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>{appointment.staffName || 'Any available staff'}</span>
                  </div>
                </div>

                {appointment.location && (
                  <div>
                    <h3 className="text-sm font-medium mb-1">Location</h3>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{appointment.location}</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-1">Service Details</h3>
                  <p className="text-sm">{appointment.serviceDescription || 'No description available.'}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-1">Duration</h3>
                  <p className="text-sm">{appointment.durationMinutes} minutes</p>
                </div>

                {appointment.price !== undefined && (
                  <div>
                    <h3 className="text-sm font-medium mb-1">Price</h3>
                    <p className="text-sm">${appointment.price.toFixed(2)}</p>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {appointment.clientPreferences && (
              <div>
                <h3 className="text-sm font-medium mb-1">Your Notes</h3>
                <p className="text-sm">{appointment.clientPreferences}</p>
              </div>
            )}

            {appointment.status === 'cancelled' && appointment.cancelReason && (
              <div className="rounded-md bg-destructive/10 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <XCircle className="h-5 w-5 text-destructive" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-destructive">Cancellation Reason</h3>
                    <div className="mt-2 text-sm">
                      <p>{appointment.cancelReason}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Appointment Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="rounded-md bg-muted p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium">Important Information</h3>
                    <div className="mt-2 text-sm text-muted-foreground">
                      <p>
                        Please arrive 10 minutes before your scheduled appointment time.
                        If you need to cancel or reschedule, please do so at least 24 hours in advance.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">What to Expect</h3>
                <p className="text-sm text-muted-foreground">
                  Your appointment has been confirmed. You will receive a reminder email 24 hours before your appointment.
                  If you have any questions or need to make changes, please contact us.
                </p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" asChild>
              <Link href="/client-portal/appointments">
                View All Appointments
              </Link>
            </Button>
            <Button asChild>
              <Link href="/client-portal">
                Return to Dashboard
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
