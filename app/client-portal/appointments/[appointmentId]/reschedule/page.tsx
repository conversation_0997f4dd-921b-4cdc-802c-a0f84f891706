// 'use client';

// import React, { useState, useEffect } from 'react';
// import { useRouter } from 'next/navigation';
// import Link from 'next/link';
// import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// import { useAuth } from '@/components/providers/AuthProvider';
// import { getClientByEmail } from '@/lib/services/clientService';
// import { collection, query, getDocs } from 'firebase/firestore';
// import { db } from '@/lib/firebase/config';
// import {
//   getAppointmentById,
//   getAvailableTimeSlots,
//   rescheduleAppointment
// } from '@/lib/services/appointmentService';
// import { Button } from '@/components/ui/button';
// import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogFooter,
//   DialogHeader,
//   DialogTitle
// } from '@/components/ui/dialog';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// import {
//   AlertCircle,
//   ArrowLeft,
//   Calendar,
//   Check,
//   Clock,
//   Loader2,
//   RefreshCw
// } from 'lucide-react';
// import { format, addMinutes, parse, addDays } from 'date-fns';
// import { Timestamp } from 'firebase/firestore';
// import { toast } from 'sonner';

// export default function RescheduleAppointmentPage({
//   params
// }: {
//   params: { appointmentId: string }
// }) {
//   const router = useRouter();
//   const { user } = useAuth();
//   const queryClient = useQueryClient();
//   const { appointmentId } = params;

//   // Form state
//   const [selectedDate, setSelectedDate] = useState<string>('');
//   const [selectedTime, setSelectedTime] = useState<string>('');
//   const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
//   const [showConfirmation, setShowConfirmation] = useState<boolean>(false);

//   // Fetch client data
//   const {
//     data: client,
//     isLoading: isLoadingClient
//   } = useQuery({
//     queryKey: ['clientByEmail', user?.email],
//     queryFn: async () => {
//       // Since we don't know the businessId yet, we need to search across all businesses
//       const allBusinessesQuery = query(collection(db, 'businesses'));
//       const businessesSnapshot = await getDocs(allBusinessesQuery);

//       for (const businessDoc of businessesSnapshot.docs) {
//         const businessId = businessDoc.id;
//         const client = await getClientByEmail(businessId, user?.email || '');
//         if (client) return client;
//       }

//       return null;
//     },
//     enabled: !!user?.email
//   });

//   // Fetch appointment
//   const {
//     data: appointment,
//     isLoading: isLoadingAppointment,
//     error: appointmentError
//   } = useQuery({
//     queryKey: ['appointment', appointmentId],
//     queryFn: () => getAppointmentById(appointmentId),
//     enabled: !!appointmentId
//   });

//   // Fetch available time slots
//   const {
//     data: availableTimeSlots,
//     isLoading: isLoadingTimeSlots,
//     refetch: refetchTimeSlots
//   } = useQuery({
//     queryKey: ['availableTimeSlots', appointment?.businessId, appointment?.serviceId, appointment?.staffId, selectedDate],
//     queryFn: () => {
//       if (!appointment?.businessId || !appointment?.serviceId || !selectedDate) return [];

//       const date = parse(selectedDate, 'yyyy-MM-dd', new Date());

//       return getAvailableTimeSlots(
//         appointment.businessId,
//         appointment.serviceId,
//         date,
//         appointment.staffId || undefined
//       );
//     },
//     enabled: !!appointment?.businessId && !!appointment?.serviceId && !!selectedDate
//   });

//   // Reschedule appointment mutation
//   const rescheduleAppointmentMutation = useMutation({
//     mutationFn: (data: {
//       appointmentId: string;
//       startTime: Date;
//       endTime: Date;
//     }) => rescheduleAppointment(
//       data.appointmentId,
//       data.startTime,
//       data.endTime
//     ),
//     onSuccess: () => {
//       queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
//       queryClient.invalidateQueries({ queryKey: ['clientAppointments', client?.id] });
//       setShowConfirmation(true);
//       setIsSubmitting(false);
//     },
//     onError: (error: Error) => {
//       toast.error(`Error rescheduling appointment: ${error.message}`);
//       setIsSubmitting(false);
//     }
//   });

//   // Generate available dates (next 30 days)
//   const getAvailableDates = () => {
//     const dates = [];
//     const today = new Date();

//     for (let i = 0; i < 30; i++) {
//       const date = addDays(today, i);
//       const dateString = format(date, 'yyyy-MM-dd');
//       const dayOfWeek = format(date, 'EEEE');

//       dates.push({
//         date: dateString,
//         dayOfWeek,
//         displayDate: format(date, 'EEEE, MMMM d, yyyy')
//       });
//     }

//     return dates;
//   };

//   // Handle date selection
//   const handleDateSelect = (date: string) => {
//     setSelectedDate(date);
//     setSelectedTime('');
//   };

//   // Handle time selection
//   const handleTimeSelect = (time: string) => {
//     setSelectedTime(time);
//   };

//   // Handle form submission
//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();

//     if (!appointment || !selectedDate || !selectedTime) {
//       toast.error('Please select a date and time');
//       return;
//     }

//     setIsSubmitting(true);

//     // Parse date and time
//     const date = parse(selectedDate, 'yyyy-MM-dd', new Date());
//     const time = parse(selectedTime, 'HH:mm', new Date());

//     // Combine date and time
//     const startTime = new Date(
//       date.getFullYear(),
//       date.getMonth(),
//       date.getDate(),
//       time.getHours(),
//       time.getMinutes()
//     );

//     // Calculate end time based on original appointment duration
//     const originalStartTime = appointment.startTime.toDate();
//     const originalEndTime = appointment.endTime.toDate();
//     const durationMinutes = Math.round((originalEndTime.getTime() - originalStartTime.getTime()) / (1000 * 60));
//     const endTime = addMinutes(startTime, durationMinutes);

//     // Reschedule appointment
//     rescheduleAppointmentMutation.mutate({
//       appointmentId,
//       startTime,
//       endTime
//     });
//   };

//   // Handle confirmation close
//   const handleConfirmationClose = () => {
//     setShowConfirmation(false);
//     router.push(`/client-portal/appointments/${appointmentId}`);
//   };

//   // Format date
//   const formatDate = (timestamp: Timestamp) => {
//     return format(timestamp.toDate(), 'EEEE, MMMM d, yyyy');
//   };

//   // Format time
//   const formatTime = (timestamp: Timestamp) => {
//     return format(timestamp.toDate(), 'h:mm a');
//   };

//   const isLoading = isLoadingClient || isLoadingAppointment;

//   if (isLoading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <Loader2 className="h-8 w-8 animate-spin text-primary" />
//       </div>
//     );
//   }

//   if (appointmentError || !appointment) {
//     return (
//       <Card>
//         <CardContent className="flex flex-col items-center justify-center py-12">
//           <AlertCircle className="h-12 w-12 text-destructive mb-4" />
//           <h3 className="text-lg font-medium">Appointment Not Found</h3>
//           <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
//             {appointmentError
//               ? (appointmentError as Error).message
//               : "We couldn't find the appointment you're looking for."}
//           </p>
//           <Button asChild>
//             <Link href="/client-portal/appointments">Back to Appointments</Link>
//           </Button>
//         </CardContent>
//       </Card>
//     );
//   }

//   // Check if this appointment belongs to the current client
//   if (client && appointment.clientId !== client.id && appointment.clientEmail !== client.email) {
//     return (
//       <Card>
//         <CardContent className="flex flex-col items-center justify-center py-12">
//           <AlertCircle className="h-12 w-12 text-destructive mb-4" />
//           <h3 className="text-lg font-medium">Access Denied</h3>
//           <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
//             You don't have permission to reschedule this appointment.
//           </p>
//           <Button asChild>
//             <Link href="/client-portal/appointments">Back to Appointments</Link>
//           </Button>
//         </CardContent>
//       </Card>
//     );
//   }

//   // Check if appointment can be rescheduled
//   if (appointment.status !== 'scheduled' && appointment.status !== 'confirmed') {
//     return (
//       <Card>
//         <CardContent className="flex flex-col items-center justify-center py-12">
//           <AlertCircle className="h-12 w-12 text-destructive mb-4" />
//           <h3 className="text-lg font-medium">Cannot Reschedule</h3>
//           <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
//             This appointment cannot be rescheduled because it is {appointment.status}.
//           </p>
//           <Button asChild>
//             <Link href={`/client-portal/appointments/${appointmentId}`}>
//               View Appointment Details
//             </Link>
//           </Button>
//         </CardContent>
//       </Card>
//     );
//   }

//   return (
//     <div className="space-y-6">
//       <div className="flex items-center justify-between">
//         <Button variant="ghost" onClick={() => router.back()} className="gap-1">
//           <ArrowLeft className="h-4 w-4" />
//           Back
//         </Button>
//         <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Reschedule Appointment</h1>
//       </div>

//       <Card>
//         <CardHeader>
//           <CardTitle>Current Appointment</CardTitle>
//           <CardDescription>
//             You are rescheduling the following appointment
//           </CardDescription>
//         </CardHeader>
//         <CardContent>
//           <div className="grid gap-4 md:grid-cols-2">
//             <div>
//               <h3 className="text-sm font-medium">Service</h3>
//               <p>{appointment.serviceName}</p>
//               <p className="text-sm text-muted-foreground">
//                 {appointment.durationMinutes} minutes
//                 {appointment.price !== undefined && ` - $${appointment.price.toFixed(2)}`}
//               </p>
//             </div>

//             <div>
//               <h3 className="text-sm font-medium">Staff</h3>
//               <p>{appointment.staffName || 'Any Available Staff'}</p>
//             </div>

//             <div>
//               <h3 className="text-sm font-medium">Date & Time</h3>
//               <p>{formatDate(appointment.startTime)}</p>
//               <p className="text-sm text-muted-foreground">
//                 {formatTime(appointment.startTime)}
//               </p>
//             </div>
//           </div>
//         </CardContent>
//       </Card>

//       <form onSubmit={handleSubmit}>
//         <Card>
//           <CardHeader>
//             <CardTitle>Select New Date & Time</CardTitle>
//             <CardDescription>
//               Choose a new date and time for your appointment
//             </CardDescription>
//           </CardHeader>
//           <CardContent>
//             <Tabs defaultValue="date" className="space-y-4">
//               <TabsList>
//                 <TabsTrigger value="date">
//                   <Calendar className="h-4 w-4 mr-2" />
//                   Date
//                 </TabsTrigger>
//                 <TabsTrigger value="time" disabled={!selectedDate}>
//                   <Clock className="h-4 w-4 mr-2" />
//                   Time
//                 </TabsTrigger>
//               </TabsList>

//               <TabsContent value="date" className="space-y-4">
//                 <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
//                   {getAvailableDates().map((dateObj) => (
//                     <div
//                       key={dateObj.date}
//                       className={`border rounded-lg p-4 cursor-pointer transition-colors ${
//                         selectedDate === dateObj.date
//                           ? 'border-primary bg-primary/5'
//                           : 'hover:border-primary/50'
//                       }`}
//                       onClick={() => handleDateSelect(dateObj.date)}
//                     >
//                       <div className="flex justify-between items-start">
//                         <div>
//                           <div className="text-sm text-muted-foreground">
//                             {dateObj.dayOfWeek}
//                           </div>
//                           <div className="font-medium">
//                             {format(parse(dateObj.date, 'yyyy-MM-dd', new Date()), 'MMMM d, yyyy')}
//                           </div>
//                         </div>
//                         {selectedDate === dateObj.date && (
//                           <div className="rounded-full bg-primary text-primary-foreground p-1">
//                             <Check className="h-4 w-4" />
//                           </div>
//                         )}
//                       </div>
//                     </div>
//                   ))}
//                 </div>
//               </TabsContent>

//               <TabsContent value="time" className="space-y-4">
//                 {isLoadingTimeSlots ? (
//                   <div className="flex justify-center items-center h-32">
//                     <Loader2 className="h-6 w-6 animate-spin text-primary" />
//                   </div>
//                 ) : availableTimeSlots && availableTimeSlots.length > 0 ? (
//                   <div className="grid gap-2 md:grid-cols-3 lg:grid-cols-4">
//                     {availableTimeSlots.map((slot) => (
//                       <div
//                         key={slot}
//                         className={`border rounded-lg p-3 cursor-pointer transition-colors ${
//                           selectedTime === slot
//                             ? 'border-primary bg-primary/5'
//                             : 'hover:border-primary/50'
//                         }`}
//                         onClick={() => handleTimeSelect(slot)}
//                       >
//                         <div className="flex justify-between items-center">
//                           <div className="font-medium">
//                             {format(parse(slot, 'HH:mm', new Date()), 'h:mm a')}
//                           </div>
//                           {selectedTime === slot && (
//                             <div className="rounded-full bg-primary text-primary-foreground p-1">
//                               <Check className="h-3 w-3" />
//                             </div>
//                           )}
//                         </div>
//                       </div>
//                     ))}
//                   </div>
//                 ) : (
//                   <div className="text-center py-8">
//                     <p className="text-muted-foreground">
//                       No available time slots for the selected date
//                     </p>
//                     <Button
//                       variant="outline"
//                       className="mt-4"
//                       onClick={() => refetchTimeSlots()}
//                     >
//                       <RefreshCw className="h-4 w-4 mr-2" />
//                       Refresh
//                     </Button>
//                   </div>
//                 )}
//               </TabsContent>
//             </Tabs>
//           </CardContent>
//           <CardFooter className="flex justify-end">
//             <Button
//               type="submit"
//               disabled={isSubmitting || !selectedDate || !selectedTime}
//             >
//               {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
//               Reschedule Appointment
//             </Button>
//           </CardFooter>
//         </Card>
//       </form>

//       {/* Confirmation Dialog */}
//       <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
//         <DialogContent>
//           <DialogHeader>
//             <DialogTitle>Appointment Rescheduled!</DialogTitle>
//             <DialogDescription>
//               Your appointment has been successfully rescheduled.
//             </DialogDescription>
//           </DialogHeader>
//           <div className="space-y-4 py-4">
//             <div className="rounded-md bg-primary/10 p-4">
//               <div className="flex">
//                 <div className="flex-shrink-0">
//                   <Check className="h-5 w-5 text-primary" />
//                 </div>
//                 <div className="ml-3">
//                   <h3 className="text-sm font-medium">Rescheduling Confirmed</h3>
//                   <div className="mt-2 text-sm">
//                     <p>
//                       You will receive a confirmation email shortly. You can view your updated appointment in the appointments section.
//                     </p>
//                   </div>
//                 </div>
//               </div>
//             </div>

//             <div className="space-y-2">
//               <h3 className="text-sm font-medium">New Appointment Details</h3>
//               <p className="text-sm">
//                 {appointment.serviceName} on {format(parse(selectedDate, 'yyyy-MM-dd', new Date()), 'EEEE, MMMM d, yyyy')} at {format(parse(selectedTime, 'HH:mm', new Date()), 'h:mm a')}
//               </p>
//               <p className="text-sm">
//                 With: {appointment.staffName || 'Any Available Staff'}
//               </p>
//             </div>
//           </div>
//           <DialogFooter>
//             <Button onClick={handleConfirmationClose}>
//               View Appointment Details
//             </Button>
//           </DialogFooter>
//         </DialogContent>
//       </Dialog>
//     </div>
//   );
// }
