// 'use client';

// import React from 'react';
// import Link from 'next/link';
// import { useQuery } from '@tanstack/react-query';
// import { useAuth } from '@/components/providers/AuthProvider';
// import { getClientByEmail } from '@/lib/services/clientService';
// import { collection, query, getDocs } from 'firebase/firestore';
// import { db } from '@/lib/firebase/config';
// import { getUpcomingAppointmentsForClient } from '@/lib/services/appointmentService';
// import { Button } from '@/components/ui/button';
// import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
// import { Badge } from '@/components/ui/badge';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
// import {
//   Calendar,
//   Clock,
//   FileText,
//   Loader2,
//   MessageSquare,
//   Plus,
//   User,
//   AlertCircle,
//   CheckCircle,
//   XCircle,
//   Mail,
//   Phone
// } from 'lucide-react';
// import { format } from 'date-fns';
// import { Timestamp } from 'firebase/firestore';

// export default function ClientPortalPage() {
//   const { user } = useAuth();

//   // Fetch client data
//   const {
//     data: client,
//     isLoading: isLoadingClient,
//     error: clientError
//   } = useQuery({
//     queryKey: ['clientByEmail', user?.email],
//     queryFn: async () => {
//       // Since we don't know the businessId yet, we need to search across all businesses
//       // This is a temporary solution until we implement proper client authentication
//       const allBusinessesQuery = query(collection(db, 'businesses'));
//       const businessesSnapshot = await getDocs(allBusinessesQuery);

//       for (const businessDoc of businessesSnapshot.docs) {
//         const businessId = businessDoc.id;
//         const client = await getClientByEmail(businessId, user?.email || '');
//         if (client) return client;
//       }

//       return null;
//     },
//     enabled: !!user?.email
//   });

//   // Fetch upcoming appointments
//   const {
//     data: upcomingAppointments,
//     isLoading: isLoadingAppointments
//   } = useQuery({
//     queryKey: ['upcomingAppointments', client?.id],
//     queryFn: () => getUpcomingAppointmentsForClient(client?.id || ''),
//     enabled: !!client?.id
//   });

//   // Format date
//   const formatDate = (timestamp: Timestamp) => {
//     return format(timestamp.toDate(), 'EEEE, MMMM d, yyyy');
//   };

//   // Format time
//   const formatTime = (timestamp: Timestamp) => {
//     return format(timestamp.toDate(), 'h:mm a');
//   };

//   // Get appointment status badge
//   const getStatusBadge = (status: string) => {
//     switch (status) {
//       case 'completed':
//         return <Badge variant="success" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Completed</Badge>;
//       case 'scheduled':
//         return <Badge variant="outline" className="flex items-center"><Calendar className="h-3 w-3 mr-1" /> Scheduled</Badge>;
//       case 'confirmed':
//         return <Badge variant="default" className="flex items-center"><CheckCircle className="h-3 w-3 mr-1" /> Confirmed</Badge>;
//       case 'cancelled':
//         return <Badge variant="destructive" className="flex items-center"><XCircle className="h-3 w-3 mr-1" /> Cancelled</Badge>;
//       case 'no-show':
//         return <Badge variant="destructive" className="flex items-center"><AlertCircle className="h-3 w-3 mr-1" /> No-show</Badge>;
//       default:
//         return <Badge variant="secondary">{status}</Badge>;
//     }
//   };

//   const isLoading = isLoadingClient || isLoadingAppointments;

//   if (isLoading) {
//     return (
//       <div className="flex justify-center items-center h-64">
//         <Loader2 className="h-8 w-8 animate-spin text-primary" />
//       </div>
//     );
//   }

//   if (clientError || !client) {
//     return (
//       <Card>
//         <CardContent className="flex flex-col items-center justify-center py-12">
//           <AlertCircle className="h-12 w-12 text-destructive mb-4" />
//           <h3 className="text-lg font-medium">Error Loading Profile</h3>
//           <p className="text-sm text-muted-foreground mt-1 mb-4 text-center max-w-md">
//             {clientError
//               ? (clientError as Error).message
//               : "We couldn't find your client profile. Please contact support for assistance."}
//           </p>
//           <Button asChild>
//             <Link href="/auth/login">Return to Login</Link>
//           </Button>
//         </CardContent>
//       </Card>
//     );
//   }

//   return (
//     <div className="space-y-6">
//       <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
//         <div>
//           <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Welcome, {client.name}</h1>
//           <p className="text-muted-foreground">
//             Manage your appointments and profile
//           </p>
//         </div>
//         <Button asChild>
//           <Link href="/client-portal/appointments/new">
//             <Plus className="h-4 w-4 mr-2" />
//             Book Appointment
//           </Link>
//         </Button>
//       </div>

//       <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
//         <Card>
//           <CardHeader className="pb-2">
//             <CardTitle className="text-lg">Upcoming Appointments</CardTitle>
//           </CardHeader>
//           <CardContent>
//             {upcomingAppointments && upcomingAppointments.length > 0 ? (
//               <div className="space-y-4">
//                 {upcomingAppointments.slice(0, 3).map((appointment) => (
//                   <div
//                     key={appointment.id}
//                     className="flex flex-col gap-1 p-3 rounded-lg border"
//                   >
//                     <div className="flex justify-between items-start">
//                       <div className="font-medium">{appointment.serviceName}</div>
//                       {getStatusBadge(appointment.status)}
//                     </div>
//                     <div className="text-sm text-muted-foreground">
//                       <div className="flex items-center">
//                         <Calendar className="h-3 w-3 mr-1" />
//                         {formatDate(appointment.startTime)}
//                       </div>
//                       <div className="flex items-center">
//                         <Clock className="h-3 w-3 mr-1" />
//                         {formatTime(appointment.startTime)}
//                       </div>
//                     </div>
//                     <div className="text-sm mt-1">
//                       With: {appointment.staffName || 'Any available staff'}
//                     </div>
//                   </div>
//                 ))}
//                 {upcomingAppointments.length > 3 && (
//                   <Button variant="link" className="px-0" asChild>
//                     <Link href="/client-portal/appointments">
//                       View all {upcomingAppointments.length} appointments
//                     </Link>
//                   </Button>
//                 )}
//               </div>
//             ) : (
//               <div className="flex flex-col items-center justify-center py-6 text-center">
//                 <Calendar className="h-8 w-8 text-muted-foreground mb-2" />
//                 <p className="text-sm text-muted-foreground">No upcoming appointments</p>
//                 <Button variant="link" asChild>
//                   <Link href="/client-portal/appointments/new">Book an appointment</Link>
//                 </Button>
//               </div>
//             )}
//           </CardContent>
//         </Card>

//         <Card>
//           <CardHeader className="pb-2">
//             <CardTitle className="text-lg">My Profile</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <div className="space-y-2">
//               <div className="flex items-center gap-2">
//                 <User className="h-4 w-4 text-muted-foreground" />
//                 <span>{client.name}</span>
//               </div>
//               <div className="flex items-center gap-2">
//                 <Mail className="h-4 w-4 text-muted-foreground" />
//                 <span>{client.email}</span>
//               </div>
//               {client.phone && (
//                 <div className="flex items-center gap-2">
//                   <Phone className="h-4 w-4 text-muted-foreground" />
//                   <span>{client.phone}</span>
//                 </div>
//               )}
//             </div>
//           </CardContent>
//           <CardFooter>
//             <Button variant="outline" className="w-full" asChild>
//               <Link href="/client-portal/profile">
//                 Edit Profile
//               </Link>
//             </Button>
//           </CardFooter>
//         </Card>

//         <Card>
//           <CardHeader className="pb-2">
//             <CardTitle className="text-lg">Quick Actions</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <div className="space-y-2">
//               <Button variant="outline" className="w-full justify-start" asChild>
//                 <Link href="/client-portal/appointments/new">
//                   <Calendar className="h-4 w-4 mr-2" />
//                   Book Appointment
//                 </Link>
//               </Button>
//               <Button variant="outline" className="w-full justify-start" asChild>
//                 <Link href="/client-portal/appointments">
//                   <Clock className="h-4 w-4 mr-2" />
//                   View Appointments
//                 </Link>
//               </Button>
//               <Button variant="outline" className="w-full justify-start" asChild>
//                 <Link href="/client-portal/messages">
//                   <MessageSquare className="h-4 w-4 mr-2" />
//                   Messages
//                 </Link>
//               </Button>
//               <Button variant="outline" className="w-full justify-start" asChild>
//                 <Link href="/client-portal/documents">
//                   <FileText className="h-4 w-4 mr-2" />
//                   Documents
//                 </Link>
//               </Button>
//             </div>
//           </CardContent>
//         </Card>
//       </div>

//       <Tabs defaultValue="appointments" className="mt-6">
//         <TabsList>
//           <TabsTrigger value="appointments">Recent Activity</TabsTrigger>
//           <TabsTrigger value="documents">Documents</TabsTrigger>
//           <TabsTrigger value="messages">Messages</TabsTrigger>
//         </TabsList>
//         <TabsContent value="appointments" className="mt-4">
//           <Card>
//             <CardHeader>
//               <CardTitle>Recent Activity</CardTitle>
//               <CardDescription>Your recent appointments and activity</CardDescription>
//             </CardHeader>
//             <CardContent>
//               {upcomingAppointments && upcomingAppointments.length > 0 ? (
//                 <div className="space-y-4">
//                   {upcomingAppointments.map((appointment) => (
//                     <div
//                       key={appointment.id}
//                       className="flex flex-col md:flex-row md:items-center justify-between gap-4 p-4 rounded-lg border"
//                     >
//                       <div>
//                         <div className="font-medium">{appointment.serviceName}</div>
//                         <div className="text-sm text-muted-foreground">
//                           {formatDate(appointment.startTime)} at {formatTime(appointment.startTime)}
//                         </div>
//                         <div className="text-sm">
//                           With: {appointment.staffName || 'Any available staff'}
//                         </div>
//                       </div>
//                       <div className="flex flex-col md:flex-row gap-2 md:items-center">
//                         {getStatusBadge(appointment.status)}
//                         <div className="flex gap-2 mt-2 md:mt-0">
//                           <Button size="sm" variant="outline" asChild>
//                             <Link href={`/client-portal/appointments/${appointment.id}`}>
//                               View Details
//                             </Link>
//                           </Button>
//                           {(appointment.status === 'scheduled' || appointment.status === 'confirmed') && (
//                             <Button size="sm" variant="outline" asChild>
//                               <Link href={`/client-portal/appointments/${appointment.id}/reschedule`}>
//                                 Reschedule
//                               </Link>
//                             </Button>
//                           )}
//                         </div>
//                       </div>
//                     </div>
//                   ))}
//                 </div>
//               ) : (
//                 <div className="flex flex-col items-center justify-center py-6 text-center">
//                   <Calendar className="h-8 w-8 text-muted-foreground mb-2" />
//                   <p className="text-sm text-muted-foreground">No recent activity</p>
//                 </div>
//               )}
//             </CardContent>
//           </Card>
//         </TabsContent>
//         <TabsContent value="documents" className="mt-4">
//           <Card>
//             <CardHeader>
//               <CardTitle>Documents</CardTitle>
//               <CardDescription>Your documents and forms</CardDescription>
//             </CardHeader>
//             <CardContent>
//               <div className="flex flex-col items-center justify-center py-6 text-center">
//                 <FileText className="h-8 w-8 text-muted-foreground mb-2" />
//                 <p className="text-sm text-muted-foreground">No documents available</p>
//               </div>
//             </CardContent>
//           </Card>
//         </TabsContent>
//         <TabsContent value="messages" className="mt-4">
//           <Card>
//             <CardHeader>
//               <CardTitle>Messages</CardTitle>
//               <CardDescription>Your messages and notifications</CardDescription>
//             </CardHeader>
//             <CardContent>
//               <div className="flex flex-col items-center justify-center py-6 text-center">
//                 <MessageSquare className="h-8 w-8 text-muted-foreground mb-2" />
//                 <p className="text-sm text-muted-foreground">No messages available</p>
//               </div>
//             </CardContent>
//           </Card>
//         </TabsContent>
//       </Tabs>
//     </div>
//   );
// }


// we want a redirect to index here

'use client';
import { useRouter } from 'next/navigation';


export default function ClientPortalPage() {
  const router = useRouter();
  router.push('/');
  return null;
}
