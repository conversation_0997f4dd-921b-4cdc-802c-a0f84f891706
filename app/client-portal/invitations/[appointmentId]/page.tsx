'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Calendar, Clock, MapPin, User, Mail, Phone, Download, ArrowLeft, ExternalLink } from 'lucide-react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Appointment } from '@/lib/types/db';
import { getICSFileURL } from '@/lib/services/firebaseStorageService';
import { generateICSContent, downloadICSFile, generateICSFileName } from '@/lib/services/icsService';

export default function AppointmentInvitationPage() {
  const params = useParams();
  const router = useRouter();
  const appointmentId = params.appointmentId as string;

  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [businessName, setBusinessName] = useState<string>('');
  const [businessAddress, setBusinessAddress] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [icsFileURL, setIcsFileURL] = useState<string | null>(null);

  useEffect(() => {
    const fetchAppointment = async () => {
      try {
        setLoading(true);
        
        // Fetch appointment data
        const appointmentRef = doc(db, 'appointments', appointmentId);
        const appointmentDoc = await getDoc(appointmentRef);

        if (!appointmentDoc.exists()) {
          setError('Appointment not found');
          return;
        }

        const appointmentData = { id: appointmentDoc.id, ...appointmentDoc.data() } as Appointment;
        setAppointment(appointmentData);

        // Fetch business data
        const businessRef = doc(db, 'businesses', appointmentData.businessId);
        const businessDoc = await getDoc(businessRef);

        if (businessDoc.exists()) {
          const businessData = businessDoc.data();
          setBusinessName(businessData.name || 'Business');
          setBusinessAddress(businessData.address || '');
        }

        // Try to get ICS file URL
        try {
          const icsURL = await getICSFileURL(appointmentData, businessName || 'Business');
          setIcsFileURL(icsURL);
        } catch (icsError) {
          console.warn('Could not fetch ICS file URL:', icsError);
        }

      } catch (err) {
        console.error('Error fetching appointment:', err);
        setError('Failed to load appointment details');
      } finally {
        setLoading(false);
      }
    };

    if (appointmentId) {
      fetchAppointment();
    }
  }, [appointmentId]);

  const handleDownloadICS = () => {
    if (!appointment) return;

    try {
      const icsContent = generateICSContent(appointment, businessName, businessAddress);
      const fileName = generateICSFileName(appointment, businessName);
      downloadICSFile(icsContent, fileName);
    } catch (error) {
      console.error('Error downloading ICS file:', error);
    }
  };

  const formatDateTime = (date: Date, timeZone: string = 'UTC') => {
    return date.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  const formatTime = (date: Date, timeZone: string = 'UTC') => {
    return date.toLocaleString('en-US', {
      timeZone,
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading appointment details...</p>
        </div>
      </div>
    );
  }

  if (error || !appointment) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <div className="text-red-500 mb-4">
              <Calendar className="h-12 w-12 mx-auto" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Appointment Not Found</h2>
            <p className="text-gray-600 mb-4">{error || 'The appointment you\'re looking for could not be found.'}</p>
            <Button onClick={() => router.push('/')} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const startTime = appointment.startTime instanceof Date 
    ? appointment.startTime 
    : appointment.startTime.toDate();
  const endTime = appointment.endTime instanceof Date 
    ? appointment.endTime 
    : appointment.endTime.toDate();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <Button 
            onClick={() => router.back()} 
            variant="ghost" 
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Appointment Invitation</h1>
            <p className="text-gray-600">View your appointment details and add to your calendar</p>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Appointment Details */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl">Appointment Details</CardTitle>
                  <Badge className={getStatusColor(appointment.status)}>
                    {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                  </Badge>
                </div>
                <CardDescription>
                  Your appointment with {businessName}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Date & Time</p>
                      <p className="text-sm text-gray-600">{formatDateTime(startTime)}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Duration</p>
                      <p className="text-sm text-gray-600">
                        {formatTime(startTime)} - {formatTime(endTime)}
                      </p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <User className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Service</p>
                      <p className="text-sm text-gray-600">{appointment.serviceName || 'Not specified'}</p>
                    </div>
                  </div>

                  {appointment.staffName && (
                    <div className="flex items-center gap-3">
                      <User className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium">Staff Member</p>
                        <p className="text-sm text-gray-600">{appointment.staffName}</p>
                      </div>
                    </div>
                  )}

                  {businessAddress && (
                    <div className="flex items-center gap-3">
                      <MapPin className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium">Location</p>
                        <p className="text-sm text-gray-600">{businessAddress}</p>
                      </div>
                    </div>
                  )}

                  {appointment.notes && (
                    <div className="flex items-start gap-3">
                      <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <p className="font-medium">Notes</p>
                        <p className="text-sm text-gray-600">{appointment.notes}</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium">Email</p>
                    <p className="text-sm text-gray-600">{appointment.clientEmail}</p>
                  </div>
                </div>
                
                {appointment.clientPhone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Phone</p>
                      <p className="text-sm text-gray-600">{appointment.clientPhone}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Actions Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={handleDownloadICS} 
                  className="w-full"
                  variant="default"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Calendar File
                </Button>

                <Button 
                  onClick={() => router.push(`/client-portal/appointments/${appointment.id}`)}
                  className="w-full"
                  variant="outline"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Full Details
                </Button>

                <Button 
                  onClick={() => router.push(`/client-portal/appointments/${appointment.id}/reschedule`)}
                  className="w-full"
                  variant="outline"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Reschedule
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Calendar Integration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm text-gray-600">
                  <p>
                    <strong>Add to Calendar:</strong> Download the .ics file and open it with your preferred calendar application.
                  </p>
                  <p>
                    <strong>Compatible with:</strong> Google Calendar, Outlook, Apple Calendar, and most other calendar apps.
                  </p>
                  <p>
                    <strong>Reminders:</strong> The calendar event includes automatic reminders 1 hour before your appointment.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
