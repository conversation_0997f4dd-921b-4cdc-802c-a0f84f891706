'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';

export default function TestEmailPage() {
  const [email, setEmail] = useState('');
  const [subject, setSubject] = useState('Test Email from Onpointly');
  const [message, setMessage] = useState('This is a test email to verify Mailgun configuration.');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  const sendTestEmail = async () => {
    if (!email) {
      setResult({ success: false, message: 'Please enter an email address' });
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      // Import the mailgun service
      const { sendMailgunEmail } = await import('@/lib/services/mailgunService');

      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">Test Email</h1>
          </div>
          <div style="padding: 20px;">
            <p>Hello!</p>
            <p>${message}</p>
            <p>This email was sent at: ${new Date().toLocaleString()}</p>
            <p>If you received this email, your Mailgun configuration is working correctly!</p>
          </div>
          <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
            <p>Powered by <a href="https://onpointly.com" style="color: #4f46e5; text-decoration: none;">Onpointly</a></p>
          </div>
        </div>
      `;

      const result = await sendMailgunEmail(
        email,
        subject,
        emailHtml,
        'Onpointly Test <<EMAIL>>'
      );

      if (result.success) {
        setResult({
          success: true,
          message: `Email sent successfully! Message ID: ${result.id}`
        });
      } else {
        setResult({
          success: false,
          message: `Failed to send email: ${result.details || 'Unknown error'}`
        });
      }
    } catch (error: any) {
      console.error('Error sending test email:', error);
      setResult({
        success: false,
        message: `Error: ${error.message || 'Unknown error occurred'}`
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>Email Configuration Test</CardTitle>
            <CardDescription>
              Test your Mailgun email configuration by sending a test email.
              For sandbox domains, make sure the recipient email is added to your authorized recipients list.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Recipient Email
              </label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter email address to test"
                className="w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                For sandbox domains, this email must be in your authorized recipients list
              </p>
            </div>

            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                Subject
              </label>
              <Input
                id="subject"
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                className="w-full"
              />
            </div>

            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                Message
              </label>
              <Textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                rows={3}
                className="w-full"
              />
            </div>

            <Button 
              onClick={sendTestEmail} 
              disabled={loading || !email}
              className="w-full"
            >
              {loading ? 'Sending...' : 'Send Test Email'}
            </Button>

            {result && (
              <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                <AlertDescription className={result.success ? 'text-green-800' : 'text-red-800'}>
                  {result.message}
                </AlertDescription>
              </Alert>
            )}

            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">Mailgun Sandbox Information</h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p><strong>Current Domain:</strong> {process.env.NEXT_PUBLIC_MAILGUN_DOMAIN}</p>
                <p><strong>Environment:</strong> {process.env.NODE_ENV || 'development'}</p>
                <p><strong>Force Real Emails:</strong> {process.env.FORCE_REAL_EMAILS || 'false'}</p>
              </div>
              
              <div className="mt-3 text-sm text-blue-700">
                <p><strong>Sandbox Restrictions:</strong></p>
                <ul className="list-disc list-inside space-y-1 mt-1">
                  <li>Can only send to authorized recipients</li>
                  <li>Limited to 300 emails per day</li>
                  <li>Add recipients in Mailgun dashboard → Sending → Authorized Recipients</li>
                  <li>Or upgrade to a paid plan to remove restrictions</li>
                </ul>
              </div>
            </div>

            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h3 className="font-medium text-yellow-900 mb-2">Troubleshooting</h3>
              <div className="text-sm text-yellow-800 space-y-1">
                <p><strong>403 Forbidden Error:</strong> Add recipient to authorized recipients list</p>
                <p><strong>401 Unauthorized:</strong> Check your API key</p>
                <p><strong>400 Bad Request:</strong> Check domain and email format</p>
                <p><strong>No emails received:</strong> Check spam folder and Mailgun logs</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
