// Simple test script to verify Mailgun configuration
const FormData = require('form-data');
const Mailgun = require('mailgun.js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

async function testMailgun() {
  console.log('🧪 Testing Mailgun Configuration');
  console.log('================================');

  // Check environment variables
  const MAILGUN_API_KEY = process.env.MAILGUN_API_KEY || process.env.NEXT_PUBLIC_MAILGUN_API_KEY;
  const MAILGUN_DOMAIN = process.env.MAILGUN_DOMAIN || process.env.NEXT_PUBLIC_MAILGUN_DOMAIN;
  const MAILGUN_FROM = process.env.MAILGUN_FROM || process.env.NEXT_PUBLIC_MAILGUN_FROM;

  console.log('Environment Variables:');
  console.log(`- API Key: ${MAILGUN_API_KEY ? MAILGUN_API_KEY.substring(0, 8) + '...' : 'NOT SET'}`);
  console.log(`- Domain: ${MAILGUN_DOMAIN || 'NOT SET'}`);
  console.log(`- From: ${MAILGUN_FROM || 'NOT SET'}`);
  console.log(`- NODE_ENV: ${process.env.NODE_ENV || 'NOT SET'}`);
  console.log(`- FORCE_REAL_EMAILS: ${process.env.FORCE_REAL_EMAILS || 'NOT SET'}`);

  if (!MAILGUN_API_KEY || !MAILGUN_DOMAIN) {
    console.error('❌ Missing required Mailgun configuration');
    return;
  }

  try {
    // Initialize Mailgun
    const mailgun = new Mailgun(FormData);
    const mg = mailgun.client({
      username: 'api',
      key: MAILGUN_API_KEY,
    });

    console.log('\n📧 Sending test email...');

    // Send test email to a real email address
    const testEmail = '<EMAIL>'; // Use your actual email
    const messageData = {
      from: MAILGUN_FROM,
      to: [testEmail],
      subject: 'Onpointly Mailgun Test Email',
      html: `
        <h1>✅ Mailgun Test Successful!</h1>
        <p>This is a test email to verify Mailgun configuration with your domain.</p>
        <p><strong>Domain:</strong> ${MAILGUN_DOMAIN}</p>
        <p><strong>From:</strong> ${MAILGUN_FROM}</p>
        <p><strong>Sent at:</strong> ${new Date().toISOString()}</p>
        <p>If you received this email, your Mailgun configuration is working correctly!</p>
      `
    };

    const result = await mg.messages.create(MAILGUN_DOMAIN, messageData);
    
    console.log('✅ Email sent successfully!');
    console.log('Message ID:', result.id);
    console.log('Status:', result.message);

  } catch (error) {
    console.error('❌ Error sending email:', error.message);
    
    if (error.status) {
      console.error('Status Code:', error.status);
    }
    
    if (error.details) {
      console.error('Details:', error.details);
    }

    // Provide specific troubleshooting advice
    if (error.status === 401) {
      console.log('\n🔧 Troubleshooting:');
      console.log('- Check if your API key is correct');
      console.log('- Make sure the API key starts with your actual key (not "key-")');
      console.log('- Verify you\'re using the Private API key, not the Public key');
    } else if (error.status === 400) {
      console.log('\n🔧 Troubleshooting:');
      console.log('- Check if the domain is correct');
      console.log('- Verify the from email address format');
      console.log('- Make sure the recipient email is valid');
    }
  }
}

// Run the test
testMailgun().catch(console.error);
