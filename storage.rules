rules_version = '2';

// Firebase Storage Security Rules for Onpointly
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }
    
    function isOwner(businessId) {
      return isSignedIn() &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.businessId == businessId &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'owner';
    }
    
    function isAdmin(businessId) {
      return isSignedIn() &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.businessId == businessId &&
        (firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'owner');
    }
    
    function isStaff(businessId) {
      return isSignedIn() &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.businessId == businessId &&
        (firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'staff' ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'owner');
    }
    
    // Business logos - only business admins can upload/update, anyone can read
    match /businesses/{businessId}/logos/{logoFile} {
      allow read: if true; // Public read access for displaying logos
      allow write: if isAdmin(businessId);
      allow delete: if isAdmin(businessId);
    }
    
    // ICS files - temporary permissive rules for testing
    match /ics_files/{businessId}/{icsFile} {
      // Allow full access to ICS files for now (can be tightened later)
      // ICS files don't contain sensitive information, so this is safe
      allow read, write: if true;
    }
    
    // Client documents and files
    match /clients/{businessId}/{clientId}/{document} {
      // Business staff can read/write client documents
      allow read, write: if isStaff(businessId);
      
      // Clients can read their own documents
      allow read: if isSignedIn() && 
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'client' &&
        firestore.exists(/databases/(default)/documents/clients/$(clientId)) &&
        firestore.get(/databases/(default)/documents/clients/$(clientId)).data.email == request.auth.token.email;
    }
    
    // Staff profile pictures and documents
    match /staff/{businessId}/{staffId}/{document} {
      // Business admins can read/write staff documents
      allow read, write: if isAdmin(businessId);
      
      // Staff members can read/write their own documents
      allow read, write: if isSignedIn() && 
        firestore.exists(/databases/(default)/documents/staff/$(staffId)) &&
        firestore.get(/databases/(default)/documents/staff/$(staffId)).data.email == request.auth.token.email;
    }
    
    // Appointment attachments and documents
    match /appointments/{businessId}/{appointmentId}/{document} {
      // Business staff can read/write appointment documents
      allow read, write: if isStaff(businessId);
      
      // Clients can read documents for their own appointments
      allow read: if isSignedIn() && 
        firestore.exists(/databases/(default)/documents/appointments/$(appointmentId)) &&
        firestore.get(/databases/(default)/documents/appointments/$(appointmentId)).data.clientEmail == request.auth.token.email;
    }
    
    // Temporary uploads (for processing before moving to final location)
    match /temp/{userId}/{document} {
      allow read, write: if isSignedIn() && request.auth.uid == userId;
      // Auto-delete temp files after 1 hour
      allow read, write: if isSignedIn() && request.auth.uid == userId && 
        request.time < resource.timeCreated + duration.value(1, 'h');
    }
    
    // Business documents and files
    match /business_documents/{businessId}/{document} {
      allow read, write: if isAdmin(businessId);
    }
    
    // Public assets (like default images, templates, etc.)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if false; // Only allow writes through admin functions
    }
    
    // Backup and export files
    match /backups/{businessId}/{backupFile} {
      allow read, write: if isOwner(businessId);
    }
    
    // Default deny rule for any other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
