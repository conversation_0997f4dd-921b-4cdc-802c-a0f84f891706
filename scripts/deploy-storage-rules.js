#!/usr/bin/env node

/**
 * <PERSON>ript to deploy Firebase Storage rules
 * This script helps deploy the storage rules to Firebase
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔥 Firebase Storage Rules Deployment Script');
console.log('==========================================');

// Check if firebase.json exists and has storage rules configured
const firebaseConfigPath = path.join(process.cwd(), 'firebase.json');
if (!fs.existsSync(firebaseConfigPath)) {
  console.error('❌ firebase.json not found in the current directory');
  process.exit(1);
}

const firebaseConfig = JSON.parse(fs.readFileSync(firebaseConfigPath, 'utf8'));
if (!firebaseConfig.storage || !firebaseConfig.storage.rules) {
  console.error('❌ Storage rules not configured in firebase.json');
  console.log('Please ensure your firebase.json includes:');
  console.log(JSON.stringify({
    storage: {
      rules: "storage.rules"
    }
  }, null, 2));
  process.exit(1);
}

// Check if storage.rules file exists
const storageRulesPath = path.join(process.cwd(), firebaseConfig.storage.rules);
if (!fs.existsSync(storageRulesPath)) {
  console.error(`❌ Storage rules file not found: ${firebaseConfig.storage.rules}`);
  process.exit(1);
}

console.log('✅ Configuration files found');
console.log(`📄 Firebase config: ${firebaseConfigPath}`);
console.log(`📄 Storage rules: ${storageRulesPath}`);

// Check if Firebase CLI is installed
try {
  execSync('firebase --version', { stdio: 'pipe' });
  console.log('✅ Firebase CLI is installed');
} catch (error) {
  console.error('❌ Firebase CLI is not installed');
  console.log('Please install it with: npm install -g firebase-tools');
  process.exit(1);
}

// Check if user is logged in
try {
  const result = execSync('firebase projects:list', { stdio: 'pipe', encoding: 'utf8' });
  console.log('✅ Firebase CLI is authenticated');
} catch (error) {
  console.error('❌ Firebase CLI is not authenticated');
  console.log('Please login with: firebase login');
  process.exit(1);
}

// Deploy storage rules
try {
  console.log('\n🚀 Deploying Firebase Storage rules...');
  
  const deployResult = execSync('firebase deploy --only storage', { 
    stdio: 'inherit',
    encoding: 'utf8' 
  });
  
  console.log('\n✅ Firebase Storage rules deployed successfully!');
  console.log('\n📋 Next steps:');
  console.log('1. Test ICS file generation by creating a new appointment');
  console.log('2. Verify that ICS files are being stored in Firebase Storage');
  console.log('3. Check that email notifications include ICS file attachments');
  
} catch (error) {
  console.error('\n❌ Failed to deploy Firebase Storage rules');
  console.error('Error:', error.message);
  
  console.log('\n🔧 Troubleshooting:');
  console.log('1. Make sure you have the correct Firebase project selected');
  console.log('2. Check that you have permission to deploy to the project');
  console.log('3. Verify your storage.rules file syntax');
  console.log('4. Try running: firebase use --add');
  
  process.exit(1);
}
