/* UI Improvements for Onpointly */

/* Better spacing for cards */
.card-improved {
  @apply shadow-sm border border-gray-200 rounded-lg;
  margin-bottom: 1.5rem;
}

.card-improved:last-child {
  margin-bottom: 0;
}

/* Better button spacing */
.button-group {
  @apply flex flex-wrap gap-3;
}

.button-group .btn {
  @apply min-w-[120px];
}

/* Improved form spacing */
.form-section {
  @apply space-y-6 mb-8;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-field {
  @apply space-y-2 mb-4;
}

.form-field:last-child {
  margin-bottom: 0;
}

/* Better grid spacing */
.grid-improved {
  @apply grid gap-6;
}

.grid-improved.grid-cols-1 {
  @apply grid-cols-1;
}

.grid-improved.grid-cols-2 {
  @apply grid-cols-1 md:grid-cols-2;
}

.grid-improved.grid-cols-3 {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.grid-improved.grid-cols-4 {
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

/* Better container spacing */
.container-improved {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

/* Page header improvements */
.page-header {
  @apply mb-8 pb-4 border-b border-gray-200;
}

.page-header h1 {
  @apply text-2xl sm:text-3xl font-bold text-gray-900 mb-2;
}

.page-header p {
  @apply text-gray-600 text-base;
}

/* Section spacing */
.section-spacing {
  @apply py-8;
}

.section-spacing:first-child {
  padding-top: 0;
}

.section-spacing:last-child {
  padding-bottom: 0;
}

/* Card content improvements */
.card-content-improved {
  @apply p-6;
}

.card-content-improved > *:first-child {
  margin-top: 0;
}

.card-content-improved > *:last-child {
  margin-bottom: 0;
}

/* Better list spacing */
.list-improved {
  @apply space-y-3;
}

.list-improved li {
  @apply flex items-start gap-3 p-3 rounded-md hover:bg-gray-50 transition-colors;
}

/* Improved table spacing */
.table-improved {
  @apply w-full border-collapse;
}

.table-improved th,
.table-improved td {
  @apply px-4 py-3 text-left border-b border-gray-200;
}

.table-improved th {
  @apply bg-gray-50 font-medium text-gray-900;
}

.table-improved tbody tr:hover {
  @apply bg-gray-50;
}

/* Better modal spacing */
.modal-content-improved {
  @apply p-6 space-y-6;
}

.modal-header-improved {
  @apply pb-4 border-b border-gray-200 mb-6;
}

.modal-footer-improved {
  @apply pt-4 border-t border-gray-200 mt-6 flex justify-end gap-3;
}

/* Responsive spacing utilities */
.spacing-xs {
  @apply space-y-2;
}

.spacing-sm {
  @apply space-y-3;
}

.spacing-md {
  @apply space-y-4;
}

.spacing-lg {
  @apply space-y-6;
}

.spacing-xl {
  @apply space-y-8;
}

/* Margin utilities */
.margin-bottom-improved {
  @apply mb-6;
}

.margin-top-improved {
  @apply mt-6;
}

/* Padding utilities */
.padding-improved {
  @apply p-4 sm:p-6;
}

.padding-x-improved {
  @apply px-4 sm:px-6;
}

.padding-y-improved {
  @apply py-4 sm:py-6;
}

/* Focus improvements */
.focus-improved:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Hover improvements */
.hover-improved {
  @apply transition-all duration-200 ease-in-out;
}

.hover-improved:hover {
  @apply transform scale-105 shadow-md;
}

/* Status badge improvements */
.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-badge.success {
  @apply bg-green-100 text-green-800;
}

.status-badge.warning {
  @apply bg-yellow-100 text-yellow-800;
}

.status-badge.error {
  @apply bg-red-100 text-red-800;
}

.status-badge.info {
  @apply bg-blue-100 text-blue-800;
}

/* Loading state improvements */
.loading-skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.loading-text {
  @apply h-4 bg-gray-200 rounded;
}

.loading-title {
  @apply h-6 bg-gray-200 rounded;
}

/* Mobile improvements */
@media (max-width: 640px) {
  .mobile-stack {
    @apply flex-col space-y-3;
  }
  
  .mobile-full-width {
    @apply w-full;
  }
  
  .mobile-text-center {
    @apply text-center;
  }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  .card-improved {
    @apply border-gray-700 bg-gray-800;
  }
  
  .page-header {
    @apply border-gray-700;
  }
  
  .page-header h1 {
    @apply text-gray-100;
  }
  
  .page-header p {
    @apply text-gray-300;
  }
  
  .list-improved li:hover {
    @apply bg-gray-700;
  }
  
  .table-improved th {
    @apply bg-gray-700 text-gray-100;
  }
  
  .table-improved th,
  .table-improved td {
    @apply border-gray-700;
  }
  
  .table-improved tbody tr:hover {
    @apply bg-gray-700;
  }
}
