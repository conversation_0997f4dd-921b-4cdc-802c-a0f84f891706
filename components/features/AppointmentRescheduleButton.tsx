'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar, Loader2 } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { rescheduleAppointment } from '@/lib/services/appointmentService';
import { toast } from 'sonner';
import { Appointment } from '@/lib/types/db';

interface AppointmentRescheduleButtonProps {
  appointment: Appointment;
  onReschedule?: () => void;
  size?: 'default' | 'sm';
  variant?: 'default' | 'outline' | 'ghost';
  googleAccessToken?: string | null;
  businessTimezone?: string;
}

export function AppointmentRescheduleButton({
  appointment,
  onReschedule,
  size = 'default',
  variant = 'outline',
  googleAccessToken,
  businessTimezone = 'UTC'
}: AppointmentRescheduleButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [rescheduleData, setRescheduleData] = useState({
    date: '',
    time: '',
    updateGoogleCalendar: true
  });

  // Check if appointment can be rescheduled
  const canReschedule = !['cancelled', 'no-show', 'completed'].includes(appointment.status);

  // Open the reschedule dialog
  const openRescheduleDialog = () => {
    // Set default values based on current appointment
    const startTime = appointment.startTime.toDate();
    setRescheduleData({
      date: startTime.toISOString().split('T')[0],
      time: `${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}`,
      updateGoogleCalendar: true
    });
    setIsDialogOpen(true);
  };

  // Handle appointment rescheduling
  const handleReschedule = async () => {
    if (!rescheduleData.date || !rescheduleData.time) {
      toast.error('Please select a date and time');
      return;
    }

    setIsLoading(true);
    try {
      // Parse the new date and time
      const [hours, minutes] = rescheduleData.time.split(':').map(Number);
      const newStartTime = new Date(rescheduleData.date);
      newStartTime.setHours(hours, minutes, 0, 0);

      // Calculate end time based on original appointment duration
      const originalStartTime = appointment.startTime.toDate();
      const originalEndTime = appointment.endTime.toDate();
      const durationMinutes = Math.round((originalEndTime.getTime() - originalStartTime.getTime()) / (1000 * 60));
      const newEndTime = new Date(newStartTime.getTime() + durationMinutes * 60 * 1000);

      await rescheduleAppointment(
        appointment.id,
        newStartTime,
        newEndTime,
        rescheduleData.updateGoogleCalendar && !!appointment.googleCalendarEventId,
        googleAccessToken || undefined,
        businessTimezone
      );

      toast.success('Appointment rescheduled successfully');
      setIsDialogOpen(false);
      setRescheduleData({
        date: '',
        time: '',
        updateGoogleCalendar: true
      });

      // Call the onReschedule callback if provided
      if (onReschedule) {
        onReschedule();
      }
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      toast.error(`Error rescheduling appointment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={openRescheduleDialog}
        disabled={!canReschedule || isLoading}
        title={canReschedule ? 'Reschedule appointment' : 'Cannot reschedule this appointment'}
        className="whitespace-nowrap"
      >
        <Calendar className="h-4 w-4 mr-2" />
        <span className="hidden sm:inline">Reschedule</span>
        <span className="sm:hidden">Resched</span>
      </Button>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reschedule Appointment</DialogTitle>
            <DialogDescription>
              Update the date and time for this appointment.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="reschedule-date">New Date</Label>
              <Input
                id="reschedule-date"
                type="date"
                value={rescheduleData.date}
                onChange={(e) => setRescheduleData({ ...rescheduleData, date: e.target.value })}
                min={new Date().toISOString().split('T')[0]}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="reschedule-time">New Time</Label>
              <Input
                id="reschedule-time"
                type="time"
                value={rescheduleData.time}
                onChange={(e) => setRescheduleData({ ...rescheduleData, time: e.target.value })}
                required
              />
            </div>
            {appointment.googleCalendarEventId && (
              <div className="flex items-center space-x-2 pt-2">
                <Checkbox
                  id="update-google-calendar"
                  checked={rescheduleData.updateGoogleCalendar}
                  onCheckedChange={(checked) =>
                    setRescheduleData({ ...rescheduleData, updateGoogleCalendar: !!checked })
                  }
                />
                <Label htmlFor="update-google-calendar" className="font-normal">
                  Update in Google Calendar
                </Label>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDialogOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleReschedule}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Rescheduling...
                </>
              ) : (
                'Reschedule'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
