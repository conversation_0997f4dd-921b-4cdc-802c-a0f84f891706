// src/components/features/AppointmentList.tsx
'use client';

import React, { useState } from 'react';
import { Appointment, ServiceDefinition, StaffMember } from '@/lib/types/db'; // Types
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatDateTimeReadable } from '@/lib/utils/dateUtils'; // Date formatting
import { Loader2, Calendar, Clock, User, MapPin, FileText, MoreHorizontal, Edit, X, AlertCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { AppointmentStatusButton } from '@/components/features/AppointmentStatusButton';
import { AppointmentRescheduleButton } from '@/components/features/AppointmentRescheduleButton';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { cancelAppointment, rescheduleAppointment, updateAppointmentPaymentStatus } from '@/lib/services/appointmentService';
import { toast } from 'sonner';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface AppointmentListProps {
  appointments: Appointment[] | undefined; // Can be undefined during loading
  services: ServiceDefinition[] | undefined; // Map serviceId to name
  staffMembers?: StaffMember[] | undefined; // Staff members for reassignment
  isLoading: boolean;
  error?: Error | null;
  businessTimezone: string; // Needed for display
  onAppointmentUpdated?: () => void; // Callback when an appointment is updated
  googleAccessToken?: string | null; // For Google Calendar integration
  showPastAppointments?: boolean; // Whether to show past appointments (default: false)
  limit?: number; // Limit the number of appointments shown
  openAddDialogOnUpdate?: boolean; // Whether to open the add dialog after an update
}

export function AppointmentList({
    appointments,
    services,
    staffMembers,
    isLoading,
    error,
    businessTimezone,
    onAppointmentUpdated,
    googleAccessToken,
    showPastAppointments = false,
    limit,
    openAddDialogOnUpdate = false
}: AppointmentListProps) {
  // Debug logging
  console.log('AppointmentList props:', {
    appointmentsLength: appointments?.length || 0,
    servicesLength: services?.length || 0,
    staffMembersLength: staffMembers?.length || 0,
    isLoading,
    hasError: !!error,
    businessTimezone
  });

  if (appointments?.length) {
    console.log('First appointment:', appointments[0]);
  }

  // =========== ALL HOOKS MUST BE DEFINED HERE UNCONDITIONALLY ===========
  // State for appointment actions
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [rescheduleData, setRescheduleData] = useState({
    date: '',
    time: '',
    updateGoogleCalendar: true
  });

  // Create a map for quick service lookup
  const serviceMap = React.useMemo(() => {
    const map = new Map<string, ServiceDefinition>();
    services?.forEach(service => map.set(service.id!, service));
    return map;
  }, [services]);

  // Create a map for quick staff lookup
  const staffMap = React.useMemo(() => {
    const map = new Map<string, StaffMember>();
    staffMembers?.forEach(staff => map.set(staff.id, staff));
    return map;
  }, [staffMembers]);

  // Filter appointments based on date and limit
  const filteredAppointments = React.useMemo(() => {
    if (!appointments || appointments.length === 0) return [];

    const now = new Date();

    // First filter by date (past or upcoming)
    let filtered = [...appointments];
    if (!showPastAppointments) {
      // Show only upcoming appointments
      filtered = filtered.filter(appt => {
        const apptDate = appt.startTime instanceof Date ? appt.startTime : appt.startTime.toDate();
        return apptDate >= now;
      });
    } else {
      // Show only past appointments
      filtered = filtered.filter(appt => {
        const apptDate = appt.startTime instanceof Date ? appt.startTime : appt.startTime.toDate();
        return apptDate < now;
      });
    }

    // Sort by date (upcoming: ascending, past: descending)
    filtered.sort((a, b) => {
      const aTime = a.startTime instanceof Date ? a.startTime : a.startTime.toDate();
      const bTime = b.startTime instanceof Date ? b.startTime : b.startTime.toDate();

      return showPastAppointments
        ? bTime.getTime() - aTime.getTime() // Past: newest first
        : aTime.getTime() - bTime.getTime(); // Upcoming: soonest first
    });

    // Apply limit if specified
    if (limit && limit > 0) {
      filtered = filtered.slice(0, limit);
    }

    return filtered;
  }, [appointments, showPastAppointments, limit]);
  // =========== END OF HOOKS SECTION ===========

  // Handle appointment cancellation
  const handleCancelAppointment = async () => {
    if (!selectedAppointment) return;

    setIsActionLoading(true);
    try {
      await cancelAppointment(
        selectedAppointment.id,
        cancelReason
      );

      toast(`Appointment cancelled successfully`);
      setIsCancelDialogOpen(false);
      setCancelReason('');
      setSelectedAppointment(null);

      // Refresh the appointments list
      if (onAppointmentUpdated) {
        onAppointmentUpdated();
      }
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      toast(`Error cancelling appointment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsActionLoading(false);
    }
  };

  // Handle appointment rescheduling
  const handleRescheduleAppointment = async () => {
    if (!selectedAppointment) return;
    if (!rescheduleData.date || !rescheduleData.time) {
      toast(`Please select a date and time`);
      return;
    }

    setIsActionLoading(true);
    try {
      // Parse the new date and time
      const [hours, minutes] = rescheduleData.time.split(':').map(Number);
      const newStartTime = new Date(rescheduleData.date);
      newStartTime.setHours(hours, minutes, 0, 0);

      // Calculate end time based on service duration
      const service = serviceMap.get(selectedAppointment.serviceId);
      const durationMinutes = service?.durationMinutes || 60; // Default to 60 minutes if not found
      const newEndTime = new Date(newStartTime.getTime() + durationMinutes * 60 * 1000);

      await rescheduleAppointment(
        selectedAppointment.id,
        newStartTime,
        newEndTime,
        rescheduleData.updateGoogleCalendar && !!selectedAppointment.googleCalendarEventId,
        googleAccessToken || undefined,
        businessTimezone
      );

      toast(`Appointment rescheduled successfully`);
      setIsRescheduleDialogOpen(false);
      setRescheduleData({
        date: '',
        time: '',
        updateGoogleCalendar: true
      });
      setSelectedAppointment(null);

      // Refresh the appointments list
      if (onAppointmentUpdated) {
        onAppointmentUpdated();
      }
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      toast(`Error rescheduling appointment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsActionLoading(false);
    }
  };

  // Open reschedule dialog with current appointment data
  const openRescheduleDialog = (appointment: Appointment) => {
    setSelectedAppointment(appointment);

    // Convert Firestore timestamp to Date
    const startDate = appointment.startTime instanceof Date
      ? appointment.startTime
      : appointment.startTime.toDate();

    // Format date and time for the form inputs
    const dateStr = startDate.toISOString().split('T')[0];
    const timeStr = `${String(startDate.getHours()).padStart(2, '0')}:${String(startDate.getMinutes()).padStart(2, '0')}`;

    setRescheduleData({
      date: dateStr,
      time: timeStr,
      updateGoogleCalendar: true
    });

    setIsRescheduleDialogOpen(true);
  };

  // Open cancel dialog
  const openCancelDialog = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setCancelReason('');
    setIsCancelDialogOpen(true);
  };

  // Open details dialog
  const openDetailsDialog = (appointment: Appointment) => {
    // Debug logging
    console.log('Opening details for appointment:', {
      id: appointment.id,
      staffId: appointment.staffId,
      staffName: appointment.staffName,
      useRoundRobin: appointment.useRoundRobin
    });

    setSelectedAppointment(appointment);
    setIsDetailsDialogOpen(true);
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Badge className="bg-blue-500 text-white hover:bg-blue-600">Scheduled</Badge>;
      case 'confirmed':
        return <Badge className="bg-green-500 text-white hover:bg-green-600">Confirmed</Badge>;
      case 'completed':
        return <Badge className="bg-green-500 text-white hover:bg-green-600">Completed</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-500 text-white hover:bg-red-600">Cancelled</Badge>;
      case 'no-show':
        return <Badge className="bg-amber-500 text-white hover:bg-amber-600">No-show</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Group appointments by date (only if we have filtered appointments)
  const appointmentsByDate = filteredAppointments.length > 0
    ? filteredAppointments.reduce((acc, appointment) => {
        // Convert Firestore timestamp to Date
        const date = appointment.startTime instanceof Date
          ? appointment.startTime
          : appointment.startTime.toDate();

        const dateStr = date.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });

        if (!acc[dateStr]) {
          acc[dateStr] = [];
        }

        acc[dateStr].push(appointment);
        return acc;
      }, {} as Record<string, Appointment[]>)
    : {};

  // Now we can render based on our state
  // All hooks have been called by this point

  // First check loading state
  if (isLoading) {
    return (
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-bold">{showPastAppointments ? 'Past Appointments' : 'Upcoming Appointments'}</h2>
            <p className="text-sm text-muted-foreground">
              {showPastAppointments
                ? 'Your appointment history will appear here'
                : 'Your scheduled appointments will appear here'}
            </p>
          </div>
          <div className="flex items-center justify-center h-40 border rounded-lg bg-card p-6">
            <div className="flex flex-col items-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-muted-foreground">Loading appointments...</p>
            </div>
          </div>
        </div>
    );
  }

  // Then check for errors
  if (error) {
     return (
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-bold">{showPastAppointments ? 'Past Appointments' : 'Upcoming Appointments'}</h2>
            <p className="text-sm text-muted-foreground">There was a problem loading your appointments</p>
          </div>
          <div className="border rounded-lg bg-card p-6">
            <div className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <p>Error: {error.message}</p>
            </div>
          </div>
        </div>
    );
  }

  // Check if we have any appointments after filtering
  if (!filteredAppointments || filteredAppointments.length === 0) {
     return (
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-bold">{showPastAppointments ? 'Past Appointments' : 'Upcoming Appointments'}</h2>
            <p className="text-sm text-muted-foreground">
              {showPastAppointments
                ? 'You don\'t have any past appointments'
                : 'You don\'t have any appointments scheduled'}
            </p>
          </div>
          <div className="border rounded-lg bg-card p-6">
            <div className="flex flex-col items-center justify-center py-10 text-center">
              <Calendar className="h-16 w-16 text-muted-foreground mb-6 opacity-20" />
              <p className="text-muted-foreground text-lg mb-2">
                {showPastAppointments
                  ? 'No past appointments found.'
                  : 'No upcoming appointments found.'}
              </p>
              <p className="text-sm text-muted-foreground mb-6">
                {showPastAppointments
                  ? 'Your appointment history will appear here'
                  : 'Your schedule is clear for now'}
              </p>
              {!showPastAppointments && (
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button variant="outline" size="lg" onClick={() => window.location.href = '/dashboard/schedule/new'}>
                    Advanced Booking
                  </Button>
                  <Button size="lg" onClick={() => onAppointmentUpdated?.()}>
                    Quick Add
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
    );
  }

  // We already created appointmentsByDate above

  return (
    <>
      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-bold">{showPastAppointments ? 'Past Appointments' : 'Upcoming Appointments'}</h2>
          <p className="text-sm text-muted-foreground">
            {showPastAppointments
              ? 'View your appointment history'
              : 'Manage your scheduled appointments'}
          </p>
        </div>
        <div className="space-y-8">
          {Object.entries(appointmentsByDate).map(([dateStr, dateAppointments]) => (
            <div key={dateStr} className="space-y-4">
              <h3 className="text-base font-semibold text-foreground">{dateStr}</h3>
              <div className="space-y-4">
                {dateAppointments.map((appt) => {
                  const service = serviceMap.get(appt.serviceId);
                  const staff = appt.staffId ? staffMap.get(appt.staffId) : null;

                  // Debug logging
                  console.log(`Appointment ${appt.id}: staffId=${appt.staffId}, staffName=${appt.staffName}, useRoundRobin=${appt.useRoundRobin}`);
                  const startTime = appt.startTime instanceof Date
                    ? appt.startTime
                    : appt.startTime.toDate();
                  const formattedTime = startTime.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                  });

                  return (
                    <div
                      key={appt.id}
                      className={`flex flex-col sm:flex-row justify-between p-3 sm:p-5 border rounded-md mb-4 ${appt.status === 'cancelled' ? 'bg-muted/50' : 'bg-background hover:bg-muted/30'}`}
                    >
                      <div className="flex items-start gap-2 sm:gap-3 mb-3 sm:mb-0">
                        <div className="flex-shrink-0 w-1 h-full self-stretch"
                          style={{ backgroundColor: service?.color || '#4f46e5' }}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex flex-wrap items-center gap-2 mb-2">
                            <p className="font-medium text-base sm:text-lg truncate">{service?.name || 'Unknown Service'}</p>
                            {getStatusBadge(appt.status)}
                          </div>
                          <div className="space-y-2 text-xs sm:text-sm">
                            <div className="flex flex-wrap items-center gap-1 text-muted-foreground">
                              <Clock className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                              <span className="text-sm sm:text-base">{formattedTime}</span>
                              <span className="mx-1">•</span>
                              <span className="text-sm sm:text-base">{service?.durationMinutes || 60} min</span>
                              {(appt.revenue || appt.price) && (
                                <>
                                  <span className="mx-1">•</span>
                                  <span>${(appt.revenue ?? appt.price ?? 0).toFixed(2)}</span>
                                  {appt.paymentStatus && (
                                    <Badge variant="outline" className={`ml-2 text-xs ${appt.paymentStatus === 'paid' ? 'bg-green-50 text-green-700 border-green-200' : 'bg-amber-50 text-amber-700 border-amber-200'}`}>
                                      {appt.paymentStatus.charAt(0).toUpperCase() + appt.paymentStatus.slice(1)}
                                    </Badge>
                                  )}
                                </>
                              )}
                            </div>
                            <div className="flex items-center gap-1 mt-1">
                              <User className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground flex-shrink-0" />
                              <span className="text-sm sm:text-base truncate">{appt.clientName}</span>
                            </div>
                            <div className="flex items-center gap-1 text-muted-foreground mt-1">
                              <span className="text-xs sm:text-sm">with</span>
                              {(() => {
                                // Debug the values
                                console.log(`Rendering appointment ${appt.id} staff info:`, {
                                  staff,
                                  staffName: appt.staffName,
                                  useRoundRobin: appt.useRoundRobin,
                                  useRoundRobinType: typeof appt.useRoundRobin
                                });

                                // Check if useRoundRobin is true (handle string 'true' as well)
                                const isRoundRobin =
                                  (typeof appt.useRoundRobin === 'boolean' && appt.useRoundRobin === true) ||
                                  (typeof appt.useRoundRobin === 'string' && appt.useRoundRobin === 'true');

                                // Get staff name if available
                                let staffName = null;

                                // First try to get from staff object
                                if (staff) {
                                  staffName = staff.name;
                                }
                                // Otherwise use staffName if it's not 'Unassigned'
                                else if (appt.staffName && appt.staffName !== 'Unassigned') {
                                  staffName = appt.staffName;
                                }

                                // Debug the staff name
                                console.log(`Staff name for appointment ${appt.id}:`, {
                                  staffName,
                                  originalStaffName: appt.staffName
                                });

                                if (isRoundRobin) {
                                  return (
                                    <div className="flex flex-col">
                                      <span className="text-xs sm:text-sm text-primary">Auto-assigned</span>
                                      {staffName && (
                                        <span className="font-medium text-sm sm:text-base truncate">{staffName}</span>
                                      )}
                                    </div>
                                  );
                                } else if (staffName) {
                                  return <span className="font-medium text-sm sm:text-base truncate">{staffName}</span>;
                                } else {
                                  return <span className="font-medium text-sm sm:text-base">Unassigned</span>;
                                }
                              })()}
                            </div>

                            {/* Google Calendar indicators */}
                            {(appt.googleCalendarEventId || appt.staffGoogleCalendarEventId) && (
                              <div className="space-y-1 mt-2">
                                {/* Business Google Calendar */}
                                {appt.googleCalendarEventId && (
                                  <div className="flex items-center gap-1 text-green-600">
                                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                    <span className="text-xs sm:text-sm">Business Calendar</span>
                                    {appt.googleCalendarLink && (
                                      <Button
                                        variant="link"
                                        className="p-0 h-auto text-xs text-green-600 underline"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          window.open(appt.googleCalendarLink, '_blank');
                                        }}
                                      >
                                        View
                                      </Button>
                                    )}
                                  </div>
                                )}

                                {/* Staff Google Calendar */}
                                {appt.staffGoogleCalendarEventId && (
                                  <div className="flex items-center gap-1 text-blue-600">
                                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                    <span className="text-xs sm:text-sm">Staff Calendar</span>
                                    {appt.staffGoogleCalendarLink && (
                                      <Button
                                        variant="link"
                                        className="p-0 h-auto text-xs text-blue-600 underline"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          window.open(appt.staffGoogleCalendarLink, '_blank');
                                        }}
                                      >
                                        View
                                      </Button>
                                    )}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-wrap items-center justify-end gap-2 sm:gap-3 mt-3 sm:mt-0">
                        <div className="hidden sm:block">
                          <AppointmentRescheduleButton
                            appointment={appt}
                            size="sm"
                            variant="outline"
                            onReschedule={() => onAppointmentUpdated?.()}
                            googleAccessToken={googleAccessToken}
                            businessTimezone={businessTimezone}
                          />
                        </div>
                        <AppointmentStatusButton
                          appointmentId={appt.id}
                          currentStatus={appt.status}
                          size="sm"
                          onStatusChange={() => onAppointmentUpdated?.()}
                        />
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8 sm:h-10 sm:w-10">
                              <MoreHorizontal className="h-4 w-4 sm:h-5 sm:w-5" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuItem onClick={() => openDetailsDialog(appt)} className="py-2">
                              <FileText className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            {appt.status !== 'cancelled' && appt.status !== 'no-show' && appt.status !== 'completed' && (
                              <DropdownMenuItem onClick={() => openRescheduleDialog(appt)} className="py-2">
                                <Calendar className="h-4 w-4 mr-2" />
                                Reschedule
                              </DropdownMenuItem>
                            )}
                            {appt.status !== 'cancelled' && appt.status !== 'no-show' && appt.status !== 'completed' && (
                              <DropdownMenuItem
                                onClick={() => openCancelDialog(appt)}
                                className="text-destructive focus:text-destructive py-2"
                              >
                                <X className="h-4 w-4 mr-2" />
                                Cancel
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Reschedule Dialog */}
      {selectedAppointment && (
        <Dialog open={isRescheduleDialogOpen} onOpenChange={setIsRescheduleDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reschedule Appointment</DialogTitle>
              <DialogDescription>
                Update the date and time for this appointment.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="reschedule-date">New Date</Label>
                <Input
                  id="reschedule-date"
                  type="date"
                  value={rescheduleData.date}
                  onChange={(e) => setRescheduleData({ ...rescheduleData, date: e.target.value })}
                  min={new Date().toISOString().split('T')[0]}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="reschedule-time">New Time</Label>
                <Input
                  id="reschedule-time"
                  type="time"
                  value={rescheduleData.time}
                  onChange={(e) => setRescheduleData({ ...rescheduleData, time: e.target.value })}
                  required
                />
              </div>
              {selectedAppointment.googleCalendarEventId && (
                <div className="flex items-center space-x-2 pt-2">
                  <Checkbox
                    id="update-google-calendar"
                    checked={rescheduleData.updateGoogleCalendar}
                    onCheckedChange={(checked) =>
                      setRescheduleData({ ...rescheduleData, updateGoogleCalendar: !!checked })
                    }
                  />
                  <Label htmlFor="update-google-calendar" className="font-normal">
                    Update in Google Calendar
                  </Label>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsRescheduleDialogOpen(false)}
                disabled={isActionLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleRescheduleAppointment}
                disabled={isActionLoading}
              >
                {isActionLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Rescheduling...
                  </>
                ) : (
                  'Reschedule'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Cancel Dialog */}
      {selectedAppointment && (
        <Dialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Cancel Appointment</DialogTitle>
              <DialogDescription>
                Are you sure you want to cancel this appointment? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="cancel-reason">Reason for Cancellation (Optional)</Label>
                <Textarea
                  id="cancel-reason"
                  placeholder="Please provide a reason for cancellation..."
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                />
              </div>
              {selectedAppointment.googleCalendarEventId && (
                <div className="pt-2 text-sm text-muted-foreground">
                  This appointment will also be removed from Google Calendar.
                </div>
              )}
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsCancelDialogOpen(false)}
                disabled={isActionLoading}
              >
                Keep Appointment
              </Button>
              <Button
                variant="destructive"
                onClick={handleCancelAppointment}
                disabled={isActionLoading}
              >
                {isActionLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Cancelling...
                  </>
                ) : (
                  'Cancel Appointment'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Details Dialog */}
      {selectedAppointment && (
        <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Appointment Details</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Service</h4>
                  <p>{serviceMap.get(selectedAppointment.serviceId)?.name || 'Unknown Service'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Status</h4>
                  <p>{getStatusBadge(selectedAppointment.status)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Date & Time</h4>
                  <p>{formatDateTimeReadable(selectedAppointment.startTime, businessTimezone)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Duration</h4>
                  <p>{serviceMap.get(selectedAppointment.serviceId)?.durationMinutes || 60} minutes</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Client</h4>
                  <p>{selectedAppointment.clientName}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Contact</h4>
                  <p>{selectedAppointment.clientEmail}</p>
                  {selectedAppointment.clientPhone && <p>{selectedAppointment.clientPhone}</p>}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-1">Staff Member</h4>
                {(() => {
                  // Debug the values
                  console.log(`Rendering details dialog staff info:`, {
                    staffId: selectedAppointment.staffId,
                    staffName: selectedAppointment.staffName,
                    useRoundRobin: selectedAppointment.useRoundRobin,
                    useRoundRobinType: typeof selectedAppointment.useRoundRobin
                  });

                  // Check if useRoundRobin is true (handle string 'true' as well)
                  const isRoundRobin =
                    selectedAppointment.useRoundRobin === true ||
                    (typeof selectedAppointment.useRoundRobin === 'string' && selectedAppointment.useRoundRobin === 'true');

                  // Get staff name if available
                  let staffName = null;

                  // First try to get from staffMap if staffId exists
                  if (selectedAppointment.staffId && staffMap.get(selectedAppointment.staffId)) {
                    staffName = staffMap.get(selectedAppointment.staffId)?.name;
                  }
                  // Otherwise use staffName if it's not 'Unassigned'
                  else if (selectedAppointment.staffName && selectedAppointment.staffName !== 'Unassigned') {
                    staffName = selectedAppointment.staffName;
                  }

                  if (isRoundRobin) {
                    return (
                      <div>
                        <p className="text-sm font-medium text-primary">Auto-assigned (Round Robin)</p>
                        {staffName ? (
                          <p>Assigned to: {staffName}</p>
                        ) : (
                          <p className="text-sm text-muted-foreground">No staff member available at this time</p>
                        )}
                      </div>
                    );
                  } else if (staffName) {
                    return <p>{staffName}</p>;
                  } else {
                    return <p>Unassigned</p>;
                  }
                })()}
              </div>
              {selectedAppointment.notes && (
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Notes</h4>
                  <p className="whitespace-pre-wrap">{selectedAppointment.notes}</p>
                </div>
              )}
              {/* Payment Information */}
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-1">Payment</h4>
                <div className="flex items-center justify-between">
                  <div>
                    <p>
                      {selectedAppointment.revenue ?
                        `$${selectedAppointment.revenue.toFixed(2)}` :
                        selectedAppointment.price ?
                          `$${selectedAppointment.price.toFixed(2)}` :
                          'No price set'
                      }
                      {selectedAppointment.paymentStatus && (
                        <Badge variant="outline" className={`ml-2 ${selectedAppointment.paymentStatus === 'paid' ? 'bg-green-50 text-green-700 border-green-200' : 'bg-amber-50 text-amber-700 border-amber-200'}`}>
                          {selectedAppointment.paymentStatus.charAt(0).toUpperCase() + selectedAppointment.paymentStatus.slice(1)}
                        </Badge>
                      )}
                    </p>
                  </div>
                  {selectedAppointment.status === 'completed' && selectedAppointment.paymentStatus !== 'paid' && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100"
                      disabled={isActionLoading}
                      onClick={async () => {
                        try {
                          setIsActionLoading(true);
                          await updateAppointmentPaymentStatus(selectedAppointment.id, 'paid');
                          toast.success('Payment status updated to Paid');
                          setIsDetailsDialogOpen(false);
                          // Only call onAppointmentUpdated if openAddDialogOnUpdate is true or we're not in the staff view
                          if (onAppointmentUpdated && openAddDialogOnUpdate) {
                            onAppointmentUpdated();
                          } else {
                            // If we don't want to open the add dialog, just refresh the data
                            window.location.reload();
                          }
                        } catch (error) {
                          console.error('Error updating payment status:', error);
                          toast.error(`Error updating payment status: ${error instanceof Error ? error.message : 'Unknown error'}`);
                        } finally {
                          setIsActionLoading(false);
                        }
                      }}
                    >
                      {isActionLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        'Mark as Paid'
                      )}
                    </Button>
                  )}
                </div>
              </div>

              {selectedAppointment.googleCalendarEventId && (
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Google Calendar</h4>
                  <p className="text-sm">This appointment is synced with Google Calendar</p>
                </div>
              )}
              {selectedAppointment.status === 'cancelled' && selectedAppointment.cancelReason && (
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">Cancellation Reason</h4>
                  <p className="whitespace-pre-wrap">{selectedAppointment.cancelReason}</p>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button onClick={() => setIsDetailsDialogOpen(false)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
