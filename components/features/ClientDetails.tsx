'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import { Client, ClientCategory, ClientNote, Appointment } from '@/lib/types/db';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ArrowLeft,
  Calendar,
  Clock,
  Edit,
  Mail,
  MapPin,
  Phone,
  Plus,
  Tag,
  Trash2,
  User,
  FileText,
  DollarSign,
  Calendar as CalendarIcon,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { Timestamp } from 'firebase/firestore';
import { createClientNote, deleteClientNote } from '@/lib/services/clientService';
import { toast } from 'sonner';

interface ClientDetailsProps {
  client: Client;
  categories: ClientCategory[];
  notes: ClientNote[];
  appointments: Appointment[];
  staffNames: Record<string, string>;
  serviceNames: Record<string, string>;
  onEditClient: () => void;
  onDeleteClient: () => void;
  onRefreshData: () => void;
  onAddNote?: (note: string, isPrivate: boolean) => Promise<void>;
  isStaffView?: boolean;
}

export function ClientDetails({
  client,
  categories,
  notes,
  appointments,
  staffNames,
  serviceNames,
  onEditClient,
  onDeleteClient,
  onRefreshData,
  onAddNote,
  isStaffView = false
}: ClientDetailsProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [newNote, setNewNote] = useState('');
  const [isPrivateNote, setIsPrivateNote] = useState(true);
  const [isAddingNote, setIsAddingNote] = useState(false);

  // Format date
  const formatDate = (timestamp: Timestamp | undefined) => {
    if (!timestamp) return 'Never';
    return format(timestamp.toDate(), 'MMM d, yyyy');
  };

  // Format date with time
  const formatDateTime = (timestamp: Timestamp | undefined) => {
    if (!timestamp) return '';
    return format(timestamp.toDate(), 'MMM d, yyyy h:mm a');
  };

  // Format currency
  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Get category name by ID
  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : '';
  };

  // Get category color by ID
  const getCategoryColor = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.color || '#888888';
  };

  // Handle adding a new note
  const handleAddNote = async () => {
    if (!newNote.trim()) return;

    setIsAddingNote(true);

    try {
      if (onAddNote) {
        // Use the provided onAddNote function if available
        await onAddNote(newNote.trim(), isPrivateNote);
      } else {
        // Fall back to the default implementation
        const noteData = {
          clientId: client.id,
          businessId: client.businessId,
          staffId: user?.uid || '',
          content: newNote.trim(),
          isPrivate: isPrivateNote
        };

        await createClientNote(noteData);
      }

      setNewNote('');
      onRefreshData();
      toast.success('Note added successfully');
    } catch (error) {
      console.error('Error adding note:', error);
      toast.error('Failed to add note');
    } finally {
      setIsAddingNote(false);
    }
  };

  // Handle deleting a note
  const handleDeleteNote = async (noteId: string) => {
    try {
      await deleteClientNote(noteId);
      onRefreshData();
      toast.success('Note deleted successfully');
    } catch (error) {
      console.error('Error deleting note:', error);
      toast.error('Failed to delete note');
    }
  };

  // Get appointment status badge
  const getStatusBadge = (status: string, isMobile: boolean = false) => {
    const iconSize = isMobile ? "h-2.5 w-2.5" : "h-3 w-3";
    const textSize = isMobile ? "text-xs" : "";
    const paddingClass = isMobile ? "py-0.5 px-1.5" : "";

    switch (status) {
      case 'completed':
        return (
          <Badge variant="success" className={`flex items-center ${paddingClass} ${textSize}`}>
            <CheckCircle className={`${iconSize} mr-1`} />
            Completed
          </Badge>
        );
      case 'scheduled':
        return (
          <Badge variant="outline" className={`flex items-center ${paddingClass} ${textSize}`}>
            <Calendar className={`${iconSize} mr-1`} />
            Scheduled
          </Badge>
        );
      case 'confirmed':
        return (
          <Badge variant="default" className={`flex items-center ${paddingClass} ${textSize}`}>
            <CheckCircle className={`${iconSize} mr-1`} />
            Confirmed
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="destructive" className={`flex items-center ${paddingClass} ${textSize}`}>
            <XCircle className={`${iconSize} mr-1`} />
            Cancelled
          </Badge>
        );
      case 'no-show':
        return (
          <Badge variant="destructive" className={`flex items-center ${paddingClass} ${textSize}`}>
            <AlertCircle className={`${iconSize} mr-1`} />
            No-show
          </Badge>
        );
      default:
        return <Badge variant="secondary" className={`${paddingClass} ${textSize}`}>{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-2 sm:mb-0">
        <Button variant="ghost" onClick={() => router.back()} className="gap-1 h-9 px-2 sm:h-10 sm:px-3">
          <ArrowLeft className="h-4 w-4" />
          <span className="text-sm">Back</span>
        </Button>
        {!isStaffView && (
          <div className="flex gap-1 sm:gap-2">
            <Button variant="outline" size="sm" className="text-xs sm:text-sm h-9 sm:h-10" onClick={onEditClient}>
              <Edit className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Edit Client</span>
              <span className="sm:hidden">Edit</span>
            </Button>
            <Button variant="destructive" size="sm" className="text-xs sm:text-sm h-9 sm:h-10" onClick={onDeleteClient}>
              <Trash2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden sm:inline">Delete</span>
              <span className="sm:hidden">Delete</span>
            </Button>
          </div>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card className="md:col-span-1">
          <CardHeader className="pb-3 sm:pb-6">
            <CardTitle className="text-lg sm:text-xl">{client.name}</CardTitle>
            <CardDescription>Client Profile</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3 sm:space-y-4 text-sm sm:text-base">
            <div className="flex flex-col gap-3">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{client.email}</span>
              </div>
              {client.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{client.phone}</span>
                </div>
              )}
              {client.address && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{client.address}</span>
                </div>
              )}
              {client.birthday && (
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                  <span>Birthday: {formatDate(client.birthday)}</span>
                </div>
              )}
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium mb-2">Categories</h3>
              <div className="flex flex-wrap gap-1">
                {client.categoryIds?.map((categoryId) => (
                  <Badge
                    key={categoryId}
                    variant="outline"
                    style={{
                      borderColor: getCategoryColor(categoryId),
                      color: getCategoryColor(categoryId)
                    }}
                  >
                    {getCategoryName(categoryId)}
                  </Badge>
                ))}
                {(!client.categoryIds || client.categoryIds.length === 0) && (
                  <span className="text-sm text-muted-foreground">No categories</span>
                )}
              </div>
            </div>

            {client.tags && client.tags.length > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-2">Tags</h3>
                <div className="flex flex-wrap gap-1">
                  {client.tags.map((tag) => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="flex items-center"
                    >
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <Separator />

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Client Statistics</h3>
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-muted rounded-md p-2">
                  <div className="text-xs text-muted-foreground">Total Appointments</div>
                  <div className="text-base sm:text-lg font-medium">{client.totalAppointments || 0}</div>
                </div>
                <div className="bg-muted rounded-md p-2">
                  <div className="text-xs text-muted-foreground">Total Spent</div>
                  <div className="text-base sm:text-lg font-medium">{formatCurrency(client.totalSpent)}</div>
                </div>
                <div className="bg-muted rounded-md p-2">
                  <div className="text-xs text-muted-foreground">Last Visit</div>
                  <div className="text-xs sm:text-sm font-medium">{formatDate(client.lastAppointment)}</div>
                </div>
                <div className="bg-muted rounded-md p-2">
                  <div className="text-xs text-muted-foreground">Average Spend</div>
                  <div className="text-base sm:text-lg font-medium">{formatCurrency(client.averageSpend)}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="md:col-span-2 space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview" className="text-xs sm:text-sm py-1.5 px-1 sm:px-2">Overview</TabsTrigger>
              <TabsTrigger value="appointments" className="text-xs sm:text-sm py-1.5 px-1 sm:px-2">Appointments</TabsTrigger>
              <TabsTrigger value="notes" className="text-xs sm:text-sm py-1.5 px-1 sm:px-2">Notes</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Client Overview</CardTitle>
                  <CardDescription>Summary of client activity and preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {client.notes && (
                    <div>
                      <h3 className="text-sm font-medium mb-2">Client Notes</h3>
                      <p className="text-sm text-muted-foreground">{client.notes}</p>
                    </div>
                  )}

                  <div>
                    <h3 className="text-sm font-medium mb-2">Preferred Staff</h3>
                    <div className="flex flex-wrap gap-1">
                      {client.preferredStaffIds?.map((staffId) => (
                        <Badge key={staffId} variant="outline">
                          {staffNames[staffId] || 'Unknown Staff'}
                        </Badge>
                      ))}
                      {(!client.preferredStaffIds || client.preferredStaffIds.length === 0) && (
                        <span className="text-sm text-muted-foreground">No preferred staff</span>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Preferred Services</h3>
                    <div className="flex flex-wrap gap-1">
                      {client.preferredServices?.map((serviceId) => (
                        <Badge key={serviceId} variant="outline">
                          {serviceNames[serviceId] || 'Unknown Service'}
                        </Badge>
                      ))}
                      {(!client.preferredServices || client.preferredServices.length === 0) && (
                        <span className="text-sm text-muted-foreground">No preferred services</span>
                      )}
                    </div>
                  </div>

                  {client.communicationPreferences && (
                    <div>
                      <h3 className="text-sm font-medium mb-2">Communication Preferences</h3>
                      <div className="grid grid-cols-3 gap-2">
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={client.communicationPreferences.email}
                            onCheckedChange={() => {}}
                            disabled
                          />
                          <Label>Email</Label>
                        </div>
                          <Switch
                            checked={client.communicationPreferences.sms}
                            onCheckedChange={() => {}}
                            disabled
                          />
                          <Label>SMS</Label>
                          <Switch
                            checked={client.communicationPreferences.push}
                            onCheckedChange={() => {}}
                            disabled
                          />
                          <Label>Push</Label>
                        </div>
                      </div>
                  )}
                </CardContent>
              </Card>

              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle>Recent Appointments</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {appointments.length > 0 ? (
                      <div className="space-y-2">
                        {appointments.slice(0, 3).map((appointment) => (
                          <div
                            key={appointment.id}
                            className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-2 rounded-md hover:bg-muted border"
                          >
                            <div className="mb-2 sm:mb-0">
                              <div className="font-medium text-sm">{serviceNames[appointment.serviceId] || 'Unknown Service'}</div>
                              <div className="text-xs text-muted-foreground">
                                {formatDateTime(appointment.startTime)}
                              </div>
                            </div>
                            <div className="self-end sm:self-auto">
                              {getStatusBadge(appointment.status, true)}
                            </div>
                          </div>
                        ))}
                        {appointments.length > 3 && (
                          <Button
                            variant="link"
                            className="px-0"
                            onClick={() => setActiveTab('appointments')}
                          >
                            View all {appointments.length} appointments
                          </Button>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <Calendar className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground">No appointments yet</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle>Recent Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {notes.length > 0 ? (
                      <div className="space-y-2">
                        {notes.slice(0, 3).map((note) => (
                          <div
                            key={note.id}
                            className="p-2 rounded-md hover:bg-muted"
                          >
                            <div className="text-sm">{note.content}</div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {formatDateTime(note.createdAt)}
                              {note.isPrivate && (
                                <span className="ml-2 text-amber-500">(Private)</span>
                              )}
                            </div>
                          </div>
                        ))}
                        {notes.length > 3 && (
                          <Button
                            variant="link"
                            className="px-0"
                            onClick={() => setActiveTab('notes')}
                          >
                            View all {notes.length} notes
                          </Button>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <FileText className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground">No notes yet</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="appointments" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Appointment History</CardTitle>
                  <CardDescription>All appointments for this client</CardDescription>
                </CardHeader>
                <CardContent>
                  {appointments.length > 0 ? (
                    <>
                      {/* Desktop view - standard table */}
                      <div className="hidden md:block">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date & Time</TableHead>
                              <TableHead>Service</TableHead>
                              <TableHead>Staff</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Price</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {appointments.map((appointment) => (
                              <TableRow key={appointment.id}>
                                <TableCell>
                                  <div className="font-medium">
                                    {formatDate(appointment.startTime)}
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {appointment.startTime && format(appointment.startTime.toDate(), 'h:mm a')}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {serviceNames[appointment.serviceId] || appointment.serviceName || 'Unknown Service'}
                                </TableCell>
                                <TableCell>
                                  {staffNames[appointment.staffId || ''] || appointment.staffName || 'Unassigned'}
                                </TableCell>
                                <TableCell>
                                  {getStatusBadge(appointment.status)}
                                </TableCell>
                                <TableCell className="text-right">
                                  {formatCurrency(appointment.price)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>

                      {/* Mobile view - card-based layout */}
                      <div className="md:hidden space-y-4">
                        <div className="grid grid-cols-4 gap-2 px-2 mb-2 text-xs font-medium text-muted-foreground">
                          <div className="col-span-2">Date & Time</div>
                          <div className="col-span-1">Service</div>
                          <div className="col-span-1">Staff</div>
                        </div>

                        {appointments.map((appointment) => (
                          <div key={appointment.id} className="border rounded-md p-3 space-y-3">
                            <div className="grid grid-cols-4 gap-2 items-center">
                              <div className="col-span-2">
                                <div className="font-medium text-sm">
                                  {formatDate(appointment.startTime)}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {appointment.startTime && format(appointment.startTime.toDate(), 'h:mm a')}
                                </div>
                              </div>
                              <div className="col-span-1 text-sm truncate">
                                {serviceNames[appointment.serviceId] || appointment.serviceName || 'Unknown'}
                              </div>
                              <div className="col-span-1 text-sm truncate">
                                {staffNames[appointment.staffId || ''] || appointment.staffName || 'Unassigned'}
                              </div>
                            </div>

                            <div className="flex justify-between items-center pt-2 border-t">
                              <div>
                                {getStatusBadge(appointment.status, true)}
                              </div>
                              <div className="font-medium text-sm">
                                {formatCurrency(appointment.price)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium">No appointments yet</h3>
                      <p className="text-sm text-muted-foreground mt-1 mb-4">
                        This client hasn't booked any appointments yet.
                      </p>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Book Appointment
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notes" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Add Note</CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Add a note about this client..."
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    rows={3}
                  />
                  <div className="flex items-center mt-2">
                    <Switch
                      id="private-note"
                      checked={isPrivateNote}
                      onCheckedChange={setIsPrivateNote}
                    />
                    <Label htmlFor="private-note" className="ml-2">
                      Private note (not visible to client)
                    </Label>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={handleAddNote}
                    disabled={!newNote.trim() || isAddingNote}
                  >
                    {isAddingNote && <div className="animate-spin mr-2">⏳</div>}
                    Add Note
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Client Notes</CardTitle>
                  <CardDescription>Notes and history for this client</CardDescription>
                </CardHeader>
                <CardContent>
                  {notes.length > 0 ? (
                    <div className="space-y-4">
                      {notes.map((note) => (
                        <div
                          key={note.id}
                          className="p-3 rounded-md border"
                        >
                          <div className="flex justify-between items-start">
                            <div className="text-sm font-medium">
                              {staffNames[note.staffId] || 'Staff Member'}
                              {note.isPrivate && (
                                <Badge variant="outline" className="ml-2">Private</Badge>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteNote(note.id)}
                            >
                              <Trash2 className="h-4 w-4 text-muted-foreground" />
                            </Button>
                          </div>
                          <div className="mt-1">{note.content}</div>
                          <div className="text-xs text-muted-foreground mt-2">
                            {formatDateTime(note.createdAt)}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium">No notes yet</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        Add your first note about this client above.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
