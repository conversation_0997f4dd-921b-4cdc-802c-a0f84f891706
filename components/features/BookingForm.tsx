// src/components/features/BookingForm.tsx
'use client'; // Needs client state and interaction

import React, { useState } from 'react';
import { Timestamp } from 'firebase/firestore';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner'; // Import from sonner instead of use-toast

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Toaster } from "@/components/ui/sonner";

import { createAppointment } from '@/lib/services/appointmentService';
import { Appointment, ServiceDefinition } from '@/lib/types/db';
import { formatDateTimeReadable } from '@/lib/utils/dateUtils';

interface BookingFormProps {
  businessId: string;
  businessTimezone: string;
  service: ServiceDefinition | null;
  selectedTime: Date;
  onBookingSuccess: () => void;
}

export function BookingForm({
  businessId,
  businessTimezone,
  service,
  selectedTime,
  onBookingSuccess
}: BookingFormProps) {
  const [clientName, setClientName] = useState('');
  const [clientEmail, setClientEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const bookingMutation = useMutation({
    mutationFn: (newAppointment: Omit<Appointment, 'id' | 'createdAt'>) => createAppointment(newAppointment),
    onSuccess: () => {
      toast.success('Appointment Booked!', {
        description: `Your appointment for ${service?.name} is confirmed.`,
      });
      onBookingSuccess();
      setClientName('');
      setClientEmail('');
    },
    onError: (error: Error) => {
      console.error("Booking failed:", error);
      toast.error('Booking Failed', {
        description: error?.message || "Could not book appointment. Please try again.",
      });
    },
    onSettled: () => {
      setIsLoading(false);
    }
  });

  if (!service) {
    return <Card><CardContent><p>Please select a service first.</p></CardContent></Card>;
  }

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    // Basic validation
    if (!clientName.trim()) {
      toast.error('Validation Error', {
        description: "Please enter your name",
      });
      return;
    }

    if (!clientEmail.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(clientEmail)) {
      toast.error('Validation Error', {
        description: "Please enter a valid email address",
      });
      return;
    }

    setIsLoading(true);

    const startTime = selectedTime;
    const endTime = new Date(startTime.getTime() + service.durationMinutes * 60000);

    const appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'> = {
      businessId: businessId,
      serviceId: service.id || '',
      clientName: clientName.trim(),
      clientEmail: clientEmail.trim().toLowerCase(),
      startTime: Timestamp.fromDate(startTime),
      endTime: Timestamp.fromDate(endTime),
      status: 'scheduled',
      serviceName: service.name
    };

    bookingMutation.mutate(appointmentData);
  };

  const formattedTime = formatDateTimeReadable(selectedTime, businessTimezone);

  return (
    <>
      <Toaster /> {/* Render the Toaster component */}
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Confirm Your Booking</CardTitle>
          <p className="text-sm text-muted-foreground">
            For {service.name} ({service.durationMinutes} mins)
            <br/>
            On {formattedTime} ({businessTimezone})
          </p>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="clientName">Full Name</Label>
              <Input
                id="clientName"
                value={clientName}
                onChange={(e) => setClientName(e.target.value)}
                placeholder="Jane Doe"
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="clientEmail">Email</Label>
              <Input
                id="clientEmail"
                type="email"
                value={clientEmail}
                onChange={(e) => setClientEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
                disabled={isLoading}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Booking...' : 'Book Appointment'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </>
  );
}