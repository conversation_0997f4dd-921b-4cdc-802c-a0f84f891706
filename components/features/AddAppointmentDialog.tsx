'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2 } from 'lucide-react';
import { ServiceDefinition, StaffMember, Appointment } from '@/lib/types/db';
import { formatDateForInput } from '@/lib/utils/dateUtils';
import { Timestamp } from 'firebase/firestore';

interface AddAppointmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAddAppointment: (appointmentData: any) => Promise<void>;
  services: ServiceDefinition[];
  staffMembers: StaffMember[];
  businessId: string;
}

export function AddAppointmentDialog({
  isOpen,
  onClose,
  onAddAppointment,
  services,
  staffMembers,
  businessId,
}: AddAppointmentDialogProps) {
  const [formData, setFormData] = useState({
    clientName: '',
    clientEmail: '',
    clientPhone: '',
    serviceId: '',
    staffId: '',
    startTime: formatDateForInput(new Date(Date.now() + 24 * 60 * 60 * 1000)), // Default to tomorrow
    notes: '',
    addToGoogleCalendar: true,
    useRoundRobin: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.serviceId) {
      setError('Please select a service');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Find the selected service to get duration
      const selectedService = services.find(s => s.id === formData.serviceId);
      if (!selectedService) {
        throw new Error('Selected service not found');
      }

      // Calculate end time based on service duration
      const startTime = new Date(formData.startTime);
      const endTime = new Date(startTime.getTime() + (selectedService.durationMinutes * 60 * 1000));

      // Create appointment data
      const appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'> = {
        businessId,
        clientName: formData.clientName,
        clientEmail: formData.clientEmail,
        serviceId: formData.serviceId,
        startTime: Timestamp.fromDate(startTime),
        endTime: Timestamp.fromDate(endTime),
        status: 'scheduled',
        useRoundRobin: formData.useRoundRobin,
      };

      // Add staff information if not using round-robin
      if (!formData.useRoundRobin && formData.staffId) {
        appointmentData.staffId = formData.staffId;

        // Get staff details for Google Calendar
        const selectedStaff = staffMembers.find(s => s.id === formData.staffId);
        if (selectedStaff) {
          appointmentData.staffName = selectedStaff.name;
          appointmentData.staffEmail = selectedStaff.email;
        }
      }

      // Add service name
      if (selectedService) {
        appointmentData.serviceName = selectedService.name;
      }

      // Only add optional fields if they have values
      if (formData.clientPhone && formData.clientPhone.trim() !== '') {
        appointmentData.clientPhone = formData.clientPhone;
      }

      if (formData.notes && formData.notes.trim() !== '') {
        appointmentData.notes = formData.notes;
      }

      // Handle Google Calendar integration flag
      // Note: addToGoogleCalendar is not part of the Appointment interface
      // It's handled by the parent component or service layer

      // Call the parent component's onAddAppointment function
      await onAddAppointment(appointmentData);

      // Reset form and close dialog
      setFormData({
        clientName: '',
        clientEmail: '',
        clientPhone: '',
        serviceId: '',
        staffId: '',
        startTime: formatDateForInput(new Date(Date.now() + 24 * 60 * 60 * 1000)),
        notes: '',
        addToGoogleCalendar: true,
        useRoundRobin: false,
      });

      onClose();
    } catch (err) {
      console.error('Error adding appointment:', err);
      setError(err instanceof Error ? err.message : 'Failed to add appointment');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add New Appointment</DialogTitle>
          <DialogDescription>
            Create a new appointment for your client. All fields marked with * are required.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="clientName">Client Name *</Label>
              <Input
                id="clientName"
                name="clientName"
                value={formData.clientName}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="clientEmail">Client Email *</Label>
              <Input
                id="clientEmail"
                name="clientEmail"
                type="email"
                value={formData.clientEmail}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="clientPhone">Client Phone</Label>
              <Input
                id="clientPhone"
                name="clientPhone"
                type="tel"
                value={formData.clientPhone}
                onChange={handleChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="startTime">Date & Time *</Label>
              <Input
                id="startTime"
                name="startTime"
                type="datetime-local"
                value={formData.startTime}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="service">Service *</Label>
              <Select
                value={formData.serviceId}
                onValueChange={(value) => handleSelectChange('serviceId', value)}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a service" />
                </SelectTrigger>
                <SelectContent>
                  {services.map((service) => (
                    <SelectItem key={service.id} value={service.id}>
                      {service.name} ({service.durationMinutes} min)
                    </SelectItem>
                  ))}
                  {services.length === 0 && (
                    <div className="px-2 py-1 text-sm text-muted-foreground">
                      No services available
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="staff">Staff Member</Label>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="useRoundRobin"
                    checked={formData.useRoundRobin}
                    onCheckedChange={(checked) => handleCheckboxChange('useRoundRobin', checked === true)}
                  />
                  <Label htmlFor="useRoundRobin" className="text-sm font-normal">
                    Use Round Robin
                  </Label>
                </div>
              </div>
              <Select
                value={formData.staffId}
                onValueChange={(value) => handleSelectChange('staffId', value)}
                disabled={formData.useRoundRobin}
              >
                <SelectTrigger>
                  <SelectValue placeholder={formData.useRoundRobin ? "Auto-assigned" : "Select staff"} />
                </SelectTrigger>
                <SelectContent>
                  {staffMembers.map((staff) => (
                    <SelectItem key={staff.id} value={staff.id}>
                      {staff.name} ({staff.role})
                    </SelectItem>
                  ))}
                  {staffMembers.length === 0 && (
                    <div className="px-2 py-1 text-sm text-muted-foreground">
                      No staff members available
                    </div>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Input
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="addToGoogleCalendar"
              checked={formData.addToGoogleCalendar}
              onCheckedChange={(checked) => handleCheckboxChange('addToGoogleCalendar', checked === true)}
            />
            <Label htmlFor="addToGoogleCalendar" className="font-normal">
              Add to Google Calendar
            </Label>
          </div>

          {error && (
            <div className="text-sm font-medium text-destructive">{error}</div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Appointment'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
