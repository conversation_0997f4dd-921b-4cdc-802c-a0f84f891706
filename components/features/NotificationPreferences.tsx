'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Badge } from '@/components/ui/badge';
import { Calendar, Mail, CheckCircle, AlertCircle } from 'lucide-react';

interface NotificationPreferencesProps {
  value: 'google_calendar' | 'email_ics';
  onChange: (value: 'google_calendar' | 'email_ics') => void;
  googleCalendarConnected: boolean;
  disabled?: boolean;
}

export function NotificationPreferences({
  value,
  onChange,
  googleCalendarConnected,
  disabled = false
}: NotificationPreferencesProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Client Notification Method
        </CardTitle>
        <CardDescription>
          Choose how the client will receive their appointment confirmation and calendar event.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <RadioGroup
          value={value}
          onValueChange={onChange}
          disabled={disabled}
          className="space-y-4"
        >
          {/* Google Calendar Option */}
          <div className="flex items-start space-x-3">
            <RadioGroupItem 
              value="google_calendar" 
              id="google_calendar"
              disabled={!googleCalendarConnected || disabled}
              className="mt-1"
            />
            <div className="flex-1 space-y-2">
              <Label 
                htmlFor="google_calendar" 
                className={`flex items-center gap-2 font-medium ${
                  !googleCalendarConnected ? 'text-muted-foreground' : ''
                }`}
              >
                <Calendar className="h-4 w-4" />
                Google Calendar Integration
                {googleCalendarConnected ? (
                  <Badge variant="success" className="text-xs">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Connected
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="text-xs">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Not Connected
                  </Badge>
                )}
              </Label>
              <div className="text-sm text-muted-foreground pl-6">
                <p className="mb-2">
                  {googleCalendarConnected 
                    ? "Automatically creates calendar events and sends invitations through Google Calendar."
                    : "Google Calendar is not connected. Connect it in settings to use this option."
                  }
                </p>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    <span>Automatic calendar event creation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    <span>Real-time updates and reminders</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    <span>Syncs with client's Google Calendar</span>
                  </div>
                  {!googleCalendarConnected && (
                    <div className="flex items-center gap-2 text-amber-600">
                      <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                      <span>Requires Google Calendar connection</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Email with ICS Option */}
          <div className="flex items-start space-x-3">
            <RadioGroupItem 
              value="email_ics" 
              id="email_ics"
              disabled={disabled}
              className="mt-1"
            />
            <div className="flex-1 space-y-2">
              <Label htmlFor="email_ics" className="flex items-center gap-2 font-medium">
                <Mail className="h-4 w-4" />
                Email with Calendar File (.ics)
                <Badge variant="outline" className="text-xs">
                  Always Available
                </Badge>
              </Label>
              <div className="text-sm text-muted-foreground pl-6">
                <p className="mb-2">
                  Sends a professional email with an attached calendar file that works with all calendar applications.
                </p>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span>Works with any calendar app (Google, Outlook, Apple, etc.)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span>Professional email template with appointment details</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span>Includes reschedule and view appointment links</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span>No external dependencies required</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </RadioGroup>

        {/* Additional Information */}
        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="text-sm font-medium mb-2">💡 Recommendation</h4>
          <p className="text-sm text-muted-foreground">
            {googleCalendarConnected 
              ? "Both options work well. Google Calendar provides real-time sync, while email with ICS files works universally with all calendar applications."
              : "Email with ICS files is recommended when Google Calendar is not connected. It provides reliable calendar integration for all clients."
            }
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
