// src/components/providers/QueryProvider.tsx
'use client';

import React, { useState, ReactNode } from 'react';
import { QueryClient, QueryClientProvider, MutationCache } from '@tanstack/react-query';
import { toast } from 'sonner';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

interface QueryProviderProps {
  children: ReactNode;
}

export function ReactQueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        // Configure cache globally for mutations
        mutationCache: new MutationCache({
           onError: (error: any, variables, context, mutation) => {
               // A central place to handle mutation errors globally
               const errorInfo = {
                   error: error || 'No error object provided',
                   errorType: typeof error,
                   errorKeys: error ? Object.keys(error) : [],
                   errorIsEmpty: !error || Object.keys(error).length === 0,
                   errorMessage: error?.message || error?.toString() || 'No error message',
                   variables, // Input variables to the mutationFn
                   mutationName: mutation?.options?.mutationKey || 'unknown',
               };

               // Only log if it's not an empty error object (which might be expected)
               if (!errorInfo.errorIsEmpty || error?.message) {
                   console.error("Global Mutation Error:", errorInfo);
               }

               // Don't show toast for login/auth errors as they're handled in their components
               const isAuthMutation =
                   mutation?.options?.mutationKey?.toString().includes('login') ||
                   mutation?.options?.mutationKey?.toString().includes('auth');

               // Don't show toast for empty error objects (likely handled by the mutation itself)
               const isEmptyError = !error || (Object.keys(error).length === 0 && !error.message);

               if (!isAuthMutation && !isEmptyError) {
                   // Show a generic toast notification for non-auth mutation errors
                   toast.error("Action Failed", {
                       description: error?.message || "An unexpected error occurred. Please try again.",
                   });
               }
           },
        }),
        defaultOptions: {
          queries: {
            staleTime: 1000 * 60 * 5, // 5 minutes
            refetchOnWindowFocus: true, // Sensible default, can be overridden per query
            retry: (failureCount, error: any) => {
                 // Don't retry on 4xx errors (like auth errors, not found)
                 if (error?.response?.status >= 400 && error?.response?.status < 500) {
                     return false;
                 }
                 // Otherwise, retry up to 2 times (total 3 attempts)
                 return failureCount < 2;
            },
          },
          // Default mutation options (less needed now with MutationCache)
          // mutations: { // onError here would be specific to mutations without their own onError
          // },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* Devtools only render in development based on NODE_ENV */}
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
}
