'use client';

import React, { useMemo, useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Toolt<PERSON>,
  Legend
} from 'recharts';
import { Appointment, ServiceDefinition } from '@/lib/types/db';
import { CHART_COLORS, TOOLTIP_STYLE } from '@/lib/utils/chartColors';
import { cn } from '@/lib/utils';
import { ServiceLogo } from '@/components/ui/ServiceLogo';
import { Logo } from '@/components/ui/Logo';

interface ServicePopularityChartProps {
  appointments: Appointment[];
  services: ServiceDefinition[];
  isLoading: boolean;
  title?: string;
  description?: string;
  className?: string;
}

export function ServicePopularityChart({
  appointments,
  services,
  isLoading,
  title = 'Popular Services',
  description = 'Most booked services',
  className
}: ServicePopularityChartProps) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const updateSize = () => {
      setIsMobile(window.innerWidth < 768); // Increased breakpoint for better mobile experience
    };
    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  const chartData = useMemo(() => {
    const serviceCounts = appointments.reduce((acc, { serviceId }) => {
      if (!serviceId) return acc;
      acc[serviceId] = (acc[serviceId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(serviceCounts)
      .map(([serviceId, value]) => {
        const service = services.find(s => s.id === serviceId);
        return {
          id: serviceId,
          name: service?.name || 'Unknown',
          value
        };
      })
      .sort((a, b) => b.value - a.value);
  }, [appointments, services]);

  // Limit the number of items shown on mobile to prevent overcrowding
  const displayData = useMemo(() => {
    if (isMobile && chartData.length > 5) {
      // Take top 4 items and group the rest as "Other"
      const topItems = chartData.slice(0, 4);
      const otherItems = chartData.slice(4);
      const otherValue = otherItems.reduce((sum, item) => sum + item.value, 0);

      if (otherValue > 0) {
        return [...topItems, { id: 'other', name: 'Other', value: otherValue }];
      }
    }
    return chartData;
  }, [chartData, isMobile]);

  const totalCount = chartData.reduce((sum, item) => sum + item.value, 0);

  // Custom legend component with logos
  const CustomLegend = ({ payload }: any) => {
    if (!payload || payload.length === 0) return null;

    return (
      <div className="flex flex-wrap justify-center gap-2 mt-2 px-2">
        {payload.map((entry: any, index: number) => (
          <div
            key={`legend-${index}`}
            className="flex items-center gap-1 bg-background/50 px-2 py-1 rounded-full text-xs"
          >
            <ServiceLogo
              serviceName={entry.value}
              size="sm"
              color={entry.color}
            />
            <span className="truncate max-w-[80px] sm:max-w-[120px]">
              {isMobile
                ? `${entry.value.length > 10 ? entry.value.substring(0, 8) + '...' : entry.value} (${((displayData[index].value / totalCount) * 100).toFixed(0)}%)`
                : `${entry.value} (${((displayData[index].value / totalCount) * 100).toFixed(0)}%)`
              }
            </span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card className={cn('shadow-md overflow-hidden', className)}>
      <CardHeader className="pb-2 sm:pb-6">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base sm:text-lg">{title}</CardTitle>
            <CardDescription className="text-xs sm:text-sm">{description}</CardDescription>
          </div>
          <Logo size="sm" className="hidden sm:block" />
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="h-[200px] flex items-center justify-center text-muted-foreground text-sm">
            <Logo size="md" className="mr-2 opacity-50" />
            <span>Loading chart data...</span>
          </div>
        ) : chartData.length === 0 ? (
          <div className="h-[200px] flex items-center justify-center text-muted-foreground text-sm">
            <Logo size="md" className="mr-2 opacity-50" />
            <span>No service data available.</span>
          </div>
        ) : (
          <div className="h-[220px] sm:h-[250px] md:h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
                <Pie
                  data={displayData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="40%"
                  outerRadius={isMobile ? 55 : 80}
                  innerRadius={isMobile ? 20 : 30}
                  labelLine={false}
                  label={({ percent }) => isMobile ? `${(percent * 100).toFixed(0)}%` : null}
                >
                  {displayData.map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={CHART_COLORS[index % CHART_COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value) => [`${value} appointments`, 'Count']}
                  contentStyle={TOOLTIP_STYLE}
                />
                <Legend
                  content={<CustomLegend />}
                  layout="horizontal"
                  verticalAlign="bottom"
                  align="center"
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
