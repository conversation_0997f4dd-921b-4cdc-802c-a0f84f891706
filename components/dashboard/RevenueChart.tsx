'use client';

import React, { useMemo, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { Appointment, ServiceDefinition } from '@/lib/types/db';
import { format, isWithinInterval, eachDayOfInterval, eachWeekOfInterval, startOfWeek, endOfWeek, eachMonthOfInterval, startOfMonth, endOfMonth, isSameDay, isSameWeek, isSameMonth } from 'date-fns';
import { DateRange, DateRangeFilter } from './DateRangeFilter';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface RevenueChartProps {
  appointments: Appointment[];
  services: ServiceDefinition[];
  isLoading: boolean;
  title?: string;
  description?: string;
}

type GroupBy = 'day' | 'week' | 'month';

export function RevenueChart({
  appointments,
  services,
  isLoading,
  title = 'Revenue Overview',
  description = 'Revenue trends over time',
}: RevenueChartProps) {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  });

  const [groupBy, setGroupBy] = useState<GroupBy>('day');

  const chartData = useMemo(() => {
    if (!appointments || appointments.length === 0 || !services || services.length === 0) {
      return [];
    }

    // Filter appointments within the date range
    const filteredAppointments = appointments.filter(appointment => {
      const appointmentDate = appointment.startTime.toDate();
      return isWithinInterval(appointmentDate, { start: dateRange.from, end: dateRange.to });
    });

    // Get service prices
    const servicePrices = services.reduce((acc, service) => {
      acc[service.id] = service.price || 0;
      return acc;
    }, {} as Record<string, number>);

    // Generate intervals based on groupBy
    let intervals: Date[] = [];

    if (groupBy === 'day') {
      intervals = eachDayOfInterval({ start: dateRange.from, end: dateRange.to });
    } else if (groupBy === 'week') {
      intervals = eachWeekOfInterval(
        { start: dateRange.from, end: dateRange.to },
        { weekStartsOn: 1 }
      );
    } else if (groupBy === 'month') {
      intervals = eachMonthOfInterval({ start: dateRange.from, end: dateRange.to });
    }

    // Calculate revenue for each interval
    return intervals.map(intervalStart => {
      let intervalEnd: Date;
      let formatString: string;

      if (groupBy === 'day') {
        intervalEnd = intervalStart;
        formatString = 'MMM d';
      } else if (groupBy === 'week') {
        intervalEnd = endOfWeek(intervalStart, { weekStartsOn: 1 });
        formatString = "'Week of' MMM d";
      } else {
        intervalEnd = endOfMonth(intervalStart);
        formatString = 'MMM yyyy';
      }

      // Calculate revenue for this interval
      const revenue = filteredAppointments.reduce((total, appointment) => {
        const appointmentDate = appointment.startTime.toDate();
        const isInInterval = groupBy === 'day'
          ? isSameDay(appointmentDate, intervalStart)
          : groupBy === 'week'
            ? isSameWeek(appointmentDate, intervalStart, { weekStartsOn: 1 })
            : isSameMonth(appointmentDate, intervalStart);

        if (isInInterval && appointment.status !== 'cancelled' && appointment.status !== 'no-show') {
          // Use the revenue field if available, otherwise fall back to service price
          if (appointment.revenue) {
            return total + appointment.revenue;
          } else {
            return total + (servicePrices[appointment.serviceId] || 0);
          }
        }

        return total;
      }, 0);

      return {
        name: format(intervalStart, formatString),
        revenue,
        date: intervalStart,
      };
    });
  }, [appointments, services, dateRange, groupBy]);

  const totalRevenue = useMemo(() => {
    return chartData.reduce((sum, data) => sum + data.revenue, 0);
  }, [chartData]);

  const averageRevenue = useMemo(() => {
    if (chartData.length === 0) return 0;
    return totalRevenue / chartData.length;
  }, [chartData, totalRevenue]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  return (
    <Card className="col-span-full">
      <CardHeader className="flex flex-col space-y-4">
        <div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
        <div className="flex flex-col space-y-3 w-full">
          <Tabs defaultValue="day" value={groupBy} onValueChange={(value) => setGroupBy(value as GroupBy)} className="w-full">
            <TabsList className="w-full grid grid-cols-3">
              <TabsTrigger value="day" className="flex-1">Daily</TabsTrigger>
              <TabsTrigger value="week" className="flex-1">Weekly</TabsTrigger>
              <TabsTrigger value="month" className="flex-1">Monthly</TabsTrigger>
            </TabsList>
          </Tabs>
          <DateRangeFilter onChange={setDateRange} className="w-full" />
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-80">
            <p className="text-muted-foreground">Loading chart data...</p>
          </div>
        ) : chartData.length === 0 ? (
          <div className="flex items-center justify-center h-80">
            <p className="text-muted-foreground">No revenue data available for the selected period</p>
          </div>
        ) : (
          <>
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 mb-6">
              <div className="rounded-lg border bg-card p-4 shadow-sm">
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Total Revenue</h3>
                <div className="text-2xl font-bold">{formatCurrency(totalRevenue)}</div>
              </div>
              <div className="rounded-lg border bg-card p-4 shadow-sm">
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Average {groupBy === 'day' ? 'Daily' : groupBy === 'week' ? 'Weekly' : 'Monthly'} Revenue</h3>
                <div className="text-2xl font-bold">{formatCurrency(averageRevenue)}</div>
              </div>
              <div className="rounded-lg border bg-card p-4 shadow-sm">
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Period</h3>
                <div className="text-sm font-medium">
                  {format(dateRange.from, 'MMM d, yyyy')} - {format(dateRange.to, 'MMM d, yyyy')}
                </div>
              </div>
            </div>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis
                    dataKey="name"
                    tick={{ fontSize: 12 }}
                    tickMargin={10}
                  />
                  <YAxis
                    tickFormatter={(value) => formatCurrency(value)}
                    width={80}
                  />
                  <Tooltip
                    formatter={(value) => [formatCurrency(value as number), 'Revenue']}
                    labelFormatter={(label) => `Revenue for ${label}`}
                    contentStyle={{
                      backgroundColor: 'hsl(var(--card))',
                      borderColor: 'hsl(var(--border))',
                      borderRadius: '0.5rem',
                      boxShadow: 'var(--shadow)'
                    }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    name="Revenue"
                    stroke="hsl(var(--primary))"
                    strokeWidth={2}
                    dot={{ r: 4, strokeWidth: 2 }}
                    activeDot={{ r: 6, strokeWidth: 2 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
