'use client';

import React, { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON> } from 'recharts';
import { Appointment, StaffMember } from '@/lib/types/db';
import { PRIMARY_COLORS, MUTED_COLORS, TOOLTIP_STYLE, GRID_STYLE, STATUS_COLORS } from '@/lib/utils/chartColors';
import { cn } from '@/lib/utils';

interface StaffPerformanceChartProps {
  appointments: Appointment[];
  staffMembers: StaffMember[];
  isLoading: boolean;
  title?: string;
  description?: string;
  className?: string;
}

export function StaffPerformanceChart({
  appointments,
  staffMembers,
  isLoading,
  title = 'Staff Performance',
  description = 'Appointments handled by staff',
  className,
}: StaffPerformanceChartProps) {
  const chartData = useMemo(() => {
    if (!appointments || appointments.length === 0 || !staffMembers || staffMembers.length === 0) {
      return [];
    }

    // Count completed and total appointments by staff
    const staffCounts = staffMembers.map(staff => {
      const staffAppointments = appointments.filter(appt => appt.staffId === staff.id);
      const completed = staffAppointments.filter(appt => appt.status === 'completed').length;
      const cancelled = staffAppointments.filter(appt => appt.status === 'cancelled').length;
      const noShow = staffAppointments.filter(appt => appt.status === 'no-show').length;
      const upcoming = staffAppointments.filter(appt =>
        appt.status === 'scheduled'
      ).length;

      return {
        name: staff.name,
        id: staff.id,
        completed,
        cancelled,
        noShow,
        upcoming,
        total: staffAppointments.length,
      };
    });

    // Sort by total appointments (descending)
    return staffCounts.sort((a, b) => b.total - a.total);
  }, [appointments, staffMembers]);

  return (
    <Card className={cn("col-span-full", className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">Loading chart data...</p>
          </div>
        ) : chartData.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">No staff performance data available</p>
          </div>
        ) : (
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                layout="vertical"
              >
                <CartesianGrid {...GRID_STYLE} />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip contentStyle={TOOLTIP_STYLE} />
                <Legend />
                <Bar
                  dataKey="completed"
                  name="Completed"
                  stackId="a"
                  fill={STATUS_COLORS.success}
                />
                <Bar
                  dataKey="upcoming"
                  name="Upcoming"
                  stackId="a"
                  fill={PRIMARY_COLORS[0]}
                />
                <Bar
                  dataKey="cancelled"
                  name="Cancelled"
                  stackId="a"
                  fill={STATUS_COLORS.danger}
                />
                <Bar
                  dataKey="noShow"
                  name="No-Show"
                  stackId="a"
                  fill={MUTED_COLORS[0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
