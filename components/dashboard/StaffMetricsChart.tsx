'use client';

import React, { useMemo, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer, Tooltip, Legend } from 'recharts';
import { Appointment, ServiceDefinition, StaffMember } from '@/lib/types/db';
import { DateRange, DateRangeFilter } from './DateRangeFilter';
import { isWithinInterval } from 'date-fns';
import { cn } from '@/lib/utils';
import { PRIMARY_COLORS, TOOLTIP_STYLE } from '@/lib/utils/chartColors';

interface StaffMetricsChartProps {
  appointments: Appointment[];
  staffMember: StaffMember;
  services: ServiceDefinition[];
  isLoading: boolean;
  title?: string;
  description?: string;
  className?: string;
}

export function StaffMetricsChart({
  appointments,
  staffMember,
  services,
  isLoading,
  title = 'Performance Metrics',
  description = 'Your performance across key metrics',
  className,
}: StaffMetricsChartProps) {
  const [dateRange, setDateRange] = useState<DateRange>({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  });

  const metricsData = useMemo(() => {
    if (!appointments || appointments.length === 0 || !staffMember) {
      return [];
    }

    // Filter appointments within the date range and for this staff member
    const filteredAppointments = appointments.filter(appointment => {
      const appointmentDate = appointment.startTime.toDate();
      return isWithinInterval(appointmentDate, { start: dateRange.from, end: dateRange.to }) &&
             appointment.staffId === staffMember.id;
    });

    // Calculate metrics
    const totalAppointments = filteredAppointments.length;
    if (totalAppointments === 0) return [];

    const completedAppointments = filteredAppointments.filter(a => a.status === 'completed').length;
    const cancelledAppointments = filteredAppointments.filter(a => a.status === 'cancelled').length;
    const noShowAppointments = filteredAppointments.filter(a => a.status === 'no-show').length;

    // Calculate completion rate (completed / total)
    const completionRate = (completedAppointments / totalAppointments) * 100;

    // Calculate cancellation rate (cancelled / total)
    const cancellationRate = (cancelledAppointments / totalAppointments) * 100;

    // Calculate no-show rate (no-show / total)
    const noShowRate = (noShowAppointments / totalAppointments) * 100;

    // Calculate service diversity (unique services / total services assigned)
    const uniqueServices = new Set(filteredAppointments.map(a => a.serviceId)).size;
    const serviceDiversity = staffMember.services
      ? (uniqueServices / staffMember.services.length) * 100
      : 0;

    // Calculate client retention (returning clients / total clients)
    const clientCounts: Record<string, number> = {};
    filteredAppointments.forEach(appointment => {
      const clientId = appointment.clientId || appointment.clientEmail || 'unknown';
      clientCounts[clientId] = (clientCounts[clientId] || 0) + 1;
    });

    const totalClients = Object.keys(clientCounts).length;
    const returningClients = Object.values(clientCounts).filter(count => count > 1).length;
    const clientRetention = totalClients > 0 ? (returningClients / totalClients) * 100 : 0;

    // Calculate average appointments per day
    const daysDiff = Math.max(1, Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)));
    const appointmentsPerDay = (totalAppointments / daysDiff) * 5; // Scale to 0-100 range (assuming max 20 per day)

    return [
      { subject: 'Completion Rate', A: Math.min(100, completionRate), fullMark: 100 },
      { subject: 'Client Retention', A: Math.min(100, clientRetention), fullMark: 100 },
      { subject: 'Service Diversity', A: Math.min(100, serviceDiversity), fullMark: 100 },
      { subject: 'Daily Volume', A: Math.min(100, appointmentsPerDay), fullMark: 100 },
      { subject: 'Low Cancellation', A: Math.min(100, 100 - cancellationRate), fullMark: 100 },
      { subject: 'Low No-Shows', A: Math.min(100, 100 - noShowRate), fullMark: 100 },
    ];
  }, [appointments, staffMember, dateRange]);

  const performanceScore = useMemo(() => {
    if (metricsData.length === 0) return 0;
    const sum = metricsData.reduce((total, metric) => total + metric.A, 0);
    return Math.round(sum / metricsData.length);
  }, [metricsData]);

  const getPerformanceLabel = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Very Good';
    if (score >= 70) return 'Good';
    if (score >= 60) return 'Satisfactory';
    if (score >= 50) return 'Needs Improvement';
    return 'Requires Attention';
  };

  return (
    <Card className={cn("col-span-full md:col-span-1", className)}>
      <CardHeader className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
        <DateRangeFilter onChange={setDateRange} />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-80">
            <p className="text-muted-foreground">Loading metrics data...</p>
          </div>
        ) : metricsData.length === 0 ? (
          <div className="flex items-center justify-center h-80">
            <p className="text-muted-foreground">No performance data available for the selected period</p>
          </div>
        ) : (
          <>
            <div className="flex justify-center mb-6">
              <div className="text-center">
                <div className="text-sm font-medium text-muted-foreground mb-1">Performance Score</div>
                <div className="text-4xl font-bold">{performanceScore}</div>
                <div className={`text-sm font-medium mt-1 ${
                  performanceScore >= 70
                    ? 'text-green-500'
                    : performanceScore >= 50
                      ? 'text-amber-500'
                      : 'text-red-500'
                }`}>
                  {getPerformanceLabel(performanceScore)}
                </div>
              </div>
            </div>
            <div className="h-72">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart cx="50%" cy="50%" outerRadius="80%" data={metricsData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="subject" />
                  <PolarRadiusAxis angle={30} domain={[0, 100]} />
                  <Radar
                    name="Performance"
                    dataKey="A"
                    stroke={PRIMARY_COLORS[0]}
                    fill={PRIMARY_COLORS[4]}
                    fillOpacity={0.6}
                  />
                  <Tooltip
                    formatter={(value) => [`${Math.round(value as number)}%`, 'Score']}
                    contentStyle={TOOLTIP_STYLE}
                  />
                  <Legend />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
