# Firebase Storage Setup Guide

This guide will help you set up Firebase Storage security rules to allow ICS file operations for the Onpointly appointment scheduling system.

## Problem

You're seeing this error when creating appointments:
```
FirebaseError: Firebase Storage: User does not have permission to access 'ics_files/...' (storage/unauthorized)
```

This happens because Firebase Storage has default security rules that deny all access.

## Solution

You need to deploy the Firebase Storage security rules that allow the application to store and retrieve ICS files.

## Method 1: Using Firebase CLI (Recommended)

### Prerequisites
1. Install Firebase CLI if you haven't already:
   ```bash
   npm install -g firebase-tools
   ```

2. Login to Firebase:
   ```bash
   firebase login
   ```

3. Make sure you're in the correct Firebase project:
   ```bash
   firebase use onpointly
   ```

### Deploy Storage Rules

1. **Automatic deployment** (using our script):
   ```bash
   node scripts/deploy-storage-rules.js
   ```

2. **Manual deployment**:
   ```bash
   firebase deploy --only storage
   ```

## Method 2: Using Firebase Console (Alternative)

If you can't use the Firebase CLI, you can manually update the rules through the Firebase Console:

### Steps:

1. **Go to Firebase Console**:
   - Visit https://console.firebase.google.com/
   - Select your "onpointly" project

2. **Navigate to Storage**:
   - Click on "Storage" in the left sidebar
   - Click on the "Rules" tab

3. **Update the Rules**:
   - Copy the contents of the `storage.rules` file from this project
   - Paste it into the Firebase Console rules editor
   - Click "Publish"

### Storage Rules Content:

```javascript
rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }
    
    function isOwner(businessId) {
      return isSignedIn() &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.businessId == businessId &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'owner';
    }
    
    function isAdmin(businessId) {
      return isSignedIn() &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.businessId == businessId &&
        (firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'owner');
    }
    
    function isStaff(businessId) {
      return isSignedIn() &&
        firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.businessId == businessId &&
        (firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'staff' ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'owner');
    }
    
    // Business logos - only business admins can upload/update, anyone can read
    match /businesses/{businessId}/logos/{logoFile} {
      allow read: if true; // Public read access for displaying logos
      allow write: if isAdmin(businessId);
      allow delete: if isAdmin(businessId);
    }
    
    // ICS files - business staff can create/read, clients can read their own files
    match /ics_files/{businessId}/{icsFile} {
      // Business staff can read all ICS files for their business
      allow read: if isStaff(businessId);
      
      // Business staff can create/update ICS files for their business
      allow write: if isStaff(businessId);
      
      // Business staff can delete ICS files for their business
      allow delete: if isStaff(businessId);
      
      // Allow public read access for ICS files (needed for email attachments and client downloads)
      // This is safe because ICS files don't contain sensitive information
      allow read: if true;
    }
    
    // Default deny rule for any other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
```

## What These Rules Do

### ICS Files (`ics_files/{businessId}/{icsFile}`)
- **Business staff** can create, read, update, and delete ICS files for their business
- **Public read access** is allowed for ICS files (needed for email attachments and client downloads)
- ICS files are stored in business-specific directories for organization

### Business Logos (`businesses/{businessId}/logos/{logoFile}`)
- **Anyone** can read business logos (for public display)
- **Only business admins** can upload/update/delete logos

### Security Features
- All rules check user authentication and business ownership
- Role-based access control (owner, admin, staff)
- Business-specific data isolation
- Public read access only for non-sensitive files (ICS files, logos)

## Testing the Setup

After deploying the rules, test the functionality:

1. **Create a new appointment** with "Email with Calendar File" selected
2. **Check the console logs** for successful ICS file upload messages
3. **Verify email delivery** with ICS file attachment
4. **Check Firebase Storage** in the console to see the uploaded ICS files

## Troubleshooting

### Common Issues:

1. **"Permission denied" errors**:
   - Make sure the storage rules are deployed correctly
   - Check that the user is authenticated and has the correct role
   - Verify the business ID matches the user's business

2. **"Firebase CLI not found"**:
   - Install Firebase CLI: `npm install -g firebase-tools`
   - Login: `firebase login`

3. **"Project not found"**:
   - Make sure you're using the correct project: `firebase use onpointly`
   - Check project permissions in Firebase Console

4. **Rules syntax errors**:
   - Validate the rules syntax in Firebase Console
   - Check for missing commas or brackets

### Fallback Behavior

The application has been updated with fallback behavior:
- If Firebase Storage upload fails, the appointment will still be created
- ICS files will still be attached to emails
- Users will receive a warning but the process won't fail completely

## Files Modified

- `storage.rules` - New Firebase Storage security rules
- `firebase.json` - Updated to include storage rules configuration
- `lib/services/emailService.ts` - Added fallback handling for storage errors
- `scripts/deploy-storage-rules.js` - Deployment script

## Next Steps

1. Deploy the storage rules using one of the methods above
2. Test appointment creation with ICS file generation
3. Verify that emails are being sent with ICS attachments
4. Check Firebase Storage console to see uploaded files

The system will now work correctly with proper Firebase Storage permissions!
