# Mailgun Email Setup Guide for Onpointly

## Current Issue

You're seeing this error when trying to send emails:
```
❌ Error sending email: Forbidden
Status Code: 403
Details: Domain sandbox5d95d63c4f354d3386ac6db746300064.mailgun.org is not allowed to send: Free accounts are for test purposes only. Please upgrade or add the address to your authorized recipients.
```

This happens because you're using a **Mailgun sandbox domain** which has restrictions.

## Quick Fix: Add Authorized Recipients

### Step 1: Go to Mailgun Dashboard
1. Visit https://app.mailgun.com/
2. Log in to your Mailgun account
3. Select your domain: `sandbox5d95d63c4f354d3386ac6db746300064.mailgun.org`

### Step 2: Add Authorized Recipients
1. In the left sidebar, click **"Sending"**
2. Click **"Authorized Recipients"**
3. Click **"Add Recipient"**
4. Add these email addresses (one at a time):
   - Your personal email address (for testing)
   - Any client email addresses you want to test with
   - Staff email addresses

### Step 3: Verify Recipients
- Mailgun will send verification emails to each recipient
- Recipients must click the verification link
- Once verified, you can send emails to these addresses

## Testing the Fix

### Option 1: Use the Test Page
1. Go to http://localhost:3000/test-email
2. Enter an authorized recipient email
3. Click "Send Test Email"
4. Check if the email is received

### Option 2: Create an Appointment
1. Create a new appointment with an authorized recipient email
2. Select "Email with Calendar File (.ics)"
3. Check console logs and email delivery

## Long-term Solutions

### Option A: Upgrade Mailgun Account (Recommended)
1. Go to Mailgun dashboard → Billing
2. Upgrade to a paid plan (starts at $35/month)
3. Benefits:
   - Remove sandbox restrictions
   - Send to any email address
   - Higher sending limits
   - Better deliverability

### Option B: Use a Different Email Service
Consider switching to:
- **Resend** (developer-friendly, good free tier)
- **SendGrid** (reliable, good free tier)
- **Amazon SES** (cost-effective for high volume)

## Current Configuration

Your current Mailgun setup:
- **Domain:** `sandbox5d95d63c4f354d3386ac6db746300064.mailgun.org`
- **API Key:** `5eb51382...` (sandbox key)
- **From Address:** `<EMAIL>`
- **Restrictions:** Can only send to authorized recipients

## Sandbox Limitations

Free Mailgun sandbox accounts have these restrictions:
- ✅ 300 emails per day
- ❌ Can only send to authorized recipients
- ❌ Limited deliverability features
- ❌ Mailgun branding in emails

## Email Features Working

Once you add authorized recipients, these features will work:
- ✅ **Client Confirmation Emails** with ICS file attachments
- ✅ **Staff Notification Emails** for new appointments
- ✅ **Business Owner Notifications**
- ✅ **Professional HTML Email Templates**
- ✅ **Calendar Integration** (.ics files)
- ✅ **Action Buttons** (View Appointment, Reschedule)

## Troubleshooting

### Common Issues:

1. **"Forbidden" Error (403)**
   - **Cause:** Recipient not in authorized list
   - **Fix:** Add recipient to authorized recipients

2. **"Unauthorized" Error (401)**
   - **Cause:** Invalid API key
   - **Fix:** Check API key in `.env.local`

3. **"Bad Request" Error (400)**
   - **Cause:** Invalid email format or domain
   - **Fix:** Check email addresses and domain configuration

4. **Emails Not Received**
   - Check spam/junk folder
   - Verify recipient is authorized
   - Check Mailgun logs in dashboard

### Debug Steps:

1. **Check Environment Variables:**
   ```bash
   # In your project directory
   cat .env.local | grep MAILGUN
   ```

2. **Test Mailgun Configuration:**
   ```bash
   node test-mailgun.js
   ```

3. **Check Application Logs:**
   - Look for Mailgun initialization messages
   - Check for email sending attempts
   - Look for error details

## Next Steps

### Immediate (for testing):
1. ✅ Add your email to authorized recipients
2. ✅ Test email sending with the test page
3. ✅ Create a test appointment

### Short-term (for development):
1. Add team member emails to authorized recipients
2. Test all email features thoroughly
3. Verify ICS file attachments work

### Long-term (for production):
1. Upgrade Mailgun account or switch providers
2. Set up custom domain for better branding
3. Configure email analytics and monitoring

## Files Modified

- `lib/services/mailgunService.ts` - Enhanced error handling for sandbox restrictions
- `lib/services/emailService.ts` - Added fallback for sandbox errors
- `app/test-email/page.tsx` - Test page for email configuration
- `test-mailgun.js` - Command-line test script

## Support

If you continue having issues:
1. Check Mailgun dashboard logs
2. Verify API key permissions
3. Contact Mailgun support for account-specific issues
4. Consider switching to a different email provider

The email system is properly configured and will work perfectly once the sandbox restrictions are resolved!
