{"name": "onpointly", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "dev:turbo": "next dev --turbopack -p 3000", "build": "next build", "build:pwa": "npm run setup-pwa && next build", "build:static": "node scripts/prepare-static-build.js && next build && node scripts/restore-after-build.js", "start": "next start", "lint": "next lint", "setup-pwa": "node scripts/setup-pwa.js", "cleanup": "node scripts/cleanup.js", "reminders:test": "node scripts/test-reminders.js", "reminders:24h": "curl -X POST http://localhost:3000/api/reminders -H 'Content-Type: application/json' -d '{\"reminderType\":\"24h\"}'", "reminders:1h": "curl -X POST http://localhost:3000/api/reminders -H 'Content-Type: application/json' -d '{\"reminderType\":\"1h\"}'", "mailgun:test": "node scripts/test-mailgun.js", "mailgun:debug": "node scripts/mailgun-debug.js", "mailgun:official": "node scripts/mailgun-official.js", "mailgun:authorize": "node scripts/mailgun-authorize.js"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.6", "@sendgrid/mail": "^8.1.5", "@tanstack/react-query": "^5.71.0", "@tanstack/react-query-devtools": "^5.71.0", "@tanstack/react-table": "^8.21.2", "@types/nodemailer": "^6.4.17", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cors": "^2.8.5", "date-fns": "^2.30.0", "date-fns-tz": "^1.3.7", "dotenv": "^16.4.7", "firebase": "^11.5.0", "firebase-admin": "^12.5.0", "firebase-functions": "^6.3.2", "form-data": "^4.0.2", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "lucide-react": "^0.485.0", "mailgun.js": "^12.0.1", "next": "15.2.4", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "nodemailer": "^6.10.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "recharts": "^2.15.2", "resend": "^4.2.0", "sonner": "^2.0.2", "stripe": "^18.2.1", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.0.17", "@types/date-fns": "^2.6.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "esbuild": "^0.19.12", "eslint": "^9", "eslint-config-next": "15.2.4", "sharp": "^0.34.1", "tailwindcss": "^4.0.17", "typescript": "^5"}, "resolutions": {"prettier": "^3.5.3"}}