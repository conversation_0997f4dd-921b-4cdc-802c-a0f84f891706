// src/lib/utils/dateUtils.ts
import { Timestamp } from 'firebase/firestore';
import { format, parse, getDay, addMinutes, isWithinInterval, isEqual, isBefore, isValid } from 'date-fns';
import utcToZonedTime from 'date-fns-tz/utcToZonedTime';
import formatInTimeZone from 'date-fns-tz/formatInTimeZone';
import { BusinessHours, Appointment } from '@/lib/types/db'; // Assuming types are defined

/**
 * Converts various date inputs (including Firestore Timestamps) to a JS Date object.
 * Returns null if the input is invalid.
 */
export const ensureDateObject = (dateInput: Date | Timestamp | string | number | undefined | null): Date | null => {
    if (!dateInput) return null;

    let date: Date | null = null;
    if (dateInput instanceof Date) {
        date = dateInput;
    } else if (dateInput instanceof Timestamp) {
        date = dateInput.toDate();
    } else if (typeof dateInput === 'string' || typeof dateInput === 'number') {
        const parsed = new Date(dateInput);
        if (isValid(parsed)) {
            date = parsed;
        }
    }

    return isValid(date) ? date : null;
};

/**
 * Formats a date/time for display in a specific timezone.
 * Example: "Tue, Jul 23, 2024, 10:30 AM"
 */
export const formatDateTimeReadable = (
    dateInput: Date | Timestamp | string | number,
    timeZone: string // e.g., 'America/New_York', 'Europe/London'
): string => {
    const date = ensureDateObject(dateInput);
    if (!date || !timeZone) return 'Invalid Date';
    try {
        return formatInTimeZone(date, timeZone, 'EEE, MMM d, yyyy, h:mm a');
    } catch (error) {
        console.error("Error formatting date/time:", error);
        return 'Invalid Timezone or Date';
    }
};

/**
 * Formats only the time part for display in a specific timezone.
 * Example: "10:30 AM"
 */
export const formatTimeReadable = (
    dateInput: Date | Timestamp | string | number,
    timeZone: string
): string => {
    const date = ensureDateObject(dateInput);
     if (!date || !timeZone) return 'Invalid Time';
     try {
        return formatInTimeZone(date, timeZone, 'h:mm a');
    } catch (error) {
        console.error("Error formatting time:", error);
        return 'Invalid Timezone or Time';
    }
};


/**
 * Parses a time string (like "09:00" or "17:30") and combines it with a reference date,
 * interpreting the time within the given timezone.
 * Returns a Date object in system time (usually UTC equivalent internally).
 */
export const parseTimeStringToDate = (
    timeString: string, // e.g., "09:00"
    referenceDate: Date, // The specific day
    timeZone: string
): Date | null => {
    if (!timeString || !referenceDate || !timeZone) return null;
    try {
        const [hours, minutes] = timeString.split(':').map(Number);
        if (isNaN(hours) || isNaN(minutes)) return null;

        // Create a date object in the target timezone, then convert to UTC Date object
        const zonedReferenceDate = utcToZonedTime(referenceDate, timeZone);
        const year = zonedReferenceDate.getFullYear();
        const month = zonedReferenceDate.getMonth();
        const day = zonedReferenceDate.getDate();

        // Construct the date in the target timezone, then convert to JS Date (UTC)
        const dateInTargetTz = new Date(year, month, day, hours, minutes);
        return utcToZonedTime(dateInTargetTz, timeZone);

    } catch (error) {
        console.error("Error parsing time string:", error);
        return null;
    }
};


/**
 * Gets the lowercase name of the day of the week ('sunday', 'monday', etc.)
 * from a Date object, considering the specified timezone.
 */
export const getDayOfWeekName = (date: Date, timeZone: string): keyof BusinessHours | null => {
    if (!date || !timeZone) return null;
    try {
        const zonedDate = utcToZonedTime(date, timeZone);
        const dayIndex = getDay(zonedDate); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
        const days: (keyof BusinessHours)[] = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        return days[dayIndex];
    } catch (error) {
        console.error("Error getting day of week:", error);
        return null;
    }

};


/**
 * Checks if a potential appointment slot overlaps with any existing appointments.
 * Assumes all inputs are valid JS Date objects.
 */
export const doesSlotOverlap = (
    slotStart: Date,
    slotEnd: Date, // Calculated by adding duration to slotStart
    existingAppointments: Appointment[]
): boolean => {
    // Validate that slotStart is before slotEnd
    if (!slotStart || !slotEnd || slotStart >= slotEnd) {
        console.warn('Invalid slot interval:', { slotStart, slotEnd });
        return true; // Consider invalid slots as overlapping to prevent booking
    }

    for (const appt of existingAppointments) {
        const apptStart = ensureDateObject(appt.startTime);
        const apptEnd = ensureDateObject(appt.endTime);

        if (!apptStart || !apptEnd) continue; // Skip invalid appointments

        // Validate that appointment has valid interval
        if (apptStart >= apptEnd) {
            console.warn('Invalid appointment interval:', { apptStart, apptEnd });
            continue; // Skip invalid appointments
        }

        try {
            // Overlap conditions:
            // 1. Existing appointment starts during the potential slot
            const apptStartsDuringSlot = isWithinInterval(apptStart, { start: slotStart, end: slotEnd }) && !isEqual(apptStart, slotEnd);
            // 2. Existing appointment ends during the potential slot
            const apptEndsDuringSlot = isWithinInterval(apptEnd, { start: slotStart, end: slotEnd }) && !isEqual(apptEnd, slotStart);
            // 3. Potential slot is completely contained within the existing appointment
            const slotWithinAppt = isWithinInterval(slotStart, { start: apptStart, end: apptEnd }) && isWithinInterval(slotEnd, { start: apptStart, end: apptEnd });
            // 4. Check for exact start time match (important)
            const exactStartMatch = isEqual(slotStart, apptStart);

            if (apptStartsDuringSlot || apptEndsDuringSlot || slotWithinAppt || exactStartMatch) {
                return true; // Overlap detected
            }
        } catch (error) {
            console.error('Error checking slot overlap:', error, {
                slotStart,
                slotEnd,
                apptStart,
                apptEnd
            });
            // If there's an error, assume overlap to be safe
            return true;
        }
    }
    return false; // No overlap
};

// Helper to add minutes easily
export const addMinutesToDate = (date: Date, minutes: number): Date => {
    return addMinutes(date, minutes);
};

/**
 * Formats a date for use in datetime-local input fields
 * @param date The date to format
 * @returns A string in the format YYYY-MM-DDThh:mm
 */
export const formatDateForInput = (date: Date): string => {
    if (!date || !(date instanceof Date)) return '';

    // Format as YYYY-MM-DDThh:mm
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
};
