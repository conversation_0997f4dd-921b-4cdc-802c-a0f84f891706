// lib/services/groupAppointmentService.ts
import { 
  collection, 
  addDoc, 
  getDocs, 
  query, 
  where, 
  Timestamp, 
  serverTimestamp,
  DocumentReference,
  updateDoc,
  deleteDoc,
  doc,
  getDoc,
  writeBatch,
  increment
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Appointment } from '@/lib/types/db';
import { createAppointment, getAppointmentById } from './appointmentService';

const appointmentsCollection = 'appointments';

/**
 * Creates a new group appointment
 * @param appointmentData The appointment data
 * @param maxParticipants Maximum number of participants allowed
 * @param initialParticipant Initial participant data
 * @returns The ID of the created group appointment
 */
export const createGroupAppointment = async (
  appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>,
  maxParticipants: number,
  initialParticipant: {
    name: string;
    email: string;
    phone?: string;
    notes?: string;
  },
  googleAccessToken?: string,
  outlookAccessToken?: string,
  businessTimezone: string = 'UTC'
): Promise<string> => {
  try {
    // Create the group appointment
    const groupAppointment = {
      ...appointmentData,
      isGroupAppointment: true,
      maxParticipants,
      currentParticipants: 1,
      participants: [initialParticipant],
    };

    // Save the group appointment
    const appointmentRef = await createAppointment(
      groupAppointment,
      'email_ics' // Default to email_ics for group appointments
    );

    console.log(`Created group appointment: ${appointmentRef.id}`);
    return appointmentRef.id;
  } catch (error) {
    console.error('Error creating group appointment:', error);
    throw error;
  }
};

/**
 * Adds a participant to a group appointment
 * @param appointmentId The ID of the group appointment
 * @param participant The participant data
 * @returns A promise that resolves when the participant is added
 */
export const addParticipantToGroupAppointment = async (
  appointmentId: string,
  participant: {
    name: string;
    email: string;
    phone?: string;
    notes?: string;
  }
): Promise<void> => {
  try {
    // Get the appointment
    const appointmentDoc = await getDoc(doc(db, appointmentsCollection, appointmentId));
    if (!appointmentDoc.exists()) {
      throw new Error(`Appointment ${appointmentId} not found`);
    }
    
    const appointment = { id: appointmentDoc.id, ...appointmentDoc.data() } as Appointment;
    
    // Check if this is a group appointment
    if (!appointment || !appointment.isGroupAppointment) {
      throw new Error(`Appointment ${appointmentId} is not a group appointment`);
    }
    
    // Check if the appointment is full
    if (appointment.currentParticipants && appointment.maxParticipants && 
        appointment.currentParticipants >= appointment.maxParticipants) {
      throw new Error(`Group appointment ${appointmentId} is full`);
    }
    
    // Add the participant
    const participants = appointment.participants || [];
    participants.push(participant);
    
    // Update the appointment
    await updateDoc(doc(db, appointmentsCollection, appointmentId), {
      participants,
      currentParticipants: increment(1),
      updatedAt: serverTimestamp()
    });
    
    console.log(`Added participant to group appointment ${appointmentId}`);
  } catch (error) {
    console.error('Error adding participant to group appointment:', error);
    throw error;
  }
};

/**
 * Removes a participant from a group appointment
 * @param appointmentId The ID of the group appointment
 * @param participantEmail The email of the participant to remove
 * @returns A promise that resolves when the participant is removed
 */
export const removeParticipantFromGroupAppointment = async (
  appointmentId: string,
  participantEmail: string
): Promise<void> => {
  try {
    // Get the appointment
    const appointmentDoc = await getDoc(doc(db, appointmentsCollection, appointmentId));
    if (!appointmentDoc.exists()) {
      throw new Error(`Appointment ${appointmentId} not found`);
    }
    
    const appointment = { id: appointmentDoc.id, ...appointmentDoc.data() } as Appointment;
    
    // Check if this is a group appointment
    if (!appointment || !appointment.isGroupAppointment) {
      throw new Error(`Appointment ${appointmentId} is not a group appointment`);
    }
    
    // Find the participant
    const participants = appointment.participants || [];
    const participantIndex = participants.findIndex(p => p.email === participantEmail);
    
    if (participantIndex === -1) {
      throw new Error(`Participant with email ${participantEmail} not found in appointment ${appointmentId}`);
    }
    
    // Remove the participant
    participants.splice(participantIndex, 1);
    
    // Update the appointment
    await updateDoc(doc(db, appointmentsCollection, appointmentId), {
      participants,
      currentParticipants: increment(-1),
      updatedAt: serverTimestamp()
    });
    
    console.log(`Removed participant from group appointment ${appointmentId}`);
  } catch (error) {
    console.error('Error removing participant from group appointment:', error);
    throw error;
  }
};

/**
 * Gets all participants in a group appointment
 * @param appointmentId The ID of the group appointment
 * @returns An array of participants
 */
export const getGroupAppointmentParticipants = async (
  appointmentId: string
): Promise<NonNullable<Appointment['participants']>> => {
  try {
    // Get the appointment
    const appointment = await getAppointmentById(appointmentId);
    
    // Check if this is a group appointment
    if (!appointment || !appointment.isGroupAppointment) {
      throw new Error(`Appointment ${appointmentId} is not a group appointment`);
    }
    
    return appointment.participants || [];
  } catch (error) {
    console.error('Error getting group appointment participants:', error);
    throw error;
  }
};

/**
 * Checks if a group appointment has available spots
 * @param appointmentId The ID of the group appointment
 * @returns A boolean indicating if the appointment has available spots
 */
export const hasAvailableSpots = async (
  appointmentId: string
): Promise<boolean> => {
  try {
    // Get the appointment
    const appointment = await getAppointmentById(appointmentId);
    
    // Check if this is a group appointment
    if (!appointment || !appointment.isGroupAppointment) {
      throw new Error(`Appointment ${appointmentId} is not a group appointment`);
    }
    
    // Check if the appointment has available spots
    return (
      appointment.currentParticipants !== undefined && 
      appointment.maxParticipants !== undefined && 
      appointment.currentParticipants < appointment.maxParticipants
    );
  } catch (error) {
    console.error('Error checking if group appointment has available spots:', error);
    throw error;
  }
};

/**
 * Gets all group appointments for a business
 * @param businessId The ID of the business
 * @returns An array of group appointments
 */
export const getBusinessGroupAppointments = async (
  businessId: string
): Promise<Appointment[]> => {
  try {
    // Query for group appointments
    const groupAppointmentsQuery = query(
      collection(db, appointmentsCollection),
      where('businessId', '==', businessId),
      where('isGroupAppointment', '==', true)
    );
    
    const groupAppointmentsSnapshot = await getDocs(groupAppointmentsQuery);
    
    // Map the documents to appointments
    return groupAppointmentsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Appointment));
  } catch (error) {
    console.error('Error getting business group appointments:', error);
    throw error;
  }
};
