import { Appointment } from '@/lib/types/db';
import { Timestamp } from 'firebase/firestore';

/**
 * Generates an ICS (iCalendar) file content for an appointment
 * @param appointment The appointment data
 * @param businessName The name of the business
 * @param businessAddress Optional business address
 * @param businessEmail Optional business email (<NAME_EMAIL>)
 * @returns ICS file content as a string
 */
export function generateICSContent(
  appointment: Appointment,
  businessName: string,
  businessAddress?: string,
  businessEmail?: string
): string {
  // Convert Firestore timestamps to Date objects with validation
  let startDate: Date;
  let endDate: Date;

  try {
    // Handle startTime conversion
    if (appointment.startTime instanceof Timestamp) {
      startDate = appointment.startTime.toDate();
    } else if (Object.prototype.toString.call(appointment.startTime) === '[object Date]') {
      startDate = appointment.startTime as Date;
    } else if (typeof appointment.startTime === 'string' || typeof appointment.startTime === 'number') {
      startDate = new Date(appointment.startTime);
    } else {
      throw new Error('Invalid startTime format in appointment');
    }

    // Handle endTime conversion
    if (appointment.endTime instanceof Timestamp) {
      endDate = appointment.endTime.toDate();
    } else if (Object.prototype.toString.call(appointment.endTime) === '[object Date]') {
      endDate = appointment.endTime as Date;
    } else if (typeof appointment.endTime === 'string' || typeof appointment.endTime === 'number') {
      endDate = new Date(appointment.endTime);
    } else {
      throw new Error('Invalid endTime format in appointment');
    }

    // Validate that dates are valid
    if (isNaN(startDate.getTime())) {
      throw new Error(`Invalid startTime date: ${appointment.startTime}`);
    }
    if (isNaN(endDate.getTime())) {
      throw new Error(`Invalid endTime date: ${appointment.endTime}`);
    }

    console.log('Successfully parsed appointment dates:', {
      appointmentId: appointment.id,
      startTime: startDate.toISOString(),
      endTime: endDate.toISOString()
    });
  } catch (error) {
    console.error('Error parsing appointment dates for ICS generation:', error);
    console.error('Appointment data:', {
      id: appointment.id,
      startTime: appointment.startTime,
      endTime: appointment.endTime,
      startTimeType: typeof appointment.startTime,
      endTimeType: typeof appointment.endTime
    });
    throw new Error(`Failed to parse appointment dates for ICS generation: ${error}`);
  }

  // Format dates for ICS (YYYYMMDDTHHMMSSZ format in UTC)
  const formatICSDate = (date: Date): string => {
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      throw new Error('Invalid date passed to formatICSDate');
    }
    return date.toISOString().replace(/[-:]/g, '').replace(/\.\d{3}/, '');
  };

  // Generate a unique UID for the event
  const uid = `appointment-${appointment.id}@onpointly.com`;

  // Create the ICS content
  const icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Onpointly//Appointment Scheduler//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:REQUEST',
    'BEGIN:VEVENT',
    `UID:${uid}`,
    `DTSTART:${formatICSDate(startDate)}`,
    `DTEND:${formatICSDate(endDate)}`,
    `DTSTAMP:${formatICSDate(new Date())}`,
    `SUMMARY:${appointment.serviceName || 'Appointment'} - ${businessName}`,
    `DESCRIPTION:Appointment with ${businessName}\\n\\n` +
    `Service: ${appointment.serviceName || 'Not specified'}\\n` +
    `Client: ${appointment.clientName}\\n` +
    `Staff: ${appointment.staffName || 'Not specified'}\\n` +
    `${appointment.notes ? `Notes: ${appointment.notes}\\n` : ''}\\n` +
    `To reschedule or view details, visit: ${process.env.NEXT_PUBLIC_APP_URL}/client-portal/appointments/${appointment.id}`,
    `ORGANIZER;CN=${businessName}:mailto:${businessEmail}`,
    `ATTENDEE;CN=${appointment.clientName};RSVP=TRUE:mailto:${appointment.clientEmail}`,
    ...(businessAddress ? [`LOCATION:${businessAddress}`] : []),
    'STATUS:CONFIRMED',
    'SEQUENCE:0',
    'BEGIN:VALARM',
    'TRIGGER:-PT15M',
    'ACTION:DISPLAY',
    'DESCRIPTION:Reminder: Your appointment is in 15 minutes',
    'END:VALARM',
    'END:VEVENT',
    'END:VCALENDAR'
  ].join('\r\n');

  return icsContent;
}

/**
 * Generates an ICS file name for an appointment
 * @param appointment The appointment data
 * @param businessName The name of the business
 * @returns A filename for the ICS file
 */
export function generateICSFileName(appointment: Appointment, businessName: string): string {
  let startDate: Date;

  try {
    // Handle startTime conversion with validation
    if (appointment.startTime instanceof Timestamp) {
      startDate = appointment.startTime.toDate();
    } else if (Object.prototype.toString.call(appointment.startTime) === '[object Date]') {
      startDate = appointment.startTime as Date;
    } else if (typeof appointment.startTime === 'string' || typeof appointment.startTime === 'number') {
      startDate = new Date(appointment.startTime);
    } else {
      throw new Error('Invalid startTime format in appointment');
    }

    // Validate that date is valid
    if (isNaN(startDate.getTime())) {
      throw new Error(`Invalid startTime date: ${appointment.startTime}`);
    }

    const dateStr = startDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    const sanitizedBusinessName = businessName.replace(/[^a-zA-Z0-9]/g, '_');
    const sanitizedServiceName = (appointment.serviceName || 'appointment').replace(/[^a-zA-Z0-9]/g, '_');

    return `${sanitizedBusinessName}_${sanitizedServiceName}_${dateStr}_${appointment.id}.ics`;
  } catch (error) {
    console.error('Error generating ICS filename:', error);
    console.error('Appointment data:', {
      id: appointment.id,
      startTime: appointment.startTime,
      startTimeType: typeof appointment.startTime
    });

    // Fallback filename if date parsing fails
    const sanitizedBusinessName = businessName.replace(/[^a-zA-Z0-9]/g, '_');
    const sanitizedServiceName = (appointment.serviceName || 'appointment').replace(/[^a-zA-Z0-9]/g, '_');
    return `${sanitizedBusinessName}_${sanitizedServiceName}_${appointment.id}.ics`;
  }
}

/**
 * Creates a Blob object from ICS content for download
 * @param icsContent The ICS file content
 * @returns A Blob object
 */
export function createICSBlob(icsContent: string): Blob {
  return new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
}

/**
 * Triggers a download of an ICS file in the browser
 * @param icsContent The ICS file content
 * @param fileName The filename for the download
 */
export function downloadICSFile(icsContent: string, fileName: string): void {
  const blob = createICSBlob(icsContent);
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
}

/**
 * Validates ICS content format
 * @param icsContent The ICS content to validate
 * @returns True if valid, false otherwise
 */
export function validateICSContent(icsContent: string): boolean {
  const requiredLines = [
    'BEGIN:VCALENDAR',
    'END:VCALENDAR',
    'BEGIN:VEVENT',
    'END:VEVENT',
    'UID:',
    'DTSTART:',
    'DTEND:'
  ];
  
  return requiredLines.every(line => icsContent.includes(line));
}
