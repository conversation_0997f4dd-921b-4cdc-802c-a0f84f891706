import { collection, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Appointment, StaffMember, Service } from '@/lib/types/db';

interface TimeSlot {
  start: Date;
  end: Date;
  available: boolean;
  reason?: string;
}

interface AvailabilityOptions {
  businessId: string;
  serviceId: string;
  staffId?: string;
  date: Date;
  duration: number; // in minutes
  checkGoogleCalendar?: boolean;
}

/**
 * Enhanced availability checker that combines business calendar and Google Calendar
 */
export class EnhancedAvailabilityService {
  /**
   * Gets available time slots for a given date and service
   */
  static async getAvailableSlots(options: AvailabilityOptions): Promise<TimeSlot[]> {
    const { businessId, serviceId, staffId, date, duration, checkGoogleCalendar = true } = options;
    
    try {
      // Get business hours for the day
      const businessHours = await this.getBusinessHours(businessId, date);
      if (!businessHours) {
        return [];
      }
      
      // Get staff availability if specific staff is selected
      const staffAvailability = staffId 
        ? await this.getStaffAvailability(staffId, date)
        : null;
      
      // Get existing appointments from business calendar
      const existingAppointments = await this.getExistingAppointments(businessId, staffId, date);
      
      // Get Google Calendar conflicts if enabled and staff has Google Calendar connected
      const googleCalendarConflicts = checkGoogleCalendar && staffId
        ? await this.getGoogleCalendarConflicts(staffId, date)
        : [];
      
      // Generate time slots based on business hours
      const allSlots = this.generateTimeSlots(businessHours.start, businessHours.end, duration);
      
      // Filter out unavailable slots
      const availableSlots = allSlots.map(slot => {
        const conflicts = this.checkForConflicts(slot, {
          existingAppointments,
          googleCalendarConflicts,
          staffAvailability
        });
        
        return {
          ...slot,
          available: conflicts.length === 0,
          reason: conflicts.length > 0 ? conflicts.join(', ') : undefined
        };
      });
      
      return availableSlots;
    } catch (error) {
      console.error('Error getting available slots:', error);
      throw error;
    }
  }
  
  /**
   * Gets business hours for a specific date
   */
  private static async getBusinessHours(businessId: string, date: Date): Promise<{ start: Date; end: Date } | null> {
    try {
      // Get day of week (0 = Sunday, 1 = Monday, etc.)
      const dayOfWeek = date.getDay();
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const dayName = dayNames[dayOfWeek];
      
      // For now, return default business hours (9 AM - 5 PM)
      // This should be fetched from business settings in the future
      const startHour = 9;
      const endHour = 17;
      
      const start = new Date(date);
      start.setHours(startHour, 0, 0, 0);
      
      const end = new Date(date);
      end.setHours(endHour, 0, 0, 0);
      
      return { start, end };
    } catch (error) {
      console.error('Error getting business hours:', error);
      return null;
    }
  }
  
  /**
   * Gets staff availability for a specific date
   */
  private static async getStaffAvailability(staffId: string, date: Date): Promise<{ start: Date; end: Date }[] | null> {
    try {
      // This should fetch staff-specific availability from the database
      // For now, return null (use business hours)
      return null;
    } catch (error) {
      console.error('Error getting staff availability:', error);
      return null;
    }
  }
  
  /**
   * Gets existing appointments from the business calendar
   */
  private static async getExistingAppointments(businessId: string, staffId: string | undefined, date: Date): Promise<Appointment[]> {
    try {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);
      
      let appointmentsQuery = query(
        collection(db, 'appointments'),
        where('businessId', '==', businessId),
        where('startTime', '>=', Timestamp.fromDate(startOfDay)),
        where('startTime', '<=', Timestamp.fromDate(endOfDay)),
        where('status', 'in', ['confirmed', 'pending'])
      );
      
      // If specific staff is selected, filter by staff
      if (staffId) {
        appointmentsQuery = query(
          collection(db, 'appointments'),
          where('businessId', '==', businessId),
          where('staffId', '==', staffId),
          where('startTime', '>=', Timestamp.fromDate(startOfDay)),
          where('startTime', '<=', Timestamp.fromDate(endOfDay)),
          where('status', 'in', ['confirmed', 'pending'])
        );
      }
      
      const snapshot = await getDocs(appointmentsQuery);
      const appointments: Appointment[] = [];
      
      snapshot.forEach(doc => {
        appointments.push({ id: doc.id, ...doc.data() } as Appointment);
      });
      
      return appointments;
    } catch (error) {
      console.error('Error getting existing appointments:', error);
      return [];
    }
  }
  
  /**
   * Gets Google Calendar conflicts for a staff member
   */
  private static async getGoogleCalendarConflicts(staffId: string, date: Date): Promise<{ start: Date; end: Date }[]> {
    try {
      // This would integrate with Google Calendar API
      // For now, return empty array
      // TODO: Implement Google Calendar integration
      return [];
    } catch (error) {
      console.error('Error getting Google Calendar conflicts:', error);
      return [];
    }
  }
  
  /**
   * Generates time slots for a given time range
   */
  private static generateTimeSlots(start: Date, end: Date, duration: number): TimeSlot[] {
    const slots: TimeSlot[] = [];
    const current = new Date(start);
    
    while (current < end) {
      const slotEnd = new Date(current.getTime() + duration * 60000);
      
      if (slotEnd <= end) {
        slots.push({
          start: new Date(current),
          end: slotEnd,
          available: true
        });
      }
      
      // Move to next slot (15-minute intervals)
      current.setMinutes(current.getMinutes() + 15);
    }
    
    return slots;
  }
  
  /**
   * Checks for conflicts with existing appointments and other constraints
   */
  private static checkForConflicts(
    slot: TimeSlot,
    constraints: {
      existingAppointments: Appointment[];
      googleCalendarConflicts: { start: Date; end: Date }[];
      staffAvailability: { start: Date; end: Date }[] | null;
    }
  ): string[] {
    const conflicts: string[] = [];
    
    // Check existing appointments
    for (const appointment of constraints.existingAppointments) {
      const appointmentStart = appointment.startTime instanceof Timestamp 
        ? appointment.startTime.toDate() 
        : new Date(appointment.startTime);
      const appointmentEnd = appointment.endTime instanceof Timestamp 
        ? appointment.endTime.toDate() 
        : new Date(appointment.endTime);
      
      if (this.timesOverlap(slot.start, slot.end, appointmentStart, appointmentEnd)) {
        conflicts.push('Existing appointment');
        break;
      }
    }
    
    // Check Google Calendar conflicts
    for (const conflict of constraints.googleCalendarConflicts) {
      if (this.timesOverlap(slot.start, slot.end, conflict.start, conflict.end)) {
        conflicts.push('Google Calendar conflict');
        break;
      }
    }
    
    // Check staff availability
    if (constraints.staffAvailability) {
      const isWithinStaffHours = constraints.staffAvailability.some(availability =>
        slot.start >= availability.start && slot.end <= availability.end
      );
      
      if (!isWithinStaffHours) {
        conflicts.push('Outside staff hours');
      }
    }
    
    return conflicts;
  }
  
  /**
   * Checks if two time ranges overlap
   */
  private static timesOverlap(start1: Date, end1: Date, start2: Date, end2: Date): boolean {
    return start1 < end2 && end1 > start2;
  }
}
