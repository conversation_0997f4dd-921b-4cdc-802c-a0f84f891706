// src/lib/services/mockAppointmentService.ts
import { Appointment } from '../types/db';
import { Timestamp } from 'firebase/firestore';

// In-memory storage for appointments
let appointments: Appointment[] = [];

/**
 * Creates a new appointment in the mock database
 * @param appointmentData The appointment data to create
 * @returns A promise that resolves with a mock document reference
 */
export const createMockAppointment = async (
  appointmentData: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>,
  addToGoogleCalendar: boolean = false
): Promise<{ id: string }> => {
  // Generate a unique ID
  const id = `mock-appt-${Date.now()}`;
  
  // Create the appointment
  const newAppointment: Appointment = {
    id,
    ...appointmentData,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  };
  
  // Add to in-memory storage
  appointments.push(newAppointment);
  
  console.log('Mock appointment created:', newAppointment);
  
  // Simulate Google Calendar integration
  if (addToGoogleCalendar) {
    console.log('Mock: Adding to Google Calendar');
    newAppointment.googleCalendarEventId = `mock-google-event-${Date.now()}`;
  }
  
  return { id };
};

/**
 * Gets all appointments for a business from the mock database
 * @param businessId The business ID
 * @returns A promise that resolves with an array of appointments
 */
export const getMockBusinessAppointments = async (businessId: string): Promise<Appointment[]> => {
  // Filter appointments by business ID
  return appointments.filter(appt => appt.businessId === businessId);
};

/**
 * Updates an appointment in the mock database
 * @param appointmentId The appointment ID
 * @param data The data to update
 * @returns A promise that resolves when the update is complete
 */
export const updateMockAppointment = async (
  appointmentId: string,
  data: Partial<Appointment>
): Promise<void> => {
  // Find the appointment
  const index = appointments.findIndex(appt => appt.id === appointmentId);
  
  if (index === -1) {
    throw new Error(`Appointment with ID ${appointmentId} not found`);
  }
  
  // Update the appointment
  appointments[index] = {
    ...appointments[index],
    ...data,
  };
  
  console.log('Mock appointment updated:', appointments[index]);
};

/**
 * Deletes an appointment from the mock database
 * @param appointmentId The appointment ID
 * @returns A promise that resolves when the deletion is complete
 */
export const deleteMockAppointment = async (appointmentId: string): Promise<void> => {
  // Filter out the appointment
  appointments = appointments.filter(appt => appt.id !== appointmentId);
  
  console.log('Mock appointment deleted:', appointmentId);
};
