import { Appointment, WaitlistEntry, StaffMember } from '@/lib/types/db';
import { ReviewRequest } from '@/lib/types/reviews';
import { Resend } from 'resend';
import { sendMailgunEmail } from './mailgunService';
import { format } from 'date-fns';
import {
  COMPANY_NAME,
  NOTIFICATION_SENDER,
  APPOINTMENTS_SENDER,
  SUPPORT_SENDER,
  EMAIL_FOOTER_TEXT
} from '@/lib/constants';
import { uploadICSFile } from './firebaseStorageService';
import { generateICSContent, generateICSFileName, createICSBlob } from './icsService';

// Mock email functions for development
const sendMockEmail = (to: string, subject: string, html: string) => {
  console.log(`[MOCK EMAIL] To: ${to}, Subject: ${subject}`);
  return Promise.resolve();
};

const sendMockAppointmentConfirmationEmail = (appointment: Appointment, businessName: string) => {
  console.log(`[MOCK] Appointment confirmation for ${appointment.clientName} at ${businessName}`);
  return Promise.resolve();
};

const sendMockWaitlistNotificationEmail = (waitlistEntry: WaitlistEntry, businessName: string) => {
  console.log(`[MOCK] Waitlist notification for ${waitlistEntry.clientName} at ${businessName}`);
  return Promise.resolve();
};

const sendMockReviewRequestEmail = (reviewRequest: ReviewRequest, appointment: Appointment, businessName: string, reviewUrl: string) => {
  console.log(`[MOCK] Review request for ${appointment.clientName} at ${businessName}`);
  console.log(`[MOCK] Review URL: ${reviewUrl}`);
  console.log(`[MOCK] Service: ${appointment.serviceName || 'Not specified'}`);
  console.log(`[MOCK] Staff: ${appointment.staffName || 'Not specified'}`);
  return Promise.resolve();
};

const sendMockStaffOnboardingEmail = (staff: StaffMember, businessName: string, loginUrl: string, tempPassword: string) => {
  console.log(`[MOCK] Staff onboarding for ${staff.name} at ${businessName}`);
  return Promise.resolve();
};

// Initialize Resend with proper error handling
let resend: Resend | null = null;
try {
  // Use environment variable for API key
  const RESEND_API_KEY = process.env.NEXT_PUBLIC_RESEND_API_KEY || process.env.RESEND_API_KEY;

  if (RESEND_API_KEY) {
    resend = new Resend(RESEND_API_KEY);
    // Resend email service initialized
  } else {
    console.warn('RESEND_API_KEY is not set');
  }
} catch (error) {
  console.error('Failed to initialize Resend email service:', error);
}

// Interface for email data (used by the API route)
// export interface EmailData {
//   to: string;
//   subject: string;
//   html: string;
// }

// We've removed SMTP functionality and will only use Mailgun for email services

// Check if we're in a development environment
// For appointment emails, we want to send real emails even in development
const isDevelopment = process.env.NODE_ENV !== 'production' && process.env.FORCE_REAL_EMAILS !== 'true';

// Debug logging for email service configuration
console.log('📧 Email Service Configuration:');
console.log(`- NODE_ENV: "${process.env.NODE_ENV}" (type: ${typeof process.env.NODE_ENV})`);
console.log(`- FORCE_REAL_EMAILS: "${process.env.FORCE_REAL_EMAILS}" (type: ${typeof process.env.FORCE_REAL_EMAILS})`);
console.log(`- isDevelopment calculation: ${process.env.NODE_ENV !== 'production'} && ${process.env.FORCE_REAL_EMAILS !== 'true'} = ${isDevelopment}`);
console.log(`- MAILGUN_DOMAIN: ${process.env.NEXT_PUBLIC_MAILGUN_DOMAIN || 'NOT SET'}`);
console.log(`- MAILGUN_FROM: ${process.env.NEXT_PUBLIC_MAILGUN_FROM || 'NOT SET'}`);
console.log('================================');

// Add debug logging for email service
// Email service initialized

/**
 * Sends an onboarding email to a newly created staff member
 * @param staff The staff member
 * @param businessName The name of the business
 * @returns A promise that resolves when the email is sent
 */
export const sendStaffOnboardingEmail = async (
  staff: StaffMember,
  businessName: string
): Promise<void> => {
  try {
    // Sending staff onboarding email

    // Generate the login URL
    const loginUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/auth/staff-login`;

    // Generate a temporary password for the staff member
    const tempPassword = generateTemporaryPassword();

    // Create the email HTML content
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Welcome to ${businessName}</h1>
        </div>

        <div style="padding: 20px; border: 1px solid #e5e7eb; border-top: none;">
          <p>Hello ${staff.name},</p>

          <p>You have been added as a staff member to ${businessName} on Onpointly, our appointment scheduling system.</p>

          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h2 style="margin-top: 0; color: #4f46e5;">Your Account Details</h2>
            <p><strong>Email:</strong> ${staff.email}</p>
            <p><strong>Temporary Password:</strong> ${tempPassword}</p>
            <p><strong>Role:</strong> ${staff.role || 'Staff Member'}</p>
          </div>

          <p>To access your staff dashboard, please follow these steps:</p>

          <ol style="margin-bottom: 20px;">
            <li>Go to the staff login page: <a href="${loginUrl}" style="color: #4f46e5;">${loginUrl}</a></li>
            <li>Enter your email address: ${staff.email}</li>
            <li>Enter your temporary password: ${tempPassword}</li>
            <li>You'll be prompted to change your password after your first login.</li>
          </ol>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${loginUrl}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Login to Your Dashboard</a>
          </div>

          <p>From your dashboard, you'll be able to:</p>
          <ul>
            <li>View your schedule and upcoming appointments</li>
            <li>Manage your availability</li>
            <li>View client information</li>
            <li>Connect your Google Calendar (recommended)</li>
          </ul>

          <p>If you have any questions, please contact the business administrator.</p>

          <p>Best regards,<br>${businessName} Team</p>
        </div>

        <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p>${EMAIL_FOOTER_TEXT}</p>
        </div>
      </div>
    `;

    // Check for development environment
    if (isDevelopment) {
      // Skip sending real emails in development
      return;
    }

    // Try multiple methods to send the email, with detailed logging
    console.log('Starting staff onboarding email process for:', staff.email);

    // First attempt: Use the Mailgun service directly
    try {
      console.log('Attempting to send staff onboarding email via Mailgun service...');

      // Import the mailgun service
      const { sendMailgunEmail } = await import('@/lib/services/mailgunService');

      // Create the email HTML
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">Welcome to ${businessName}!</h1>
          </div>
          <div style="padding: 20px;">
            <p>Hello ${staff.name},</p>
            <p>Your staff account has been created for ${businessName}.</p>
            <p>You can log in using the following credentials:</p>
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p><strong>Email:</strong> ${staff.email}</p>
              <p><strong>Temporary Password:</strong> ${tempPassword}</p>
              <p><strong>Login URL:</strong> <a href="${loginUrl}">${loginUrl}</a></p>
            </div>
            <p>For security reasons, please change your password after logging in for the first time.</p>
            <p>If you have any questions, please contact your administrator.</p>
            <p>Thank you,<br>${businessName} Team</p>
          </div>
        </div>
      `;

      const result = await sendMailgunEmail(
        staff.email,
        `Welcome to ${businessName} - Staff Account Created`,
        emailHtml,
        `${businessName} <<EMAIL>>`
      );

      if (result.success) {
        console.log('Staff onboarding email sent successfully via Mailgun service:', result.id);
        return;
      } else {
        console.error('Mailgun service failed to send email:', result.details);
        throw new Error(result.details || 'Mailgun service failed to send email');
      }
    } catch (mailgunError) {
      console.error('Error sending email via Mailgun service:', mailgunError);
      // Continue to next attempt
    }

    // Second attempt: Use the Firebase function
    try {
      console.log('Attempting to send staff onboarding email via Firebase function...');

      // Import the Firebase functions SDK dynamically to avoid circular dependencies
      const { getFunctions, httpsCallable } = await import('firebase/functions');
      const { app } = await import('@/lib/firebase/config');

      const functions = getFunctions(app);
      const sendStaffOnboardingEmailFn = httpsCallable(functions, 'sendStaffOnboardingEmail');

      const result = await sendStaffOnboardingEmailFn({
        email: staff.email,
        name: staff.name,
        businessId: staff.businessId,
        businessName,
        tempPassword,
      });

      console.log('Staff onboarding email sent successfully via Firebase function:', result.data);
      return;
    } catch (fnError) {
      console.error('Error sending email via Firebase function:', fnError);
      // Continue to next attempt
    }

    // Third attempt: Try to send via the API route
    try {
      console.log('Attempting to send staff onboarding email via API route...');

      const response = await fetch('/api/staff-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: staff.email,
          name: staff.name,
          businessId: staff.businessId,
          businessName,
          tempPassword,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `API route responded with status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Staff onboarding email sent successfully via API route:', data.id);
      return;
    } catch (apiError) {
      console.error('Error sending email via API route:', apiError);
      // Continue to final fallback
    }

    // Final fallback: Use mock email service and log a warning
    console.warn('All email sending methods failed. Falling back to mock email service.');
    console.warn('IMPORTANT: The staff member will NOT receive an actual email with their credentials.');
    console.warn('Please provide the following credentials to the staff member manually:');
    console.warn(`Email: ${staff.email}`);
    console.warn(`Temporary Password: ${tempPassword}`);
    console.warn(`Login URL: ${loginUrl}`);

    await sendMockStaffOnboardingEmail(staff, businessName, loginUrl, tempPassword);

    // Throw an error to indicate that no real email was sent
    throw new Error('Failed to send staff onboarding email through all available methods. Manual credential sharing required.');
  } catch (error) {
    console.error('Error sending staff onboarding email:', error);
    throw error;
  }
};

/**
 * Send appointment reschedule notification email with updated ICS file
 * @param appointment The updated appointment data
 * @param businessName The business name
 * @param timeZone The business timezone
 * @param businessAddress Optional business address
 * @param sendToAll Whether to send emails to all parties (client, business owner, and staff)
 */
export const sendAppointmentRescheduleEmailWithICS = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string,
  businessAddress?: string,
  sendToAll: boolean = true
): Promise<void> => {
  console.log('🔄 Starting sendAppointmentRescheduleEmailWithICS');
  console.log(`- Client Email: ${appointment.clientEmail}`);
  console.log(`- Business Name: ${businessName}`);
  console.log(`- Send To All: ${sendToAll}`);

  try {
    // Check if we should use mock email service
    console.log(`🔍 Email service decision: isDevelopment = ${isDevelopment}`);

    // TEMPORARY FIX: Force real emails for appointment confirmations
    const forceRealEmails = true;
    console.log(`🔧 FORCING REAL EMAILS: ${forceRealEmails}`);

    if (isDevelopment && !forceRealEmails) {
      console.log("⚠️  Development environment - using mock email service");
      await sendMockAppointmentConfirmationEmail(appointment, businessName);
      return;
    }

    console.log("✅ Production mode or FORCE_REAL_EMAILS=true - sending real emails via Mailgun");

    // Generate ICS content for the rescheduled appointment
    const icsContent = generateICSContent(appointment, businessName, businessAddress);
    const icsFileName = generateICSFileName(appointment, businessName);

    // Format appointment times for display
    const startTimeDate = appointment.startTime instanceof Date ? appointment.startTime : appointment.startTime.toDate();
    const endTimeDate = appointment.endTime instanceof Date ? appointment.endTime : appointment.endTime.toDate();

    const appointmentDate = format(startTimeDate, 'EEEE, MMMM d, yyyy');
    const startTime = format(startTimeDate, 'h:mm a');
    const endTime = format(endTimeDate, 'h:mm a');

    // Create the appointment view URL (public page, no authentication required)
    const appointmentViewURL = `${process.env.NEXT_PUBLIC_APP_URL}/appointment/${appointment.id}`;
    const rescheduleURL = `${process.env.NEXT_PUBLIC_APP_URL}/appointment/${appointment.id}/reschedule`;

    // Create enhanced email HTML for reschedule notification
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f59e0b; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">Appointment Rescheduled</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px;">Your appointment has been moved to a new time</p>
        </div>

        <div style="padding: 30px; background-color: #ffffff;">
          <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin-bottom: 25px;">
            <h2 style="color: #92400e; margin: 0 0 10px 0; font-size: 18px;">📅 New Appointment Details</h2>
            <p style="margin: 0; color: #92400e;">Your appointment has been successfully rescheduled.</p>
          </div>

          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
            <h3 style="margin: 0 0 15px 0; color: #1f2937; font-size: 18px;">${appointment.serviceName}</h3>

            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">📅 Date:</strong>
              <span style="color: #6b7280; margin-left: 10px;">${appointmentDate}</span>
            </div>

            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">🕐 Time:</strong>
              <span style="color: #6b7280; margin-left: 10px;">${startTime} - ${endTime}</span>
            </div>

            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">👤 Staff:</strong>
              <span style="color: #6b7280; margin-left: 10px;">${appointment.staffName || 'Any available staff'}</span>
            </div>

            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">📍 Client:</strong>
              <span style="color: #6b7280; margin-left: 10px;">${appointment.clientName}</span>
            </div>

            ${(appointment as any).durationMinutes ? `
            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">⏱️ Duration:</strong>
              <span style="color: #6b7280; margin-left: 10px;">${(appointment as any).durationMinutes} minutes</span>
            </div>
            ` : ''}

            ${businessAddress ? `
            <div style="margin-bottom: 15px;">
              <strong style="color: #374151;">📍 Location:</strong>
              <span style="color: #6b7280; margin-left: 10px;">${businessAddress}</span>
            </div>
            ` : ''}
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${appointmentViewURL}" style="display: inline-block; background-color: #f59e0b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 0 10px 10px 0;">View Appointment</a>
            <a href="${rescheduleURL}" style="display: inline-block; background-color: #6b7280; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 0 10px 10px 0;">Reschedule Again</a>
          </div>

          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin-top: 25px;">
            <h4 style="margin: 0 0 10px 0; color: #374151;">📎 Calendar File Attached</h4>
            <p style="margin: 0; color: #6b7280; font-size: 14px;">
              A calendar file (.ics) is attached to this email. You can import it into your calendar application
              (Google Calendar, Outlook, Apple Calendar, etc.) to add this rescheduled appointment to your calendar.
            </p>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 12px;">
            <p style="margin: 0;">This is an automated message from ${businessName}</p>
            <p style="margin: 5px 0 0 0;">Powered by Onpointly</p>
          </div>
        </div>
      </div>
    `;

    // Send the reschedule notification email to CLIENT
    console.log('📧 Sending reschedule email to CLIENT:', appointment.clientEmail);
    const staffReplyTo = appointment.staffEmail || '<EMAIL>';
    console.log('📧 Using reply-to email:', staffReplyTo);

    const clientResult = await sendMailgunEmail(
      appointment.clientEmail,
      `Appointment Rescheduled - ${businessName}`,
      emailHtml,
      `${businessName} <<EMAIL>>`,
      [
        {
          filename: icsFileName,
          content: Buffer.from(icsContent).toString('base64'),
          contentType: 'text/calendar'
        }
      ],
      staffReplyTo // Reply-to staff email
    );

    if (clientResult.success) {
      console.log('✅ Reschedule notification email sent successfully to CLIENT via Mailgun:', clientResult.id);
    } else {
      console.error('❌ Failed to send reschedule email to CLIENT via Mailgun:', clientResult.details);
      throw new Error(clientResult.details || 'Failed to send reschedule email to CLIENT via Mailgun');
    }

    // Send emails to business owner and staff if requested
    if (sendToAll) {
      console.log('📧 Sending reschedule notifications to business owner and staff...');

      try {
        // Send to business owner
        console.log('📧 Sending reschedule email to BUSINESS OWNER...');
        await sendBusinessOwnerRescheduleNotification(appointment, businessName, timeZone, businessAddress);
        console.log('✅ Business owner reschedule notification sent successfully');
      } catch (ownerEmailError) {
        console.error('❌ Error sending reschedule email to business owner:', ownerEmailError);
        // Continue with staff notification even if owner email fails
      }

      // Send to staff member if assigned
      if (appointment.staffId || appointment.staffEmail) {
        try {
          console.log('📧 Sending reschedule email to STAFF MEMBER...');
          await sendStaffRescheduleNotification(appointment, businessName, timeZone, businessAddress);
          console.log('✅ Staff reschedule notification sent successfully');
        } catch (staffEmailError) {
          console.error('❌ Error sending reschedule email to staff member:', staffEmailError);
          // Don't fail the whole process if staff email fails
        }
      } else {
        console.log('ℹ️ No staff member assigned to this appointment, skipping staff notification');
      }
    } else {
      console.log('ℹ️ sendToAll is false, skipping business owner and staff notifications');
    }

  } catch (error) {
    console.error('❌ Error in sendAppointmentRescheduleEmailWithICS:', error);
    throw error;
  }
};

/**
 * Send reschedule notification to business owner
 */
const sendBusinessOwnerRescheduleNotification = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string,
  businessAddress?: string
): Promise<void> => {
  console.log('📧 Creating business owner reschedule notification...');

  // Generate ICS content for business owner
  const icsContent = generateICSContent(appointment, businessName, businessAddress);
  const icsFileName = generateICSFileName(appointment, businessName);

  // Format appointment times for display
  const startTimeDate = appointment.startTime instanceof Date ? appointment.startTime : appointment.startTime.toDate();
  const endTimeDate = appointment.endTime instanceof Date ? appointment.endTime : appointment.endTime.toDate();

  const appointmentDate = format(startTimeDate, 'EEEE, MMMM d, yyyy');
  const startTime = format(startTimeDate, 'h:mm a');
  const endTime = format(endTimeDate, 'h:mm a');

  // Create business owner email HTML
  const businessOwnerEmailHtml = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #f59e0b; color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0; font-size: 24px;">Appointment Rescheduled</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px;">A client has rescheduled their appointment</p>
      </div>

      <div style="padding: 30px; background-color: #ffffff;">
        <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin-bottom: 25px;">
          <h2 style="color: #92400e; margin: 0 0 10px 0; font-size: 18px;">📅 Rescheduled Appointment</h2>
          <p style="margin: 0; color: #92400e;">This appointment has been moved to a new time.</p>
        </div>

        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
          <h3 style="margin: 0 0 15px 0; color: #1f2937; font-size: 18px;">${appointment.serviceName}</h3>

          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">👤 Client:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${appointment.clientName}</span>
          </div>

          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">📧 Email:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${appointment.clientEmail}</span>
          </div>

          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">📅 New Date:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${appointmentDate}</span>
          </div>

          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">🕐 New Time:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${startTime} - ${endTime}</span>
          </div>

          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">👤 Staff:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${appointment.staffName || 'Any available staff'}</span>
          </div>

          ${(appointment as any).durationMinutes ? `
          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">⏱️ Duration:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${(appointment as any).durationMinutes} minutes</span>
          </div>
          ` : ''}

          ${businessAddress ? `
          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">📍 Location:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${businessAddress}</span>
          </div>
          ` : ''}
        </div>

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin-top: 25px;">
          <h4 style="margin: 0 0 10px 0; color: #374151;">📎 Calendar File Attached</h4>
          <p style="margin: 0; color: #6b7280; font-size: 14px;">
            A calendar file (.ics) is attached to this email with the updated appointment details.
          </p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 12px;">
          <p style="margin: 0;">This is an automated message from ${businessName}</p>
          <p style="margin: 5px 0 0 0;">Powered by Onpointly</p>
        </div>
      </div>
    </div>
  `;

  // Send email to business owner (you'll need to get the business owner email from somewhere)
  // For now, using a placeholder - you should get this from the business data
  const businessOwnerEmail = '<EMAIL>'; // TODO: Get actual business owner email

  console.log('📧 Sending to business owner email:', businessOwnerEmail);

  const result = await sendMailgunEmail(
    businessOwnerEmail,
    `Appointment Rescheduled - ${businessName}`,
    businessOwnerEmailHtml,
    `${businessName} <<EMAIL>>`,
    [
      {
        filename: icsFileName,
        content: Buffer.from(icsContent).toString('base64'),
        contentType: 'text/calendar'
      }
    ]
  );

  if (!result.success) {
    throw new Error(result.details || 'Failed to send business owner reschedule notification');
  }
};

/**
 * Send reschedule notification to staff member
 */
const sendStaffRescheduleNotification = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string,
  businessAddress?: string
): Promise<void> => {
  console.log('📧 Creating staff reschedule notification...');

  // Generate ICS content for staff member
  const icsContent = generateICSContent(appointment, businessName, businessAddress);
  const icsFileName = generateICSFileName(appointment, businessName);

  // Format appointment times for display
  const startTimeDate = appointment.startTime instanceof Date ? appointment.startTime : appointment.startTime.toDate();
  const endTimeDate = appointment.endTime instanceof Date ? appointment.endTime : appointment.endTime.toDate();

  const appointmentDate = format(startTimeDate, 'EEEE, MMMM d, yyyy');
  const startTime = format(startTimeDate, 'h:mm a');
  const endTime = format(endTimeDate, 'h:mm a');

  // Create staff email HTML
  const staffEmailHtml = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #f59e0b; color: white; padding: 20px; text-align: center;">
        <h1 style="margin: 0; font-size: 24px;">Appointment Rescheduled</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px;">Your assigned appointment has been rescheduled</p>
      </div>

      <div style="padding: 30px; background-color: #ffffff;">
        <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin-bottom: 25px;">
          <h2 style="color: #92400e; margin: 0 0 10px 0; font-size: 18px;">📅 Your Appointment Update</h2>
          <p style="margin: 0; color: #92400e;">A client has rescheduled their appointment with you.</p>
        </div>

        <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
          <h3 style="margin: 0 0 15px 0; color: #1f2937; font-size: 18px;">${appointment.serviceName}</h3>

          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">👤 Client:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${appointment.clientName}</span>
          </div>

          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">📧 Client Email:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${appointment.clientEmail}</span>
          </div>

          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">📅 New Date:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${appointmentDate}</span>
          </div>

          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">🕐 New Time:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${startTime} - ${endTime}</span>
          </div>

          ${(appointment as any).durationMinutes ? `
          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">⏱️ Duration:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${(appointment as any).durationMinutes} minutes</span>
          </div>
          ` : ''}

          ${businessAddress ? `
          <div style="margin-bottom: 15px;">
            <strong style="color: #374151;">📍 Location:</strong>
            <span style="color: #6b7280; margin-left: 10px;">${businessAddress}</span>
          </div>
          ` : ''}
        </div>

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin-top: 25px;">
          <h4 style="margin: 0 0 10px 0; color: #374151;">📎 Calendar File Attached</h4>
          <p style="margin: 0; color: #6b7280; font-size: 14px;">
            A calendar file (.ics) is attached to this email with the updated appointment details.
          </p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 12px;">
          <p style="margin: 0;">This is an automated message from ${businessName}</p>
          <p style="margin: 5px 0 0 0;">Powered by Onpointly</p>
        </div>
      </div>
    </div>
  `;

  // Send email to staff member
  const staffEmail = appointment.staffEmail || '<EMAIL>'; // TODO: Get actual staff email

  console.log('📧 Sending to staff email:', staffEmail);

  const result = await sendMailgunEmail(
    staffEmail,
    `Appointment Rescheduled - ${businessName}`,
    staffEmailHtml,
    `${businessName} <<EMAIL>>`,
    [
      {
        filename: icsFileName,
        content: Buffer.from(icsContent).toString('base64'),
        contentType: 'text/calendar'
      }
    ]
  );

  if (!result.success) {
    throw new Error(result.details || 'Failed to send staff reschedule notification');
  }
};

// Helper function to generate a temporary password
function generateTemporaryPassword(length = 10) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
  let password = '';

  // Ensure at least one uppercase, one lowercase, one number, and one special character
  password += charset.charAt(Math.floor(Math.random() * 26)); // Uppercase
  password += charset.charAt(26 + Math.floor(Math.random() * 26)); // Lowercase
  password += charset.charAt(52 + Math.floor(Math.random() * 10)); // Number
  password += charset.charAt(62 + Math.floor(Math.random() * 10)); // Special

  // Fill the rest of the password
  for (let i = 4; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }

  // Shuffle the password
  return password.split('').sort(() => 0.5 - Math.random()).join('');
}

/**
 * Sends appointment confirmation emails with ICS file attachments
 * @param appointment The appointment data
 * @param businessName The business name
 * @param timeZone The business timezone
 * @param businessAddress Optional business address
 * @param sendToAll Whether to send emails to all parties (client, business owner, and staff)
 */
export const sendAppointmentConfirmationEmailWithICS = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string,
  businessAddress?: string,
  sendToAll: boolean = true
): Promise<void> => {
  console.log('🚀🚀🚀 EMAIL FUNCTION CALLED! 🚀🚀🚀');
  console.log('🚀 Starting sendAppointmentConfirmationEmailWithICS');
  console.log(`- Client Email: ${appointment.clientEmail}`);
  console.log(`- Business Name: ${businessName}`);
  console.log(`- Send To All: ${sendToAll}`);
  console.log(`- isDevelopment: ${isDevelopment}`);

  // Email function is being called correctly

  try {
    // Get business email for ICS organizer
    let businessEmail = '<EMAIL>'; // Default fallback
    try {
      const { getDoc, doc } = await import('firebase/firestore');
      const { db } = await import('@/lib/firebase');
      const businessRef = doc(db, 'businesses', appointment.businessId);
      const businessDoc = await getDoc(businessRef);
      if (businessDoc.exists()) {
        const businessData = businessDoc.data();
        if (businessData.email) {
          businessEmail = businessData.email;
        }
      }
    } catch (businessLookupError) {
      console.warn('Could not lookup business email for ICS organizer:', businessLookupError);
    }

    console.log('📧 Using business email for ICS organizer:', businessEmail);

    // Try to generate and upload ICS file to Firebase Storage
    let icsFileURL: string | null = null;
    try {
      icsFileURL = await uploadICSFile(appointment, businessName, businessAddress);
      console.log('ICS file uploaded to Firebase Storage successfully:', icsFileURL);
    } catch (storageError) {
      console.warn('Failed to upload ICS file to Firebase Storage (continuing with email attachment only):', storageError);
      // Continue with email sending even if storage upload fails
    }

    // Generate ICS content for email attachment
    const icsContent = generateICSContent(appointment, businessName, businessAddress, businessEmail);
    const icsFileName = generateICSFileName(appointment, businessName);

    // Handle different date formats (Timestamp vs Date)
    const startTime = appointment.startTime instanceof Date
      ? appointment.startTime
      : appointment.startTime.toDate();
    const endTime = appointment.endTime instanceof Date
      ? appointment.endTime
      : appointment.endTime.toDate();

    const formattedStartTime = startTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedEndTime = endTime.toLocaleString('en-US', {
      timeZone,
      hour: 'numeric',
      minute: '2-digit',
    });

    // Create the appointment view URL (public page, no authentication required)
    const appointmentViewURL = `${process.env.NEXT_PUBLIC_APP_URL}/appointment/${appointment.id}`;
    const rescheduleURL = `${process.env.NEXT_PUBLIC_APP_URL}/appointment/${appointment.id}/reschedule`;

    // Create enhanced email HTML with ICS file and action buttons
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Appointment Confirmed</h1>
        </div>

        <div style="padding: 20px;">
          <p>Hello ${appointment.clientName},</p>
          <p>Your appointment has been confirmed with ${businessName}.</p>

          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4f46e5;">
            <h3 style="margin-top: 0; color: #4f46e5;">Appointment Details</h3>
            <p><strong>📅 Date & Time:</strong> ${formattedStartTime}</p>
            <p><strong>⏰ Duration:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
            <p><strong>🛍️ Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
            ${appointment.staffName ? `<p><strong>👤 Staff:</strong> ${appointment.staffName}</p>` : ''}
            ${businessAddress ? `<p><strong>📍 Location:</strong> ${businessAddress}</p>` : ''}
            ${appointment.notes ? `<p><strong>📝 Notes:</strong> ${appointment.notes}</p>` : ''}
          </div>

          <div style="background-color: #ecfdf5; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981;">
            <p style="margin-top: 0;"><strong>📅 Add to Your Calendar</strong></p>
            <p>We've attached a calendar file (.ics) to this email. Simply open the attachment and your calendar app will import the appointment automatically.</p>
            <p style="margin-bottom: 0;">Compatible with Google Calendar, Outlook, Apple Calendar, and most other calendar applications.</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${appointmentViewURL}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 0 10px; display: inline-block;">View Appointment</a>
            <a href="${rescheduleURL}" style="background-color: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 0 10px; display: inline-block;">Reschedule</a>
          </div>

          <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
            <p style="margin-top: 0;"><strong>⏰ Reminder</strong></p>
            <p style="margin-bottom: 0;">You'll receive a reminder 24 hours and 1 hour before your appointment. Please arrive 5-10 minutes early.</p>
          </div>

          <p>If you need to make any changes or have questions, please don't hesitate to contact us.</p>
          <p>Thank you for choosing ${businessName}!</p>
        </div>

        <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p style="margin-bottom: 5px;">This is an automated message from your appointment scheduling system.</p>
          <p style="margin-top: 0;">${EMAIL_FOOTER_TEXT}</p>
        </div>
      </div>
    `;

    // Check if we should use mock email service
    console.log(`🔍 Email service decision: isDevelopment = ${isDevelopment}`);

    // TEMPORARY FIX: Force real emails for appointment confirmations
    const forceRealEmails = true;
    console.log(`🔧 FORCING REAL EMAILS: ${forceRealEmails}`);

    if (isDevelopment && !forceRealEmails) {
      console.log("⚠️  Development environment - using mock email service");
      await sendMockAppointmentConfirmationEmail(appointment, businessName);
      return;
    }

    console.log("✅ Production mode or FORCE_REAL_EMAILS=true - sending real emails via Mailgun");

    // Try sending with Mailgun (preferred method)
    console.log('📧 About to send main appointment email via Mailgun...');

    // Try sending with Mailgun (preferred method)
    try {
      console.log('📧 Sending appointment confirmation email with ICS attachment via Mailgun...');
      console.log('📧 Email details:', {
        to: appointment.clientEmail,
        subject: `Appointment Confirmed - ${businessName}`,
        from: `${businessName} <<EMAIL>>`,
        icsFileName: icsFileName,
        icsContentLength: icsContent.length
      });

      // Get staff email for reply-to
      let staffReplyTo = '<EMAIL>'; // Default fallback
      if (appointment.staffEmail) {
        staffReplyTo = appointment.staffEmail;
      } else if (appointment.staffId) {
        // Try to get staff email from database
        try {
          const { getDoc, doc } = await import('firebase/firestore');
          const { db } = await import('@/lib/firebase');
          const staffRef = doc(db, 'staff', appointment.staffId);
          const staffDoc = await getDoc(staffRef);
          if (staffDoc.exists()) {
            const staffData = staffDoc.data();
            if (staffData.email) {
              staffReplyTo = staffData.email;
            }
          }
        } catch (staffLookupError) {
          console.warn('Could not lookup staff email for reply-to:', staffLookupError);
        }
      }

      console.log('📧 Using reply-to email:', staffReplyTo);

      const result = await sendMailgunEmail(
        appointment.clientEmail,
        `Appointment Confirmed - ${businessName}`,
        emailHtml,
        `${businessName} <<EMAIL>>`,
        [
          {
            filename: icsFileName,
            content: Buffer.from(icsContent).toString('base64'),
            contentType: 'text/calendar'
          }
        ],
        staffReplyTo // Reply-to staff email
      );

      console.log('📧 Mailgun result:', result);

      if (result.success) {
        console.log('✅ Appointment confirmation email sent successfully to client via Mailgun:', result.id);
        if (icsFileURL) {
          console.log('📁 ICS file URL:', icsFileURL);
        } else {
          console.log('📎 ICS file was included as email attachment only (storage upload failed)');
        }

        // Send email to staff member if requested and assigned
        if (sendToAll && (appointment.staffId || appointment.staffEmail)) {
          try {
            await sendStaffAppointmentNotification(appointment, businessName, timeZone);
            console.log('Staff notification sent successfully');
          } catch (staffEmailError) {
            console.error('Error sending email to staff member:', staffEmailError);
            // Don't fail the whole process if staff email fails
          }
        }

        return;
      } else {
        console.error('❌ Failed to send email via Mailgun:', result.details);
        console.error('❌ Full result object:', result);
        throw new Error(result.details || 'Failed to send email via Mailgun');
      }
    } catch (mailgunError: any) {
      console.error('❌ Error sending email via Mailgun:', mailgunError);
      console.error('❌ Error type:', typeof mailgunError);
      console.error('❌ Error details:', {
        message: mailgunError.message,
        status: mailgunError.status,
        stack: mailgunError.stack
      });

      // Check if this is a sandbox restriction error
      if (mailgunError.status === 403 && mailgunError.message?.includes('sandbox')) {
        console.warn('⚠️  Mailgun sandbox restriction detected!');
        console.warn('📧 For testing purposes, emails can only be sent to authorized recipients.');
        console.warn('🔧 To fix this:');
        console.warn('   1. Add the recipient email to authorized recipients in Mailgun dashboard');
        console.warn('   2. Or upgrade your Mailgun account to remove sandbox restrictions');
        console.warn('   3. Or use a different email service for testing');

        // Log the email details for manual verification
        console.log('📋 Email that would have been sent:');
        console.log(`   To: ${appointment.clientEmail}`);
        console.log(`   Subject: Appointment Confirmed - ${businessName}`);
        console.log(`   ICS File: ${icsFileName}`);

        // Don't throw error, just log the issue
        console.log('✅ Appointment created successfully (email sending skipped due to sandbox restrictions)');
        return;
      }

      // For other errors, fallback to mock email service
      console.log('Falling back to mock email service due to Mailgun error');
      await sendMockAppointmentConfirmationEmail(appointment, businessName);
    }
  } catch (error) {
    console.error('Error sending appointment confirmation email with ICS:', error);
    throw error;
  }
};

/**
 * Legacy appointment confirmation email function (kept for backward compatibility)
 * @param appointment The appointment data
 * @param businessName The business name
 * @param timeZone The business timezone
 * @param sendToAll Whether to send emails to all parties (client, business owner, and staff)
 */
export const sendAppointmentConfirmationEmail = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string,
  sendToAll: boolean = true
): Promise<void> => {
  try {
    // Handle different date formats (Timestamp vs Date)
    const startTime = appointment.startTime instanceof Date
      ? appointment.startTime
      : appointment.startTime.toDate();
    const endTime = appointment.endTime instanceof Date
      ? appointment.endTime
      : appointment.endTime.toDate();

    const formattedStartTime = startTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedEndTime = endTime.toLocaleString('en-US', {
      timeZone,
      hour: 'numeric',
      minute: '2-digit',
    });

    // Create ICS file content for calendar attachment
    const icsStartTime = startTime.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
    const icsEndTime = endTime.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

    const icsContent = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Onpointly//Appointment Calendar//EN',
      'CALSCALE:GREGORIAN',
      'METHOD:REQUEST',
      'BEGIN:VEVENT',
      `SUMMARY:${appointment.serviceName || 'Appointment'} - ${businessName}`,
      `DTSTART:${icsStartTime}`,
      `DTEND:${icsEndTime}`,
      `DESCRIPTION:Your appointment with ${businessName} for ${appointment.serviceName || 'service'}.`,
      `LOCATION:${appointment.location || businessName}`,
      `ORGANIZER;CN=${businessName}:mailto:<EMAIL>`,
      `ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE;CN=${appointment.clientName}:mailto:${appointment.clientEmail}`,
      'STATUS:CONFIRMED',
      `UID:appointment-${appointment.id}@onpointly.com`,
      'SEQUENCE:0',
      'BEGIN:VALARM',
      'TRIGGER:-PT1H',
      'ACTION:DISPLAY',
      'DESCRIPTION:Reminder',
      'END:VALARM',
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\r\n');

    // In development, use the mock email service
    if (isDevelopment) {
      console.log("Development environment - using mock email service");
      await sendMockAppointmentConfirmationEmail(appointment, businessName);
      return;
    }

    // Create the email HTML with the Onpointly footer
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Appointment Confirmation</h1>
        </div>
        <div style="padding: 20px;">
          <p>Hello ${appointment.clientName},</p>
          <p>Your appointment has been confirmed with ${businessName}.</p>
          <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Appointment Details</h3>
            <p><strong>Date:</strong> ${formattedStartTime}</p>
            <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
            <p><strong>Service:</strong> ${appointment.serviceName || appointment.serviceId || 'Not specified'}</p>
            ${appointment.staffName ? `<p><strong>Staff:</strong> ${appointment.staffName}</p>` : ''}
            ${appointment.location ? `<p><strong>Location:</strong> ${appointment.location}</p>` : ''}
          </div>

          <div style="background-color: #e6f7ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin-top: 0;"><strong>📅 Add to Your Calendar</strong></p>
            <p>We've attached a calendar file (.ics) to this email that you can use to add this appointment to your calendar app.</p>
            <p style="margin-bottom: 0;">Simply open the attachment and your calendar app will import the appointment details automatically.</p>
          </div>

          <p>If you need to reschedule or cancel your appointment, please contact us as soon as possible.</p>
          <p>Thank you for choosing ${businessName}!</p>
        </div>

        <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p style="margin-bottom: 5px;">This is an automated message, please do not reply to this email.</p>
          <p style="margin-top: 0;">Powered by <a href="https://onpointly.com" style="color: #4f46e5; text-decoration: none;">Onpointly</a></p>
        </div>
      </div>
    `;

    if (!resend) {
      console.warn('Resend email service is not initialized. Trying alternative methods.');
    } else {
      try {
        // Try sending with Resend first
        const { data, error } = await resend.emails.send({
          from: 'OnPointly <<EMAIL>>',
          to: appointment.clientEmail,
          subject: `Appointment Confirmation - ${businessName}`,
          html: emailHtml,
          attachments: [
            {
              filename: `appointment-${icsStartTime}.ics`,
              content: Buffer.from(icsContent).toString('base64'),
              contentType: 'text/calendar'
            }
          ]
        });

        if (error) {
          console.error('Failed to send email with Resend:', error);
          throw error;
        }

        console.log('Email sent successfully with Resend:', data);
        return;
      } catch (resendError) {
        console.error('Error sending email with Resend:', resendError);
        // Fall through to alternative methods
      }
    }

    // Try using Mailgun as a fallback
    try {
      console.log('Attempting to send email via Mailgun...');

      // Import the mailgun service
      const { sendMailgunEmail } = await import('@/lib/services/mailgunService');

      const result = await sendMailgunEmail(
        appointment.clientEmail,
        `Appointment Confirmation - ${businessName}`,
        emailHtml,
        `${businessName} <<EMAIL>>`,
        [
          {
            filename: `appointment-${icsStartTime}.ics`,
            content: Buffer.from(icsContent).toString('base64'),
            contentType: 'text/calendar'
          }
        ]
      );

      if (result.success) {
        console.log('Email sent successfully via Mailgun:', result.id);
        return;
      } else {
        console.error('Failed to send email via Mailgun:', result.details);
        throw new Error(result.details || 'Failed to send email via Mailgun');
      }
    } catch (mailgunError) {
      console.error('Error sending email via Mailgun:', mailgunError);

      // Try using the API route as a last resort
      try {
        console.log('Attempting to send email via API route...');
        const response = await fetch('/api/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: appointment.clientEmail,
            subject: `Appointment Confirmation - ${businessName}`,
            html: emailHtml,
            preferredProvider: 'mailgun',
            attachments: [
              {
                filename: `appointment-${icsStartTime}.ics`,
                content: Buffer.from(icsContent).toString('base64'),
                contentType: 'text/calendar'
              }
            ]
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `API route responded with status: ${response.status}`);
        }

        console.log('Email sent successfully to client via API route');

        // If requested, send notification to staff member if assigned
        if (sendToAll && appointment.staffId) {
          try {
            // If staff email is not in the appointment, try to get it from the database
            if (!appointment.staffEmail && appointment.staffId) {
              try {
                const { getDoc, doc } = await import('firebase/firestore');
                const { db } = await import('@/lib/firebase');

                const staffRef = doc(db, 'staff', appointment.staffId);
                const staffDoc = await getDoc(staffRef);

                if (staffDoc.exists()) {
                  const staffData = staffDoc.data();
                  appointment.staffEmail = staffData.email;
                  appointment.staffName = staffData.name;
                }
              } catch (staffLookupError) {
                console.error('Error looking up staff email:', staffLookupError);
              }
            }

            if (appointment.staffEmail) {
              await sendStaffAppointmentNotification(appointment, businessName, timeZone);
            } else {
              console.warn(`Staff member ${appointment.staffId} has no email address, skipping notification`);
            }
          } catch (staffEmailError) {
            console.error('Error sending email to staff member:', staffEmailError);
            // Continue with other notifications
          }
        }

        return;
      } catch (apiError) {
        console.error('Failed to send email via API route:', apiError);
        throw apiError;
      }
    }
  } catch (error) {
    console.error('Error sending appointment confirmation email:', error);
    throw error;
  }
};

/**
 * Sends an appointment notification to the assigned staff member
 */
export const sendStaffAppointmentNotification = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string
): Promise<void> => {
  try {
    let staffEmail = appointment.staffEmail;
    let staffName = appointment.staffName;

    // If staff email is not in the appointment, try to get it from the database
    if (!staffEmail && appointment.staffId) {
      try {
        const { getDoc, doc } = await import('firebase/firestore');
        const { db } = await import('@/lib/firebase');

        const staffRef = doc(db, 'staff', appointment.staffId);
        const staffDoc = await getDoc(staffRef);

        if (staffDoc.exists()) {
          const staffData = staffDoc.data();
          staffEmail = staffData.email;
          staffName = staffData.name;
        }
      } catch (staffLookupError) {
        console.error('Error looking up staff email:', staffLookupError);
      }
    }

    if (!staffEmail) {
      console.warn(`Staff member ${appointment.staffId} has no email address, skipping notification`);
      return;
    }

    console.log(`Sending appointment notification to staff member: ${staffEmail}`);

    // Handle different date formats (Timestamp vs Date)
    const startTime = appointment.startTime instanceof Date
      ? appointment.startTime
      : appointment.startTime.toDate();
    const endTime = appointment.endTime instanceof Date
      ? appointment.endTime
      : appointment.endTime.toDate();

    const formattedStartTime = startTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedEndTime = endTime.toLocaleString('en-US', {
      timeZone,
      hour: 'numeric',
      minute: '2-digit',
    });

    // Create ICS file content for calendar attachment
    const icsStartTime = startTime.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
    const icsEndTime = endTime.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

    const icsContent = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Onpointly//Appointment Calendar//EN',
      'CALSCALE:GREGORIAN',
      'METHOD:REQUEST',
      'BEGIN:VEVENT',
      `SUMMARY:${appointment.serviceName || 'Appointment'} with ${appointment.clientName}`,
      `DTSTART:${icsStartTime}`,
      `DTEND:${icsEndTime}`,
      `DESCRIPTION:Appointment with ${appointment.clientName} (${appointment.clientEmail}) for ${appointment.serviceName || 'service'}.\nPhone: ${appointment.clientPhone || 'Not provided'}\nNotes: ${appointment.notes || 'None'}`,
      `LOCATION:${appointment.location || businessName}`,
      `ORGANIZER;CN=${businessName}:mailto:<EMAIL>`,
      `ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE;CN=${staffName || 'Staff'}:mailto:${staffEmail}`,
      `ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=ACCEPTED;RSVP=FALSE;CN=${appointment.clientName}:mailto:${appointment.clientEmail}`,
      'STATUS:CONFIRMED',
      `UID:appointment-staff-${appointment.id}@onpointly.com`,
      'SEQUENCE:0',
      'BEGIN:VALARM',
      'TRIGGER:-PT1H',
      'ACTION:DISPLAY',
      'DESCRIPTION:Reminder',
      'END:VALARM',
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\r\n');

    // Create the email HTML
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">New Appointment Assigned</h1>
        </div>
        <div style="padding: 20px;">
          <p>Hello ${staffName || 'Team Member'},</p>
          <p>You have been assigned a new appointment at ${businessName}.</p>

          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4f46e5;">
            <h3 style="margin-top: 0; color: #4f46e5;">Appointment Details</h3>
            <p><strong>📅 Date & Time:</strong> ${formattedStartTime}</p>
            <p><strong>⏰ Duration:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
            <p><strong>🛍️ Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
            <p><strong>👤 Client:</strong> ${appointment.clientName}</p>
            <p><strong>📧 Client Email:</strong> ${appointment.clientEmail}</p>
            ${appointment.clientPhone ? `<p><strong>📞 Client Phone:</strong> ${appointment.clientPhone}</p>` : ''}
            ${appointment.location ? `<p><strong>📍 Location:</strong> ${appointment.location}</p>` : ''}
            ${appointment.notes ? `<p><strong>📝 Notes:</strong> ${appointment.notes}</p>` : ''}
          </div>

          <div style="background-color: #ecfdf5; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981;">
            <p style="margin-top: 0;"><strong>📅 Add to Your Calendar</strong></p>
            <p>We've attached a calendar file (.ics) to this email. Simply open the attachment and your calendar app will import the appointment automatically.</p>
            <p style="margin-bottom: 0;">Compatible with Google Calendar, Outlook, Apple Calendar, and most other calendar applications.</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/staff/schedule" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 0 10px; display: inline-block;">View Schedule</a>
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/staff/dashboard" style="background-color: #6b7280; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; margin: 0 10px; display: inline-block;">Staff Dashboard</a>
          </div>

          <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
            <p style="margin-top: 0;"><strong>⏰ Reminder</strong></p>
            <p style="margin-bottom: 0;">Please prepare for this appointment and arrive 5-10 minutes early. Contact the client if you need to make any changes.</p>
          </div>

          <p>If you have any questions about this appointment, please contact your administrator.</p>
          <p>Thank you,<br>${businessName} Team</p>
        </div>

        <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p style="margin-bottom: 5px;">This is an automated message from your Onpointly scheduling system.</p>
          <p style="margin-top: 0;">${EMAIL_FOOTER_TEXT}</p>
        </div>
      </div>
    `;

    // Try to send the email using Mailgun first
    try {
      // Import the mailgun service
      const { sendMailgunEmail } = await import('@/lib/services/mailgunService');

      const result = await sendMailgunEmail(
        staffEmail,
        `New Appointment - ${appointment.clientName}`,
        emailHtml,
        `${businessName} <<EMAIL>>`,
        [
          {
            filename: `appointment-${icsStartTime}.ics`,
            content: Buffer.from(icsContent).toString('base64'),
            contentType: 'text/calendar'
          }
        ]
      );

      if (result.success) {
        console.log('Staff notification sent successfully via Mailgun:', result.id);
        return;
      } else {
        console.error('Failed to send staff notification via Mailgun:', result.details);
        throw new Error(result.details || 'Failed to send email via Mailgun');
      }
    } catch (mailgunError) {
      console.error('Error sending staff notification via Mailgun:', mailgunError);

      // Try using the API route as a fallback
      try {
        console.log('Attempting to send staff notification via API route...');
        const response = await fetch('/api/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: staffEmail,
            subject: `New Appointment - ${appointment.clientName}`,
            html: emailHtml,
            preferredProvider: 'mailgun',
            attachments: [
              {
                filename: `appointment-${icsStartTime}.ics`,
                content: Buffer.from(icsContent).toString('base64'),
                contentType: 'text/calendar'
              }
            ]
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `API route responded with status: ${response.status}`);
        }

        console.log('Staff notification sent successfully via API route');
        return;
      } catch (apiError) {
        console.error('Failed to send staff notification via API route:', apiError);
        // Don't throw here, as we don't want to prevent the client from receiving their confirmation
        // if the staff notification fails
      }
    }
  } catch (error) {
    console.error('Error sending staff appointment notification:', error);
    // Don't throw the error to prevent breaking the appointment creation process
  }
};

/**
 * Sends an appointment notification to the business owner
 */
export const sendBusinessOwnerAppointmentNotification = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string
): Promise<void> => {
  try {
    // Get the business owner's email from Firestore
    const { getDoc, doc } = await import('firebase/firestore');
    const { db } = await import('@/lib/firebase');

    const businessRef = doc(db, 'businesses', appointment.businessId);
    const businessDoc = await getDoc(businessRef);

    if (!businessDoc.exists()) {
      console.error(`Business with ID ${appointment.businessId} not found`);
      return;
    }

    const businessData = businessDoc.data();
    const ownerEmail = businessData.email || businessData.ownerEmail;

    if (!ownerEmail) {
      console.error('Business owner email not found in business data:', {
        hasEmail: !!businessData.email,
        hasOwnerEmail: !!businessData.ownerEmail,
        businessId: appointment.businessId
      });
      return;
    }

    console.log(`Sending appointment notification to business owner: ${ownerEmail}`);

    // Handle different date formats (Timestamp vs Date)
    const startTime = appointment.startTime instanceof Date
      ? appointment.startTime
      : appointment.startTime.toDate();
    const endTime = appointment.endTime instanceof Date
      ? appointment.endTime
      : appointment.endTime.toDate();

    const formattedStartTime = startTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedEndTime = endTime.toLocaleString('en-US', {
      timeZone,
      hour: 'numeric',
      minute: '2-digit',
    });

    // Create ICS file content for calendar attachment
    const icsStartTime = startTime.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');
    const icsEndTime = endTime.toISOString().replace(/[-:]/g, '').replace(/\.\d+/, '');

    const icsContent = [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Onpointly//Appointment Calendar//EN',
      'CALSCALE:GREGORIAN',
      'METHOD:REQUEST',
      'BEGIN:VEVENT',
      `SUMMARY:${appointment.serviceName || 'Appointment'} with ${appointment.clientName}`,
      `DTSTART:${icsStartTime}`,
      `DTEND:${icsEndTime}`,
      `DESCRIPTION:Appointment with ${appointment.clientName} (${appointment.clientEmail}) for ${appointment.serviceName || 'service'}.\nPhone: ${appointment.clientPhone || 'Not provided'}\nNotes: ${appointment.notes || 'None'}`,
      `LOCATION:${appointment.location || businessName}`,
      `ORGANIZER;CN=${businessName}:mailto:<EMAIL>`,
      `ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE;CN=${businessName}:mailto:${ownerEmail}`,
      `ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=ACCEPTED;RSVP=FALSE;CN=${appointment.clientName}:mailto:${appointment.clientEmail}`,
      'STATUS:CONFIRMED',
      `UID:appointment-owner-${appointment.id}@onpointly.com`,
      'SEQUENCE:0',
      'BEGIN:VALARM',
      'TRIGGER:-PT1H',
      'ACTION:DISPLAY',
      'DESCRIPTION:Reminder',
      'END:VALARM',
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\r\n');

    // Create the email HTML
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">New Appointment</h1>
        </div>
        <div style="padding: 20px;">
          <p>Hello,</p>
          <p>A new appointment has been booked for your business.</p>
          <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Appointment Details</h3>
            <p><strong>Client:</strong> ${appointment.clientName}</p>
            <p><strong>Email:</strong> ${appointment.clientEmail}</p>
            <p><strong>Phone:</strong> ${appointment.clientPhone || 'Not provided'}</p>
            <p><strong>Date:</strong> ${formattedStartTime}</p>
            <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
            <p><strong>Service:</strong> ${appointment.serviceName || appointment.serviceId || 'Not specified'}</p>
            ${appointment.staffName ? `<p><strong>Staff:</strong> ${appointment.staffName}</p>` : ''}
            ${appointment.location ? `<p><strong>Location:</strong> ${appointment.location}</p>` : ''}
            ${appointment.notes ? `<p><strong>Notes:</strong> ${appointment.notes}</p>` : ''}
          </div>

          <div style="background-color: #e6f7ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p style="margin-top: 0;"><strong>📅 Add to Your Calendar</strong></p>
            <p>We've attached a calendar file (.ics) to this email that you can use to add this appointment to your calendar app.</p>
            <p style="margin-bottom: 0;">Simply open the attachment and your calendar app will import the appointment details automatically.</p>
          </div>
        </div>

        <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p style="margin-bottom: 5px;">This is an automated message from your Onpointly booking system.</p>
          <p style="margin-top: 0;">Powered by <a href="https://onpointly.com" style="color: #4f46e5; text-decoration: none;">Onpointly</a></p>
        </div>
      </div>
    `;

    // Try to send the email using Mailgun first
    try {
      // Import the mailgun service
      const { sendMailgunEmail } = await import('@/lib/services/mailgunService');

      const result = await sendMailgunEmail(
        ownerEmail,
        `New Appointment - ${appointment.clientName}`,
        emailHtml,
        `Onpointly <<EMAIL>>`,
        [
          {
            filename: `appointment-${icsStartTime}.ics`,
            content: Buffer.from(icsContent).toString('base64'),
            contentType: 'text/calendar'
          }
        ]
      );

      if (result.success) {
        console.log('Business owner notification sent successfully via Mailgun:', result.id);
        return;
      } else {
        console.error('Failed to send business owner notification via Mailgun:', result.details);
        throw new Error(result.details || 'Failed to send email via Mailgun');
      }
    } catch (mailgunError) {
      console.error('Error sending business owner notification via Mailgun:', mailgunError);

      // Try using the API route as a fallback
      try {
        console.log('Attempting to send business owner notification via API route...');
        const response = await fetch('/api/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: ownerEmail,
            subject: `New Appointment - ${appointment.clientName}`,
            html: emailHtml,
            preferredProvider: 'mailgun',
            attachments: [
              {
                filename: `appointment-${icsStartTime}.ics`,
                content: Buffer.from(icsContent).toString('base64'),
                contentType: 'text/calendar'
              }
            ]
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `API route responded with status: ${response.status}`);
        }

        console.log('Business owner notification sent successfully via API route');
        return;
      } catch (apiError) {
        console.error('Failed to send business owner notification via API route:', apiError);
        // Don't throw here, as we don't want to prevent the client from receiving their confirmation
        // if the business owner notification fails
      }
    }
  } catch (error) {
    console.error('Error sending business owner notification:', error);
    // Don't throw here, as we don't want to prevent the client from receiving their confirmation
    // if the business owner notification fails
  }
};



export const sendAppointmentReminderEmail = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string,
  hoursBeforeAppointment: number = 24
): Promise<void> => {
  try {
    const startTime = appointment.startTime.toDate();
    const endTime = appointment.endTime.toDate();

    const formattedStartTime = startTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedEndTime = endTime.toLocaleString('en-US', {
      timeZone,
      hour: 'numeric',
      minute: '2-digit',
    });

    // Determine the reminder message based on hours before appointment
    const reminderTitle = hoursBeforeAppointment === 24
      ? "Reminder: Your Appointment Tomorrow"
      : "Reminder: Your Appointment Today";

    const reminderIntro = hoursBeforeAppointment === 24
      ? `This is a friendly reminder that your appointment with ${businessName} is scheduled for tomorrow.`
      : `This is a friendly reminder that your appointment with ${businessName} is scheduled in ${hoursBeforeAppointment} hour${hoursBeforeAppointment === 1 ? '' : 's'}.`;

    // Personalized preparation instructions based on service type
    let preparationInstructions = '';
    if (appointment.serviceName) {
      const serviceName = appointment.serviceName.toLowerCase();
      if (serviceName.includes('haircut') || serviceName.includes('hair')) {
        preparationInstructions = `<p>For the best experience, please arrive with clean, dry hair if possible.</p>`;
      } else if (serviceName.includes('massage')) {
        preparationInstructions = `<p>For your comfort, we recommend arriving 10 minutes early to relax before your massage.</p>`;
      } else if (serviceName.includes('facial') || serviceName.includes('skin')) {
        preparationInstructions = `<p>For the best results, please arrive with clean skin, free of makeup if possible.</p>`;
      } else if (serviceName.includes('nail') || serviceName.includes('manicure') || serviceName.includes('pedicure')) {
        preparationInstructions = `<p>Please remove any old nail polish before your appointment for the best results.</p>`;
      } else if (serviceName.includes('consultation') || serviceName.includes('meeting')) {
        preparationInstructions = `<p>Please bring any relevant documents or information to make the most of your consultation.</p>`;
      }
    }

    // In development, use the mock email service
    if (isDevelopment) {
      console.log("Development environment - using mock email service for reminder");
      await sendMockEmail(
        appointment.clientEmail,
        `${reminderTitle} - ${businessName}`,
        `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">Appointment Reminder</h1>
          </div>

          <div style="padding: 20px; border: 1px solid #e5e7eb; border-top: none;">
            <p>Dear ${appointment.clientName},</p>

            <p>${reminderIntro}</p>

            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h2 style="margin-top: 0; color: #4f46e5;">Appointment Details</h2>
              <p><strong>Date:</strong> ${formattedStartTime}</p>
              <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
              <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
              <p><strong>Staff:</strong> ${appointment.staffName || (appointment.useRoundRobin ? 'Auto-assigned (Round Robin)' : 'Not specified')}</p>
              ${appointment.notes ? `<p><strong>Notes:</strong> ${appointment.notes}</p>` : ''}
            </div>

            ${preparationInstructions}

            <p>If you need to reschedule or cancel your appointment, please contact us as soon as possible.</p>

            <p>Thank you for choosing ${businessName}!</p>

            <hr style="margin: 20px 0;">
            <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      `
      );
      return;
    }

    const { data, error } = await resend!.emails.send({
      from: 'OnPointly <<EMAIL>>',
      to: appointment.clientEmail,
      subject: `${reminderTitle} - ${businessName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0;">Appointment Reminder</h1>
          </div>

          <div style="padding: 20px; border: 1px solid #e5e7eb; border-top: none;">
            <p>Dear ${appointment.clientName},</p>

            <p>${reminderIntro}</p>

            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h2 style="margin-top: 0; color: #4f46e5;">Appointment Details</h2>
              <p><strong>Date:</strong> ${formattedStartTime}</p>
              <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
              <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
              <p><strong>Staff:</strong> ${appointment.staffName || (appointment.useRoundRobin ? 'Auto-assigned (Round Robin)' : 'Not specified')}</p>
              ${appointment.notes ? `<p><strong>Notes:</strong> ${appointment.notes}</p>` : ''}
            </div>

            ${preparationInstructions}

            <p>If you need to reschedule or cancel your appointment, please contact us as soon as possible.</p>

            <p>Thank you for choosing ${businessName}!</p>

            <hr style="margin: 20px 0;">
            <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      `,
    });

    if (error) {
      console.error('Failed to send reminder email:', error);
      throw error;
    }

    console.log('Reminder email sent successfully:', data);
  } catch (error) {
    console.error('Error sending appointment reminder email:', error);
    throw error;
  }
};

/**
 * Sends an email notification when an appointment is rescheduled
 * @param appointment The updated appointment
 * @param businessName The name of the business
 * @param timeZone The timezone for formatting dates
 * @param previousStartTime The previous start time (before rescheduling)
 * @returns A promise that resolves when the email is sent
 */
export const sendAppointmentRescheduleEmail = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string,
  previousStartTime: Date
): Promise<void> => {
  try {
    const startTime = appointment.startTime.toDate();
    const endTime = appointment.endTime.toDate();

    const formattedStartTime = startTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedEndTime = endTime.toLocaleString('en-US', {
      timeZone,
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedPreviousTime = previousStartTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    // In development, just log the email details
    if (isDevelopment) {
      console.log("Development environment - would send reschedule email to:", appointment.clientEmail);
      console.log("Subject: Appointment Rescheduled -", businessName);
      console.log("Content:", `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Appointment Rescheduled</h2>
          <p>Hello ${appointment.clientName},</p>
          <p>Your appointment with ${businessName} has been rescheduled.</p>
          <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Previous Appointment</h3>
            <p><strong>Date and Time:</strong> ${formattedPreviousTime}</p>
          </div>
          <div style="background-color: #e6f7ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">New Appointment Details</h3>
            <p><strong>Date:</strong> ${formattedStartTime}</p>
            <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
            <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
          </div>
          <p>If you need to make any changes, please contact us as soon as possible.</p>
          <p>Thank you for choosing ${businessName}!</p>
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
        </div>
      `);
      return;
    }

    if (!resend) {
      console.warn('Resend email service is not initialized. Email will not be sent.');
      return;
    }

    try {
      const { data, error } = await resend.emails.send({
        from: 'OnPointly <<EMAIL>>',
        to: appointment.clientEmail,
        subject: `Appointment Rescheduled - ${businessName}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Appointment Rescheduled</h2>
            <p>Hello ${appointment.clientName},</p>
            <p>Your appointment with ${businessName} has been rescheduled.</p>
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0;">Previous Appointment</h3>
              <p><strong>Date and Time:</strong> ${formattedPreviousTime}</p>
            </div>
            <div style="background-color: #e6f7ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0;">New Appointment Details</h3>
              <p><strong>Date:</strong> ${formattedStartTime}</p>
              <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
              <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
            </div>
            <p>If you need to make any changes, please contact us as soon as possible.</p>
            <p>Thank you for choosing ${businessName}!</p>
            <hr style="margin: 20px 0;">
            <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
          </div>
        `,
      });

      if (error) {
        console.error('Failed to send reschedule email:', error);
        throw error;
      }

      console.log('Reschedule email sent successfully:', data);
    } catch (emailError) {
      console.error('Error sending email with Resend:', emailError);
      // Try using Firebase Functions as a fallback
      try {
        console.log('Attempting to send email via API route...');
        const response = await fetch('/api/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: appointment.clientEmail,
            subject: `Appointment Rescheduled - ${businessName}`,
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Appointment Rescheduled</h2>
                <p>Hello ${appointment.clientName},</p>
                <p>Your appointment with ${businessName} has been rescheduled.</p>
                <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="margin-top: 0;">Previous Appointment</h3>
                  <p><strong>Date and Time:</strong> ${formattedPreviousTime}</p>
                </div>
                <div style="background-color: #e6f7ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="margin-top: 0;">New Appointment Details</h3>
                  <p><strong>Date:</strong> ${formattedStartTime}</p>
                  <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
                  <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
                </div>
                <p>If you need to make any changes, please contact us as soon as possible.</p>
                <p>Thank you for choosing ${businessName}!</p>
                <hr style="margin: 20px 0;">
                <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
              </div>
            `,
          }),
        });

        if (!response.ok) {
          throw new Error(`API route responded with status: ${response.status}`);
        }

        console.log('Reschedule email sent successfully via API route');
      } catch (apiError) {
        console.error('Failed to send email via API route:', apiError);
        throw apiError;
      }
    }
  } catch (error) {
    console.error('Error sending appointment reschedule email:', error);
    throw error;
  }
};

/**
 * Sends an email notification when an appointment is updated with new details
 * @param appointment The updated appointment
 * @param businessName The name of the business
 * @param timeZone The timezone for formatting dates
 * @param changes Object describing what changed (e.g., {notes: true, staffId: true})
 * @returns A promise that resolves when the email is sent
 */
export const sendAppointmentUpdateEmail = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string,
  changes: Record<string, boolean>
): Promise<void> => {
  try {
    const startTime = appointment.startTime.toDate();
    const endTime = appointment.endTime.toDate();

    const formattedStartTime = startTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedEndTime = endTime.toLocaleString('en-US', {
      timeZone,
      hour: 'numeric',
      minute: '2-digit',
    });

    // Create a list of what changed
    const changesList = Object.entries(changes)
      .filter(([_, changed]) => changed)
      .map(([field]) => {
        switch (field) {
          case 'staffId':
            return 'Staff member';
          case 'notes':
            return 'Appointment notes';
          case 'serviceId':
            return 'Service';
          case 'status':
            return 'Appointment status';
          default:
            return field.charAt(0).toUpperCase() + field.slice(1);
        }
      });

    // In development, just log the email details
    if (isDevelopment) {
      console.log("Development environment - would send update email to:", appointment.clientEmail);
      console.log("Subject: Appointment Updated -", businessName);
      console.log("Content:", `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Appointment Updated</h2>
          <p>Hello ${appointment.clientName},</p>
          <p>Your appointment with ${businessName} has been updated.</p>
          <div style="background-color: #e6f7ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Updated Appointment Details</h3>
            <p><strong>Date:</strong> ${formattedStartTime}</p>
            <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
            <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
            ${appointment.staffName ? `<p><strong>Staff:</strong> ${appointment.staffName}</p>` : ''}
            ${appointment.notes ? `<p><strong>Notes:</strong> ${appointment.notes}</p>` : ''}
            <p><strong>Changes made:</strong> ${changesList.join(', ')}</p>
          </div>
          <p>If you have any questions about these changes, please contact us.</p>
          <p>Thank you for choosing ${businessName}!</p>
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
        </div>
      `);
      return;
    }

    if (!resend) {
      console.warn('Resend email service is not initialized. Email will not be sent.');
      return;
    }

    try {
      const { data, error } = await resend.emails.send({
        from: 'OnPointly <<EMAIL>>',
        to: appointment.clientEmail,
        subject: `Appointment Updated - ${businessName}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2>Appointment Updated</h2>
            <p>Hello ${appointment.clientName},</p>
            <p>Your appointment with ${businessName} has been updated.</p>
            <div style="background-color: #e6f7ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0;">Updated Appointment Details</h3>
              <p><strong>Date:</strong> ${formattedStartTime}</p>
              <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
              <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
              ${appointment.staffName ? `<p><strong>Staff:</strong> ${appointment.staffName}</p>` : ''}
              ${appointment.notes ? `<p><strong>Notes:</strong> ${appointment.notes}</p>` : ''}
              <p><strong>Changes made:</strong> ${changesList.join(', ')}</p>
            </div>
            <p>If you have any questions about these changes, please contact us.</p>
            <p>Thank you for choosing ${businessName}!</p>
            <hr style="margin: 20px 0;">
            <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
          </div>
        `,
      });

      if (error) {
        console.error('Failed to send update email:', error);
        throw error;
      }

      console.log('Update email sent successfully:', data);
    } catch (emailError) {
      console.error('Error sending email with Resend:', emailError);
      // Try using Firebase Functions as a fallback
      try {
        console.log('Attempting to send email via API route...');
        const response = await fetch('/api/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            to: appointment.clientEmail,
            subject: `Appointment Updated - ${businessName}`,
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2>Appointment Updated</h2>
                <p>Hello ${appointment.clientName},</p>
                <p>Your appointment with ${businessName} has been updated.</p>
                <div style="background-color: #e6f7ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="margin-top: 0;">Updated Appointment Details</h3>
                  <p><strong>Date:</strong> ${formattedStartTime}</p>
                  <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
                  <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
                  ${appointment.staffName ? `<p><strong>Staff:</strong> ${appointment.staffName}</p>` : ''}
                  ${appointment.notes ? `<p><strong>Notes:</strong> ${appointment.notes}</p>` : ''}
                  <p><strong>Changes made:</strong> ${changesList.join(', ')}</p>
                </div>
                <p>If you have any questions about these changes, please contact us.</p>
                <p>Thank you for choosing ${businessName}!</p>
                <hr style="margin: 20px 0;">
                <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
              </div>
            `,
          }),
        });

        if (!response.ok) {
          throw new Error(`API route responded with status: ${response.status}`);
        }

        console.log('Update email sent successfully via API route');
      } catch (apiError) {
        console.error('Failed to send email via API route:', apiError);
        throw apiError;
      }
    }
  } catch (error) {
    console.error('Error sending appointment update email:', error);
    throw error;
  }
};

/**
 * Sends an email notification to staff when an appointment is rescheduled
 * @param appointment The updated appointment
 * @param businessName The name of the business
 * @param timeZone The timezone for formatting dates
 * @param previousStartTime The previous start time (before rescheduling)
 * @returns A promise that resolves when the email is sent
 */
export const sendStaffRescheduleEmail = async (
  appointment: Appointment,
  businessName: string,
  timeZone: string,
  previousStartTime: Date
): Promise<void> => {
  try {
    // Check if staff email exists
    if (!appointment.staffEmail) {
      console.log('No staff email found for appointment, skipping staff notification');
      return;
    }

    const startTime = appointment.startTime.toDate();
    const endTime = appointment.endTime.toDate();

    const formattedStartTime = startTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedEndTime = endTime.toLocaleString('en-US', {
      timeZone,
      hour: 'numeric',
      minute: '2-digit',
    });

    const formattedPreviousTime = previousStartTime.toLocaleString('en-US', {
      timeZone,
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });

    // In development, use the mock email service
    if (isDevelopment) {
      console.log("Development environment - using mock email service for staff reschedule notification");
      console.log(`[MOCK] Staff reschedule notification for ${appointment.staffName} at ${businessName}`);
      console.log(`[MOCK] Appointment with ${appointment.clientName} rescheduled from ${formattedPreviousTime} to ${formattedStartTime}`);
      return;
    }

    // Try to send with Resend first
    if (resend) {
      try {
        const { data, error } = await resend.emails.send({
          from: 'OnPointly <<EMAIL>>',
          to: appointment.staffEmail,
          subject: `Appointment Rescheduled - ${businessName}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2>Appointment Rescheduled</h2>
              <p>Hello ${appointment.staffName},</p>
              <p>An appointment with ${appointment.clientName} has been rescheduled.</p>
              <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0;">Previous Appointment</h3>
                <p><strong>Date and Time:</strong> ${formattedPreviousTime}</p>
              </div>
              <div style="background-color: #e6f7ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0;">New Appointment Details</h3>
                <p><strong>Client:</strong> ${appointment.clientName}</p>
                <p><strong>Date:</strong> ${formattedStartTime}</p>
                <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
                <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
                ${appointment.notes ? `<p><strong>Notes:</strong> ${appointment.notes}</p>` : ''}
              </div>
              <p>Please update your schedule accordingly.</p>
              <p>Thank you,<br>${businessName}</p>
              <hr style="margin: 20px 0;">
              <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
            </div>
          `,
        });

        if (error) {
          console.error('Failed to send staff reschedule email:', error);
          throw error;
        }

        console.log('Staff reschedule email sent successfully:', data);
        return;
      } catch (resendError) {
        console.error('Error sending staff reschedule email with Resend:', resendError);
        // Fall through to alternative methods
      }
    }

    // Try using the API route directly
    try {
      console.log('Attempting to send staff reschedule email via API route...');
      const response = await fetch('/api/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: appointment.staffEmail,
          subject: `Appointment Rescheduled - ${businessName}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2>Appointment Rescheduled</h2>
              <p>Hello ${appointment.staffName},</p>
              <p>An appointment with ${appointment.clientName} has been rescheduled.</p>
              <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0;">Previous Appointment</h3>
                <p><strong>Date and Time:</strong> ${formattedPreviousTime}</p>
              </div>
              <div style="background-color: #e6f7ff; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0;">New Appointment Details</h3>
                <p><strong>Client:</strong> ${appointment.clientName}</p>
                <p><strong>Date:</strong> ${formattedStartTime}</p>
                <p><strong>Time:</strong> ${formattedStartTime} - ${formattedEndTime}</p>
                <p><strong>Service:</strong> ${appointment.serviceName || 'Not specified'}</p>
                ${appointment.notes ? `<p><strong>Notes:</strong> ${appointment.notes}</p>` : ''}
              </div>
              <p>Please update your schedule accordingly.</p>
              <p>Thank you,<br>${businessName}</p>
              <hr style="margin: 20px 0;">
              <p style="color: #666; font-size: 12px;">This is an automated message, please do not reply to this email.</p>
            </div>
          `,
        }),
      });

      if (!response.ok) {
        throw new Error(`API route responded with status: ${response.status}`);
      }

      console.log('Staff reschedule email sent successfully via API route');
      return;
    } catch (apiError) {
      console.error('Failed to send staff email via API route:', apiError);
      // Continue without throwing - we don't want to block the process if staff notification fails
      console.log('Unable to send staff notification, but continuing with the process');
    }
  } catch (error) {
    console.error('Error sending staff reschedule email:', error);
    // Don't throw the error - we don't want to block the process if staff notification fails
    console.log('Error in staff notification, but continuing with the process');
  }
};

/**
 * Sends a waitlist notification email to a client when a spot becomes available
 * @param waitlistEntry The waitlist entry
 * @param appointment The appointment
 * @param businessName The name of the business
 * @param responseUrl The URL for the client to respond to the notification
 * @returns A promise that resolves when the email is sent
 */
export const sendWaitlistNotificationEmail = async (
  waitlistEntry: WaitlistEntry,
  appointment: Appointment,
  businessName: string,
  responseUrl: string
): Promise<void> => {
  try {
    const subject = `Appointment Available at ${businessName}`;

    // Format the appointment date and time
    const startTime = appointment.startTime.toDate();
    const endTime = appointment.endTime.toDate();

    const formattedStartTime = startTime.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    const formattedEndTime = endTime.toLocaleString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    // Calculate the response deadline
    const responseDeadline = waitlistEntry.responseDeadline?.toDate().toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    // Create the email content
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Appointment Available</h1>
        </div>

        <div style="padding: 20px; border: 1px solid #e5e7eb; border-top: none;">
          <p>Hello ${waitlistEntry.clientName},</p>

          <p>Good news! An appointment slot has become available at ${businessName}.</p>

          <div style="background-color: #f9fafb; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #4f46e5;">Appointment Details</h3>
            <p><strong>Date:</strong> ${formattedStartTime.split(',')[0]}, ${formattedStartTime.split(',')[1]}</p>
            <p><strong>Time:</strong> ${formattedStartTime.split(',')[2]} - ${formattedEndTime}</p>
            <p><strong>Service:</strong> ${appointment.serviceName || 'Scheduled Service'}</p>
            ${appointment.staffName ? `<p><strong>Staff:</strong> ${appointment.staffName}</p>` : ''}
          </div>

          <p>Please respond by <strong>${responseDeadline}</strong> to secure this appointment slot.</p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${responseUrl}&action=accept" style="background-color: #4f46e5; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Accept Appointment</a>
            <a href="${responseUrl}&action=decline" style="background-color: #e5e7eb; color: #374151; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Decline</a>
          </div>

          <p>If you don't respond by the deadline, this slot may be offered to another client on the waitlist.</p>

          <p>Thank you for choosing ${businessName}!</p>
        </div>

        <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p>This is an automated message from Onpointly. Please do not reply to this email.</p>
        </div>
      </div>
    `;

    // In development, use the mock email service
    if (isDevelopment) {
      console.log("Development environment - using mock email service");
      await sendMockWaitlistNotificationEmail(waitlistEntry, businessName);
      return;
    }

    // Try to send with Resend first
    if (resend) {
      try {
        const { data, error } = await resend.emails.send({
          from: 'OnPointly <<EMAIL>>',
          to: waitlistEntry.clientEmail,
          subject,
          html,
        });

        if (error) {
          console.error('Failed to send waitlist notification email with Resend:', error);
          throw error;
        }

        console.log('Waitlist notification email sent successfully with Resend:', data);
        return;
      } catch (resendError) {
        console.error('Error sending email with Resend:', resendError);
        // Fall through to alternative methods
      }
    }

    // Try using the API route directly
    try {
      console.log('Attempting to send email via API route...');
      const response = await fetch('/api/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: waitlistEntry.clientEmail,
          subject,
          html,
        }),
      });

      if (!response.ok) {
        throw new Error(`API route responded with status: ${response.status}`);
      }

      console.log('Waitlist notification email sent successfully via API route');
      return;
    } catch (apiError) {
      console.error('Failed to send email via API route:', apiError);
      throw apiError;
    }


  } catch (error) {
    console.error('Error sending waitlist notification email:', error);
    throw error;
  }
};

/**
 * Sends a waitlist confirmation email to a client when they join a waitlist
 * @param waitlistEntry The waitlist entry
 * @param businessName The name of the business
 * @returns A promise that resolves when the email is sent
 */
/**
 * Send a review request email to a client after their appointment
 * @param reviewRequest The review request
 * @param appointment The appointment
 * @param businessName The name of the business
 * @returns Promise that resolves when the email is sent
 */
export const sendReviewRequestEmail = async (
  reviewRequest: ReviewRequest,
  appointment: Appointment,
  businessName: string
): Promise<void> => {
  try {
    // IMPORTANT DEBUG LOG - Check if this appears in the console
    console.log('🔴 SENDING REVIEW REQUEST EMAIL:', {
      reviewRequestId: reviewRequest.id,
      appointmentId: appointment.id,
      clientEmail: appointment.clientEmail,
      businessName,
      reviewToken: reviewRequest.token,
      reviewUrl: `${process.env.NEXT_PUBLIC_APP_URL}/review/${reviewRequest.token}`
    });

    // Get additional business details
    let businessDetails = null;
    let businessAppearance = null;
    try {
      // Import the business service dynamically to avoid circular dependencies
      const { getBusiness } = await import('./businessService');
      const business = await getBusiness(appointment.businessId);
      if (business) {
        businessDetails = business;
        businessAppearance = business.appearance || {};
        console.log('Retrieved business details for review email:', {
          businessId: business.id,
          businessName: business.name,
          hasAppearance: !!business.appearance
        });
      }
    } catch (businessError) {
      console.error('Error getting business details for review email:', businessError);
      // Continue without business details
    }

    // Get service details
    let serviceDetails = null;
    try {
      if (appointment.serviceId) {
        // Import the service service dynamically to avoid circular dependencies
        const { getServiceById } = await import('./serviceService');
        serviceDetails = await getServiceById(appointment.serviceId);
        console.log('Retrieved service details for review email:', {
          serviceId: appointment.serviceId,
          serviceName: serviceDetails?.name
        });
      }
    } catch (serviceError) {
      console.error('Error getting service details for review email:', serviceError);
      // Continue without service details
    }

    // Get staff details
    let staffDetails = null;
    try {
      if (appointment.staffId) {
        // Import the staff service dynamically to avoid circular dependencies
        const { getStaffById } = await import('./staffService');
        staffDetails = await getStaffById(appointment.staffId);
        console.log('Retrieved staff details for review email:', {
          staffId: appointment.staffId,
          staffName: staffDetails?.name
        });
      }
    } catch (staffError) {
      console.error('Error getting staff details for review email:', staffError);
      // Continue without staff details
    }

    const subject = `How was your appointment with ${businessName}?`;

    // Format the appointment date and time
    const startTime = appointment.startTime.toDate();

    const formattedDate = startTime.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    const formattedTime = startTime.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });

    // Generate review URL
    const reviewUrl = `${process.env.NEXT_PUBLIC_APP_URL}/review/${reviewRequest.token}`;

    // Get business theme colors
    const primaryColor = businessAppearance?.primaryColor || '#4f46e5';
    const secondaryColor = (businessAppearance && 'secondaryColor' in businessAppearance && (businessAppearance as any).secondaryColor)
      ? (businessAppearance as any).secondaryColor
      : '#f3f4f6';
    const textColor = (businessAppearance && 'textColor' in businessAppearance)
      ? (businessAppearance as any).textColor
      : '#000000';

    // Determine contrasting text color for primary color background
    const getContrastColor = (hexColor: string) => {
      // Convert hex to RGB
      const r = parseInt(hexColor.slice(1, 3), 16);
      const g = parseInt(hexColor.slice(3, 5), 16);
      const b = parseInt(hexColor.slice(5, 7), 16);

      // Calculate luminance (perceived brightness)
      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

      // Return white for dark colors, black for light colors
      return luminance > 0.5 ? '#000000' : '#ffffff';
    };

    const headerTextColor = getContrastColor(primaryColor);

    // Create the email content with business theme
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: ${primaryColor}; color: ${headerTextColor}; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">How was your appointment?</h1>
        </div>

        <div style="padding: 20px; border: 1px solid #e5e7eb; border-top: none;">
          <p>Hello ${appointment.clientName},</p>

          <p>Thank you for choosing ${businessName}. We hope you enjoyed your recent appointment and would love to hear about your experience.</p>

          <div style="background-color: ${secondaryColor}; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h2 style="margin-top: 0; color: ${primaryColor};">Appointment Details</h2>
            <p><strong>Service:</strong> ${serviceDetails?.name || appointment.serviceName || 'Scheduled Service'}</p>
            <p><strong>Date:</strong> ${formattedDate}</p>
            <p><strong>Time:</strong> ${formattedTime}</p>
            <p><strong>Staff:</strong> ${staffDetails?.name || appointment.staffName || 'Not specified'}</p>
            ${businessDetails?.address ? `<p><strong>Location:</strong> ${businessDetails.address}</p>` : ''}
          </div>

          <p>Your feedback helps us improve our services and assists other clients in making informed decisions. It only takes a minute to share your experience.</p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${reviewUrl}" style="background-color: ${primaryColor}; color: ${headerTextColor}; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Leave a Review</a>
          </div>

          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p>${reviewUrl}</p>

          <p>Thank you for your time!</p>

          <p>Best regards,<br>${businessName} Team</p>
        </div>

        <div style="background-color: ${secondaryColor}; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p>Powered by Onpointly</p>
          <p>This email was sent to ${appointment.clientEmail}. If you have any questions, please contact ${businessName}.</p>
        </div>
      </div>
    `;

    // In development, use the mock email service for logging
    if (isDevelopment) {
      console.log("Development environment - using mock email service for review request");
      await sendMockReviewRequestEmail(reviewRequest, appointment, businessName, reviewUrl);
    }

    // We've removed SMTP functionality and will only use Mailgun for email services

    // Try to send with Mailgun first (primary service)
    try {
      console.log('Attempting to send review request email via Mailgun...');
      const result = await sendMailgunEmail(
        appointment.clientEmail,
        subject,
        html,
        'OnPointly <<EMAIL>>'
      );

      if (result.success) {
        console.log('Review request email sent successfully with Mailgun:', result.id);
        return;
      } else {
        console.error('Failed to send review request email with Mailgun:', result.error);
        console.error('Error details:', result.details);
        // Fall through to Resend as fallback
      }
    } catch (mailgunError) {
      console.error('Error sending review request email with Mailgun:', mailgunError);
      // Fall through to Resend as fallback
    }

    // Try to send with Resend as a fallback
    if (resend) {
      try {
        console.log('Falling back to Resend for review request email...');
        const { data, error } = await resend.emails.send({
          from: 'OnPointly <<EMAIL>>',
          to: appointment.clientEmail,
          subject,
          html,
        });

        if (error) {
          console.error('Failed to send review request email with Resend:', error);
          throw error;
        }

        console.log('Review request email sent successfully with Resend:', data);
        return;
      } catch (resendError) {
        console.error('Error sending email with Resend:', resendError);
        // Fall through to API route method
      }
    }

    // Try using the API route as a last resort
    try {
      console.log('Attempting to send review request email via API route...');
      const response = await fetch('/api/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: appointment.clientEmail,
          subject,
          html,
          from: 'OnPointly <<EMAIL>>',
          preferredProvider: 'mailgun' // Explicitly request Mailgun
        }),
      });

      if (!response.ok) {
        throw new Error(`API route responded with status: ${response.status}`);
      }

      console.log('Review request email sent successfully via API route');
      return;
    } catch (apiError) {
      console.error('Failed to send email via API route:', apiError);
      throw apiError;
    }
  } catch (error) {
    console.error('Error sending review request email:', error);
    throw error;
  }
};

export const sendWaitlistConfirmationEmail = async (
  waitlistEntry: WaitlistEntry,
  businessName: string
): Promise<void> => {
  try {
    const subject = `You've Been Added to the Waitlist at ${businessName}`;

    // Create the email content
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4f46e5; color: white; padding: 20px; text-align: center;">
          <h1 style="margin: 0;">Waitlist Confirmation</h1>
        </div>

        <div style="padding: 20px; border: 1px solid #e5e7eb; border-top: none;">
          <p>Hello ${waitlistEntry.clientName},</p>

          <p>You have been successfully added to the waitlist at ${businessName}.</p>

          <div style="background-color: #f9fafb; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #4f46e5;">Waitlist Details</h3>
            <p><strong>Position:</strong> ${waitlistEntry.position}</p>

            ${waitlistEntry.preferredTimeRanges && waitlistEntry.preferredTimeRanges.length > 0 ? `
              <p><strong>Your Preferred Times:</strong></p>
              <ul>
                ${waitlistEntry.preferredTimeRanges.map(range => {
                  const startDate = range.startTime.toDate().toLocaleDateString();
                  const startTime = range.startTime.toDate().toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true,
                  });
                  const endTime = range.endTime.toDate().toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true,
                  });
                  return `<li>${startDate} from ${startTime} to ${endTime}</li>`;
                }).join('')}
              </ul>
            ` : ''}
          </div>

          <p>We'll notify you as soon as an appointment slot becomes available that matches your preferences.</p>

          <p>Thank you for choosing ${businessName}!</p>
        </div>

        <div style="background-color: #f3f4f6; padding: 15px; text-align: center; font-size: 12px; color: #6b7280;">
          <p>This is an automated message from Onpointly. Please do not reply to this email.</p>
        </div>
      </div>
    `;

    // In development, use the mock email service
    if (isDevelopment) {
      console.log("Development environment - using mock email service");
      await sendMockEmail(waitlistEntry.clientEmail, subject, html);
      return;
    }

    // Try to send with Resend first
    if (resend) {
      try {
        const { data, error } = await resend.emails.send({
          from: 'OnPointly <<EMAIL>>',
          to: waitlistEntry.clientEmail,
          subject,
          html,
        });

        if (error) {
          console.error('Failed to send waitlist confirmation email with Resend:', error);
          throw error;
        }

        console.log('Waitlist confirmation email sent successfully with Resend:', data);
        return;
      } catch (resendError) {
        console.error('Error sending email with Resend:', resendError);
        // Fall through to alternative methods
      }
    }

    // Try using the API route directly
    try {
      console.log('Attempting to send email via API route...');
      const response = await fetch('/api/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: waitlistEntry.clientEmail,
          subject,
          html,
        }),
      });

      if (!response.ok) {
        throw new Error(`API route responded with status: ${response.status}`);
      }

      console.log('Waitlist confirmation email sent successfully via API route');
      return;
    } catch (apiError) {
      console.error('Failed to send email via API route:', apiError);
      throw apiError;
    }


  } catch (error) {
    console.error('Error sending waitlist confirmation email:', error);
    throw error;
  }
};