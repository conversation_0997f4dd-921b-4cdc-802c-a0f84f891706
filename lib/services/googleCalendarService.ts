import { Appointment } from '@/lib/types/db';
import { GoogleCalendarEventRequest, GoogleCalendarSettings, GoogleCalendarTokens } from '@/lib/types/googleCalendar';
import { db } from '../firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';

// Helper function to get business timezone
async function getBusinessTimezone(businessId: string): Promise<string> {
  try {
    const businessRef = doc(db, 'businesses', businessId);
    const businessDoc = await getDoc(businessRef);

    if (businessDoc.exists() && businessDoc.data().timeZone) {
      return businessDoc.data().timeZone;
    }

    // Default to UTC if no timezone is set
    return 'UTC';
  } catch (error) {
    console.error('Error getting business timezone:', error);
    return 'UTC';
  }
}

// Refresh token if it's expired or about to expire
async function refreshTokenIfNeeded(businessId: string, tokens: GoogleCalendarTokens): Promise<GoogleCalendarTokens> {
  const now = Date.now();
  // Refresh if token expires in less than 5 minutes
  if (tokens.expires_at - now < 5 * 60 * 1000) {
    const response = await fetch('/api/auth/google/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        businessId,
        refresh_token: tokens.refresh_token
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to refresh Google Calendar token');
    }

    const newTokens = await response.json();

    // Update tokens in Firestore
    const businessRef = doc(db, 'businesses', businessId);
    await updateDoc(businessRef, {
      'googleCalendar.tokens': newTokens
    });

    return newTokens;
  }

  return tokens;
}

// Get Google Calendar settings for a business
export async function getGoogleCalendarSettings(businessId: string): Promise<GoogleCalendarSettings | null> {
  const businessRef = doc(db, 'businesses', businessId);
  const businessDoc = await getDoc(businessRef);

  if (!businessDoc.exists()) {
    return null;
  }

  return businessDoc.data()?.googleCalendar || null;
}

// Create a Google Calendar event for an appointment
export async function createGoogleCalendarEvent(
  appointment: Appointment,
  businessId: string
): Promise<string | null> {
  // If this is a recurring appointment, use the recurring event creation
  if (appointment.isRecurring && appointment.recurringPattern) {
    return createRecurringGoogleCalendarEvent(appointment, businessId);
  }
  try {
    // Get Google Calendar settings
    const settings = await getGoogleCalendarSettings(businessId);
    if (!settings?.enabled || !settings.tokens) {
      console.log('Google Calendar not enabled for this business');
      return null;
    }

    // Refresh token if needed
    const tokens = await refreshTokenIfNeeded(businessId, settings.tokens);

    // Prepare attendees
    const attendees = [];

    // Add client as attendee
    if (appointment.clientEmail) {
      attendees.push({
        email: appointment.clientEmail,
        displayName: appointment.clientName,
        optional: false,
      });
    }

    // Add staff member as attendee with proper permissions
    // Ensure staff always receives notifications
    if (appointment.staffEmail) {
      attendees.push({
        email: appointment.staffEmail,
        displayName: appointment.staffName || '',
        responseStatus: 'accepted' as const,
        optional: false,
      });
      console.log('Added staff member to calendar event attendees:', appointment.staffEmail);
    } else if (appointment.staffId) {
      // If staffEmail is not provided but staffId is, try to get the staff email
      try {
        const staffRef = doc(db, 'staff', appointment.staffId);
        const staffDoc = await getDoc(staffRef);
        if (staffDoc.exists() && staffDoc.data().email) {
          const staffEmail = staffDoc.data().email;
          attendees.push({
            email: staffEmail,
            displayName: appointment.staffName || staffDoc.data().name || '',
            responseStatus: 'accepted' as const,
            optional: false,
          });
          console.log('Added staff member to calendar event attendees from staffId:', staffEmail);
        }
      } catch (error) {
        console.error('Error getting staff email from staffId:', error);
      }
    }

    // Get business email to ensure it's the organizer
    const businessRef = doc(db, 'businesses', businessId);
    const businessDoc = await getDoc(businessRef);
    const businessEmail = businessDoc.exists() ? businessDoc.data()?.email : null;

    console.log('Business email for calendar event:', businessEmail);

    // Create event request
    const eventRequest: GoogleCalendarEventRequest = {
      summary: `${appointment.serviceName || 'Appointment'} - ${appointment.clientName}`,
      description: createEventDescription(appointment),
      start: {
        dateTime: appointment.startTime.toDate().toISOString(),
        timeZone: await getBusinessTimezone(businessId),
      },
      end: {
        dateTime: appointment.endTime.toDate().toISOString(),
        timeZone: await getBusinessTimezone(businessId),
      },
      attendees,
      // If business email exists and is different from client email, set as organizer
      organizer: businessEmail && businessEmail !== appointment.clientEmail ? {
        email: businessEmail,
        self: true
      } : undefined,
      guestsCanSeeOtherGuests: settings.visibility?.clientCanSeeOtherGuests || true,
      guestsCanModify: settings.visibility?.clientCanModify || false,
      visibility: settings.visibility?.defaultVisibility || 'private',
      reminders: {
        useDefault: false, // Don't use default reminders
        overrides: settings.defaultReminders || [
          { method: 'email', minutes: 1440 }, // 24 hours before
          { method: 'email', minutes: 480 },  // Morning of (8 hours before)
          { method: 'email', minutes: 30 }    // 30 minutes before
        ],
      },
      sendUpdates: 'all', // Always send updates to all attendees
      sendNotifications: true, // Ensure notifications are sent
      // Add additional parameters to ensure notifications are delivered
      conferenceDataVersion: 0,
      // Force notification delivery
      eventNotifications: [
        { type: 'eventCreation', method: 'email' },
        { type: 'eventChange', method: 'email' },
        { type: 'eventCancellation', method: 'email' }
      ]
    };

    console.log('Creating Google Calendar event with request:', eventRequest);
    console.log('Using access token:', tokens.access_token ? `${tokens.access_token.substring(0, 10)}...` : 'No token');
    try {
      // Log detailed information about the request
      console.log('Google Calendar API request details:', {
        url: 'https://www.googleapis.com/calendar/v3/calendars/primary/events',
        method: 'POST',
        hasAuthHeader: !!tokens.access_token,
        tokenExpiry: tokens.expires_at ? new Date(tokens.expires_at).toISOString() : 'unknown',
        hasAttendees: eventRequest.attendees && eventRequest.attendees.length > 0,
        attendeeCount: eventRequest.attendees?.length || 0
      });

      const response = await fetch(
        `https://www.googleapis.com/calendar/v3/calendars/primary/events`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${tokens.access_token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(eventRequest),
        }
      );

      console.log('Google Calendar API response status:', response.status);
      const responseData = await response.json();
      console.log('Google Calendar API response:', responseData);

      if (!response.ok) {
        // Check if it's an API not enabled error
        if (responseData.error?.status === 'PERMISSION_DENIED' ||
            responseData.error?.message?.includes('has not been used in project') ||
            responseData.error?.message?.includes('it is disabled')) {
          throw new Error(`Google Calendar API is not enabled. Please enable it in the Google Cloud Console and try again.`);
        }
        throw new Error(`Failed to create Google Calendar event: ${responseData.error?.message || 'Unknown error'}`);
      }

      // Update appointment with Google Calendar info
      await updateDoc(doc(db, 'appointments', appointment.id), {
        googleCalendarEventId: responseData.id,
        googleCalendarLink: responseData.htmlLink,
      });

      return responseData.id;
    } catch (error) {
      console.error('Error creating Google Calendar event:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error creating Google Calendar event:', error);
    throw error;
  }
}

// Helper function to create event description
function createEventDescription(appointment: Appointment): string {
  let description = `Appointment with ${appointment.clientName}`;

  if (appointment.serviceName) {
    description += `\nService: ${appointment.serviceName}`;
  }

  if (appointment.notes) {
    description += `\n\nNotes: ${appointment.notes}`;
  }

  description += '\n\nContact Information:';
  description += `\nName: ${appointment.clientName}`;
  if (appointment.clientEmail) {
    description += `\nEmail: ${appointment.clientEmail}`;
  }
  if (appointment.clientPhone) {
    description += `\nPhone: ${appointment.clientPhone}`;
  }

  return description;
}

// Update a Google Calendar event
export async function updateGoogleCalendarEvent(
  appointment: Appointment,
  businessId: string
): Promise<void> {
  if (!appointment.googleCalendarEventId) {
    console.log('No Google Calendar event ID found for this appointment, skipping calendar update');
    return;
  }

  const settings = await getGoogleCalendarSettings(businessId);
  if (!settings?.enabled || !settings.tokens) {
    throw new Error('Google Calendar not enabled for this business');
  }

  const tokens = await refreshTokenIfNeeded(businessId, settings.tokens);

  // Get business email to ensure it's the organizer
  const businessRef = doc(db, 'businesses', businessId);
  const businessDoc = await getDoc(businessRef);
  const businessEmail = businessDoc.exists() ? businessDoc.data()?.email : null;

  console.log('Business email for calendar event update:', businessEmail);

  // Create event request similar to createGoogleCalendarEvent
  const eventRequest: GoogleCalendarEventRequest = {
    summary: `${appointment.serviceName || 'Appointment'} - ${appointment.clientName}`,
    description: createEventDescription(appointment),
    start: {
      dateTime: appointment.startTime.toDate().toISOString(),
      timeZone: await getBusinessTimezone(businessId),
    },
    end: {
      dateTime: appointment.endTime.toDate().toISOString(),
      timeZone: await getBusinessTimezone(businessId),
    },
    attendees: [
      {
        email: appointment.clientEmail,
        displayName: appointment.clientName,
        optional: false,
      },
      ...(appointment.staffEmail ? [{
        email: appointment.staffEmail,
        displayName: appointment.staffName || '',
        responseStatus: 'accepted' as const,
        optional: false,
      }] : []),
    ],
    // If business email exists and is different from client email, set as organizer
    organizer: businessEmail && businessEmail !== appointment.clientEmail ? {
      email: businessEmail,
      self: true
    } : undefined,
    guestsCanSeeOtherGuests: settings.visibility.clientCanSeeOtherGuests,
    guestsCanModify: settings.visibility.clientCanModify,
    visibility: settings.visibility.defaultVisibility,
    reminders: {
      useDefault: false,
      overrides: settings.defaultReminders,
    },
    sendUpdates: 'all', // Always send updates to all attendees
    sendNotifications: true, // Ensure notifications are sent
  };

  const response = await fetch(
    `https://www.googleapis.com/calendar/v3/calendars/primary/events/${appointment.googleCalendarEventId}`,
    {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(eventRequest),
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to update Google Calendar event: ${error.error?.message || 'Unknown error'}`);
  }
}

// Create a recurring Google Calendar event
export async function createRecurringGoogleCalendarEvent(
  appointment: Appointment,
  businessId: string
): Promise<string | null> {
  try {
    // Get Google Calendar settings
    const settings = await getGoogleCalendarSettings(businessId);
    if (!settings?.enabled || !settings.tokens) {
      console.log('Google Calendar not enabled for this business');
      return null;
    }

    // Refresh token if needed
    const tokens = await refreshTokenIfNeeded(businessId, settings.tokens);

    // Prepare attendees
    const attendees = [];

    // Add client as attendee
    if (appointment.clientEmail) {
      attendees.push({
        email: appointment.clientEmail,
        displayName: appointment.clientName,
        optional: false,
      });
    }

    // Add staff member as attendee with proper permissions
    // Ensure staff always receives notifications
    if (appointment.staffEmail) {
      attendees.push({
        email: appointment.staffEmail,
        displayName: appointment.staffName || '',
        responseStatus: 'accepted' as const,
        optional: false,
      });
      console.log('Added staff member to recurring calendar event attendees:', appointment.staffEmail);
    } else if (appointment.staffId) {
      // If staffEmail is not provided but staffId is, try to get the staff email
      try {
        const staffRef = doc(db, 'staff', appointment.staffId);
        const staffDoc = await getDoc(staffRef);
        if (staffDoc.exists() && staffDoc.data().email) {
          const staffEmail = staffDoc.data().email;
          attendees.push({
            email: staffEmail,
            displayName: appointment.staffName || staffDoc.data().name || '',
            responseStatus: 'accepted' as const,
            optional: false,
          });
          console.log('Added staff member to recurring calendar event attendees from staffId:', staffEmail);
        }
      } catch (error) {
        console.error('Error getting staff email from staffId:', error);
      }
    }

    // Get business email to ensure it's the organizer
    const businessRef = doc(db, 'businesses', businessId);
    const businessDoc = await getDoc(businessRef);
    const businessEmail = businessDoc.exists() ? businessDoc.data()?.email : null;

    console.log('Business email for recurring calendar event:', businessEmail);

    // Generate RRULE string based on the recurring pattern
    if (!appointment.recurringPattern) {
      throw new Error('Recurring pattern is required for recurring appointments');
    }
    const recurrenceRule = generateRecurrenceRule(appointment.recurringPattern);
    console.log('Generated recurrence rule:', recurrenceRule);

    // Create event request
    const eventRequest: GoogleCalendarEventRequest = {
      summary: `${appointment.serviceName || 'Appointment'} - ${appointment.clientName}`,
      description: createEventDescription(appointment),
      start: {
        dateTime: appointment.startTime.toDate().toISOString(),
        timeZone: await getBusinessTimezone(businessId),
      },
      end: {
        dateTime: appointment.endTime.toDate().toISOString(),
        timeZone: await getBusinessTimezone(businessId),
      },
      attendees,
      // If business email exists and is different from client email, set as organizer
      organizer: businessEmail && businessEmail !== appointment.clientEmail ? {
        email: businessEmail,
        self: true
      } : undefined,
      guestsCanSeeOtherGuests: settings.visibility?.clientCanSeeOtherGuests || true,
      guestsCanModify: settings.visibility?.clientCanModify || false,
      visibility: settings.visibility?.defaultVisibility || 'private',
      reminders: {
        useDefault: false, // Don't use default reminders
        overrides: settings.defaultReminders || [
          { method: 'email', minutes: 60 },
          { method: 'popup', minutes: 30 }
        ],
      },
      sendUpdates: 'all', // Always send updates to all attendees
      sendNotifications: true, // Ensure notifications are sent
      // Add additional parameters to ensure notifications are delivered
      conferenceDataVersion: 0,
      // Force notification delivery
      eventNotifications: [
        { type: 'eventCreation', method: 'email' },
        { type: 'eventChange', method: 'email' },
        { type: 'eventCancellation', method: 'email' }
      ],
      recurrence: [recurrenceRule], // Add the recurrence rule
    };

    console.log('Creating recurring Google Calendar event with request:', eventRequest);

    const response = await fetch(
      `https://www.googleapis.com/calendar/v3/calendars/primary/events`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokens.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(eventRequest),
      }
    );

    console.log('Google Calendar API response status:', response.status);
    const responseData = await response.json();
    console.log('Google Calendar API response:', responseData);

    if (!response.ok) {
      throw new Error(`Failed to create recurring Google Calendar event: ${responseData.error?.message || 'Unknown error'}`);
    }

    // Update appointment with Google Calendar info
    await updateDoc(doc(db, 'appointments', appointment.id), {
      googleCalendarEventId: responseData.id,
      googleCalendarLink: responseData.htmlLink,
    });

    return responseData.id;
  } catch (error) {
    console.error('Error creating recurring Google Calendar event:', error);
    throw error;
  }
}

// Helper function to generate RRULE string for Google Calendar
function generateRecurrenceRule(recurringPattern: NonNullable<Appointment['recurringPattern']>): string {
  const { frequency, interval, endDate, endAfterOccurrences } = recurringPattern;
  const daysOfWeek = recurringPattern.daysOfWeek || [];

  let rrule = 'RRULE:';

  // Set frequency
  switch (frequency) {
    case 'daily':
      rrule += 'FREQ=DAILY';
      break;
    case 'weekly':
      rrule += 'FREQ=WEEKLY';
      break;
    case 'monthly':
      rrule += 'FREQ=MONTHLY';
      break;
    case 'custom':
      // Default to daily if custom but no specific pattern
      rrule += 'FREQ=DAILY';
      break;
    default:
      rrule += 'FREQ=DAILY';
  }

  // Set interval
  if (interval && interval > 1) {
    rrule += `;INTERVAL=${interval}`;
  }

  // Set specific weekdays for weekly recurrence
  if (frequency === 'weekly' && daysOfWeek.length > 0) {
    // Map numeric day values (0-6) to RRULE day codes
    const dayMap: string[] = ['SU', 'MO', 'TU', 'WE', 'TH', 'FR', 'SA'];

    const days = daysOfWeek.map(dayNum => {
      // Ensure dayNum is between 0-6
      const index = ((dayNum % 7) + 7) % 7;
      return dayMap[index];
    }).filter(Boolean);

    if (days.length > 0) {
      rrule += `;BYDAY=${days.join(',')}`;
    }
  }

  // Set end condition
  if (endAfterOccurrences && endAfterOccurrences > 0) {
    rrule += `;COUNT=${endAfterOccurrences}`;
  } else if (endDate) {
    // Convert Firestore Timestamp to Date
    const untilDate = endDate.toDate();
    const year = untilDate.getUTCFullYear();
    const month = String(untilDate.getUTCMonth() + 1).padStart(2, '0');
    const day = String(untilDate.getUTCDate()).padStart(2, '0');
    rrule += `;UNTIL=${year}${month}${day}T235959Z`;
  }

  return rrule;
}

// Delete a Google Calendar event
export async function deleteGoogleCalendarEvent(
  eventId: string,
  businessId: string
): Promise<void> {
  const settings = await getGoogleCalendarSettings(businessId);
  if (!settings?.enabled || !settings.tokens) {
    throw new Error('Google Calendar not enabled for this business');
  }

  const tokens = await refreshTokenIfNeeded(businessId, settings.tokens);

  const response = await fetch(
    `https://www.googleapis.com/calendar/v3/calendars/primary/events/${eventId}`,
    {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`,
      },
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to delete Google Calendar event: ${error.error?.message || 'Unknown error'}`);
  }
}