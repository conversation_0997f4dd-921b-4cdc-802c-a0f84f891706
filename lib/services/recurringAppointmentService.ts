// lib/services/recurringAppointmentService.ts
import {
  collection,
  addDoc,
  getDocs,
  query,
  where,
  Timestamp,
  serverTimestamp,
  DocumentReference,
  updateDoc,
  deleteDoc,
  doc,
  getDoc,
  writeBatch,
  orderBy
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Appointment } from '@/lib/types/db';
import { addDays, addWeeks, addMonths, getDay, getDate, setDate } from 'date-fns';
import { createAppointment } from './appointmentService';

const appointmentsCollection = 'appointments';

/**
 * Creates a recurring appointment series
 * @param baseAppointment The base appointment data
 * @param recurringPattern The recurring pattern
 * @returns The ID of the parent recurring appointment
 */
export const createRecurringAppointment = async (
  baseAppointment: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>,
  recurringPattern: NonNullable<Appointment['recurringPattern']>,
  googleAccessToken?: string,
  businessTimezone: string = 'UTC'
): Promise<string> => {
  try {
    // Create a sanitized version of the recurring pattern
    // Remove any undefined values that would cause Firebase errors
    const sanitizedPattern = { ...recurringPattern };

    // If endDate is undefined, remove it from the object
    if (sanitizedPattern.endDate === undefined) {
      delete sanitizedPattern.endDate;
    }

    // Handle daysOfWeek for different frequency types
    if (sanitizedPattern.frequency === 'monthly' && sanitizedPattern.daysOfWeek === undefined) {
      // For monthly frequency, remove daysOfWeek if it's undefined
      delete sanitizedPattern.daysOfWeek;
      console.log('Removed undefined daysOfWeek for monthly recurring pattern');
    } else if (sanitizedPattern.frequency === 'daily' && sanitizedPattern.daysOfWeek === undefined) {
      // For daily frequency, remove daysOfWeek if it's undefined
      delete sanitizedPattern.daysOfWeek;
      console.log('Removed undefined daysOfWeek for daily recurring pattern');
    }

    // Create the parent recurring appointment with the sanitized pattern
    const parentAppointment = {
      ...baseAppointment,
      isRecurring: true,
      recurringPattern: sanitizedPattern,
    };

    // Save the parent appointment
    // Make sure staffId is properly set when not using round-robin
    const useRoundRobin = baseAppointment.useRoundRobin === true;

    // Handle undefined values in parent appointment
    const cleanParentAppointment = { ...parentAppointment };
    Object.keys(cleanParentAppointment).forEach(key => {
      if (cleanParentAppointment[key as keyof typeof cleanParentAppointment] === undefined) {
        console.log(`Removing undefined value for field ${key} in parent recurring appointment`);
        delete cleanParentAppointment[key as keyof typeof cleanParentAppointment];
      }
    });

    // If not using round-robin and staffId is undefined, remove the property to avoid Firebase errors
    if (!useRoundRobin && cleanParentAppointment.staffId === undefined) {
      console.log('Removing undefined staffId for parent recurring appointment');
      delete cleanParentAppointment.staffId;
    }

    const parentAppointmentRef = await createAppointment({
      ...cleanParentAppointment,
      useRoundRobin
    }, 'email_ics'); // Default to email_ics for recurring appointments

    const parentAppointmentId = parentAppointmentRef;
    console.log(`Created parent recurring appointment: ${parentAppointmentId}`);

    // Generate the recurring instances
    await generateRecurringInstances(
      parentAppointmentId,
      baseAppointment,
      recurringPattern,
      googleAccessToken,
      businessTimezone
    );

    return parentAppointmentId;
  } catch (error) {
    console.error('Error creating recurring appointment:', error);
    throw error;
  }
};

/**
 * Generates instances of a recurring appointment
 * @param parentAppointmentId The ID of the parent recurring appointment
 * @param baseAppointment The base appointment data
 * @param recurringPattern The recurring pattern
 */
export const generateRecurringInstances = async (
  parentAppointmentId: string,
  baseAppointment: Omit<Appointment, 'id' | 'createdAt' | 'updatedAt'>,
  recurringPattern: NonNullable<Appointment['recurringPattern']>,
  googleAccessToken?: string,
  businessTimezone: string = 'UTC'
): Promise<void> => {
  try {
    // Destructure with default values to handle undefined properties
    const {
      frequency,
      interval,
      endDate = null, // Use null instead of undefined
      endAfterOccurrences = 52, // Default to 52 occurrences if not specified
      exceptions = []
    } = recurringPattern;

    // Convert exceptions to Date objects for easier comparison
    const exceptionDates = exceptions.map(exception =>
      exception instanceof Timestamp ? exception.toDate() : exception
    );

    // Get the start date of the base appointment
    const startDate = baseAppointment.startTime instanceof Timestamp
      ? baseAppointment.startTime.toDate()
      : new Date(baseAppointment.startTime as any);

    // Calculate the duration in milliseconds
    const endDate1 = baseAppointment.endTime instanceof Timestamp
      ? baseAppointment.endTime.toDate()
      : new Date(baseAppointment.endTime as any);
    const durationMs = endDate1.getTime() - startDate.getTime();

    // Determine the end condition
    const maxOccurrences = endAfterOccurrences || 52; // Default to 1 year of weekly appointments
    const maxEndDate = endDate
      ? endDate instanceof Timestamp ? endDate.toDate() : new Date(endDate as any)
      : addDays(startDate, 365); // Default to 1 year from start

    // Generate the recurring dates
    const recurringDates: Date[] = [];
    let currentDate = new Date(startDate);
    let occurrenceCount = 0;

    while (currentDate <= maxEndDate && occurrenceCount < maxOccurrences) {
      // Skip the first occurrence as it's the parent appointment
      if (occurrenceCount > 0) {
        // Check if this date is in the exceptions list
        const isException = exceptionDates.some(exceptionDate =>
          exceptionDate.getFullYear() === currentDate.getFullYear() &&
          exceptionDate.getMonth() === currentDate.getMonth() &&
          exceptionDate.getDate() === currentDate.getDate()
        );

        if (!isException) {
          recurringDates.push(new Date(currentDate));
        }
      }

      // Calculate the next occurrence based on frequency
      switch (frequency) {
        case 'daily':
          currentDate = addDays(currentDate, interval);
          break;
        case 'weekly':
          currentDate = addWeeks(currentDate, interval);
          break;
        case 'monthly':
          currentDate = addMonths(currentDate, interval);
          break;
        case 'custom':
          // For custom frequency, handle based on daysOfWeek or dayOfMonth
          if (recurringPattern.daysOfWeek && recurringPattern.daysOfWeek.length > 0) {
            // Find the next day of week in the list
            let found = false;
            let tempDate = addDays(currentDate, 1); // Start from tomorrow

            while (!found) {
              const dayOfWeek = getDay(tempDate);
              if (recurringPattern.daysOfWeek.includes(dayOfWeek)) {
                currentDate = tempDate;
                found = true;
              } else {
                tempDate = addDays(tempDate, 1);
              }
            }
          } else if (recurringPattern.dayOfMonth) {
            // Move to the same day next month
            const nextMonth = addMonths(currentDate, 1);
            const maxDaysInMonth = new Date(nextMonth.getFullYear(), nextMonth.getMonth() + 1, 0).getDate();
            const targetDay = Math.min(recurringPattern.dayOfMonth, maxDaysInMonth);
            currentDate = setDate(nextMonth, targetDay);
          } else {
            // Default to weekly if no specific pattern
            currentDate = addWeeks(currentDate, 1);
          }
          break;
      }

      occurrenceCount++;
    }

    console.log(`Generated ${recurringDates.length} recurring instances`);

    // Create appointments for each recurring date using batch operations
    const batch = writeBatch(db);
    const createdAppointments: DocumentReference[] = [];
    const useRoundRobin = baseAppointment.useRoundRobin === true;

    // Create a clean copy of the base appointment data without undefined values
    const cleanBaseAppointment = { ...baseAppointment };

    // Handle undefined staffId - Firebase doesn't accept undefined values
    if (cleanBaseAppointment.staffId === undefined) {
      // If using round-robin or staffId is undefined, remove the staffId property
      delete cleanBaseAppointment.staffId;
      console.log('Removed undefined staffId for recurring appointment instances');
    }

    // Prepare common data for all instances
    const commonData = {
      ...cleanBaseAppointment, // Use the cleaned data without undefined values
      parentRecurringAppointmentId: parentAppointmentId,
      isRecurring: false, // These are instances, not parents
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    // If not using round-robin and staffId is undefined, remove it to avoid Firebase errors
    if (!useRoundRobin && commonData.staffId === undefined) {
      console.log('Removing undefined staffId for recurring instances');
      delete commonData.staffId;
    }

    // Maximum batch size in Firestore is 500
    const MAX_BATCH_SIZE = 450; // Using slightly less than max for safety
    let currentBatch = writeBatch(db);
    let batchCount = 0;
    let totalCreated = 0;

    console.log(`Creating ${recurringDates.length} recurring instances in batches...`);

    for (const date of recurringDates) {
      // Calculate the end time based on the duration
      const endTime = new Date(date.getTime() + durationMs);

      // Create a new document reference
      const newAppointmentRef = doc(collection(db, appointmentsCollection));
      createdAppointments.push(newAppointmentRef);

      // Create the instance appointment data
      const instanceData = {
        ...commonData,
        startTime: Timestamp.fromDate(date),
        endTime: Timestamp.fromDate(endTime),
      };

      // Final safety check for undefined values
      const cleanInstanceData = { ...instanceData };
      Object.keys(cleanInstanceData).forEach(key => {
        if ((cleanInstanceData as any)[key] === undefined) {
          console.log(`Removing undefined value for field ${key} in recurring instance`);
          delete (cleanInstanceData as any)[key];
        }
      });

      // Add to batch
      currentBatch.set(newAppointmentRef, cleanInstanceData);
      batchCount++;

      // If we've reached the batch limit, commit and start a new batch
      if (batchCount >= MAX_BATCH_SIZE) {
        console.log(`Committing batch of ${batchCount} appointments...`);
        await currentBatch.commit();
        totalCreated += batchCount;
        console.log(`Created ${totalCreated}/${recurringDates.length} appointments so far...`);

        // Start a new batch
        currentBatch = writeBatch(db);
        batchCount = 0;
      }
    }

    // Commit any remaining appointments
    if (batchCount > 0) {
      console.log(`Committing final batch of ${batchCount} appointments...`);
      await currentBatch.commit();
      totalCreated += batchCount;
    }

    // Now handle Google Calendar integration if needed
    // For recurring appointments, we only need to create one event in Google Calendar with recurrence
    if (googleAccessToken) {
      console.log('Adding recurring appointment to Google Calendar...');
      try {
        // Import services dynamically to avoid circular dependencies
        const { createGoogleCalendarEvent } = await import('./googleCalendarService');

        // Get the parent appointment data
        const parentAppointmentDoc = await getDoc(doc(db, appointmentsCollection, parentAppointmentId));
        if (parentAppointmentDoc.exists()) {
          const parentAppointmentData = { id: parentAppointmentId, ...parentAppointmentDoc.data() } as Appointment;

          // Create the recurring event in Google Calendar
          // The createGoogleCalendarEvent function will detect it's a recurring appointment
          // and use the appropriate method to create it with recurrence rules
          const eventId = await createGoogleCalendarEvent(
            parentAppointmentData,
            baseAppointment.businessId
          );

          if (eventId) {
            console.log(`Created recurring Google Calendar event with ID: ${eventId}`);

            // Update all appointments with the same Google Calendar event ID
            // This ensures all instances are linked to the same recurring event
            const batch = writeBatch(db);
            let batchCount = 0;
            const MAX_BATCH_SIZE = 450;

            // Update parent appointment
            batch.update(doc(db, appointmentsCollection, parentAppointmentId), {
              googleCalendarEventId: eventId
            });
            batchCount++;

            // Update all child appointments
            for (const appointmentRef of createdAppointments) {
              batch.update(appointmentRef, {
                googleCalendarEventId: eventId
              });
              batchCount++;

              // If we've reached the batch limit, commit and start a new batch
              if (batchCount >= MAX_BATCH_SIZE) {
                await batch.commit();
                console.log(`Committed batch of ${batchCount} appointment updates`);
                batchCount = 0;
              }
            }

            // Commit any remaining updates
            if (batchCount > 0) {
              await batch.commit();
              console.log(`Committed final batch of ${batchCount} appointment updates`);
            }
          }
        }
      } catch (error) {
        console.error('Error setting up Google Calendar integration for recurring appointment:', error);
        // Don't throw the error, just log it
      }
    }

    console.log(`Created ${createdAppointments.length} recurring appointment instances`);
  } catch (error) {
    console.error('Error generating recurring instances:', error);
    throw error;
  }
};

/**
 * Gets all appointments in a recurring series
 * @param parentAppointmentId The ID of the parent recurring appointment
 * @returns An array of appointments in the series
 */
export const getRecurringAppointmentSeries = async (
  parentAppointmentId: string
): Promise<Appointment[]> => {
  try {
    // Get the parent appointment
    const parentAppointmentDoc = await getDoc(doc(db, appointmentsCollection, parentAppointmentId));
    if (!parentAppointmentDoc.exists()) {
      throw new Error(`Parent appointment ${parentAppointmentId} not found`);
    }

    const parentAppointment = {
      id: parentAppointmentDoc.id,
      ...parentAppointmentDoc.data()
    } as Appointment;

    // Get all instances of this recurring appointment
    const instancesQuery = query(
      collection(db, appointmentsCollection),
      where('parentRecurringAppointmentId', '==', parentAppointmentId),
      orderBy('startTime', 'asc')
    );

    const instancesSnapshot = await getDocs(instancesQuery);
    const instances = instancesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Appointment));

    // Combine parent and instances
    return [parentAppointment, ...instances];
  } catch (error) {
    console.error('Error getting recurring appointment series:', error);
    throw error;
  }
};

/**
 * Updates a recurring appointment series
 * @param parentAppointmentId The ID of the parent recurring appointment
 * @param updatedAppointment The updated appointment data
 * @param updateMode Whether to update just this instance, all future instances, or the entire series
 * @param instanceId Optional ID of a specific instance to update
 */
export const updateRecurringAppointment = async (
  parentAppointmentId: string,
  updatedAppointment: Partial<Appointment>,
  updateMode: 'this' | 'future' | 'all',
  instanceId?: string
): Promise<void> => {
  try {
    // Get the parent appointment
    const parentAppointmentDoc = await getDoc(doc(db, appointmentsCollection, parentAppointmentId));
    if (!parentAppointmentDoc.exists()) {
      throw new Error(`Parent appointment ${parentAppointmentId} not found`);
    }

    const parentAppointment = {
      id: parentAppointmentDoc.id,
      ...parentAppointmentDoc.data()
    } as Appointment;

    // Handle different update modes
    switch (updateMode) {
      case 'this':
        // Update just one instance
        if (instanceId) {
          await updateDoc(doc(db, appointmentsCollection, instanceId), updatedAppointment);
          console.log(`Updated instance ${instanceId}`);
        } else {
          // Update the parent appointment
          await updateDoc(doc(db, appointmentsCollection, parentAppointmentId), updatedAppointment);
          console.log(`Updated parent appointment ${parentAppointmentId}`);
        }
        break;

      case 'future':
        // Update the parent and all future instances
        if (instanceId) {
          // Get the instance to determine the cutoff date
          const instanceDoc = await getDoc(doc(db, appointmentsCollection, instanceId));
          if (!instanceDoc.exists()) {
            throw new Error(`Instance ${instanceId} not found`);
          }

          const instance = { id: instanceDoc.id, ...instanceDoc.data() } as Appointment;
          const cutoffDate = instance.startTime instanceof Timestamp
            ? instance.startTime.toDate()
            : new Date(instance.startTime as any);

          // Update the instance itself
          await updateDoc(doc(db, appointmentsCollection, instanceId), updatedAppointment);
          console.log(`Updated instance ${instanceId}`);

          // Get all future instances
          const futureInstancesQuery = query(
            collection(db, appointmentsCollection),
            where('parentRecurringAppointmentId', '==', parentAppointmentId),
            where('startTime', '>=', Timestamp.fromDate(cutoffDate))
          );

          const futureInstancesSnapshot = await getDocs(futureInstancesQuery);

          // Update each future instance
          const batch = writeBatch(db);
          futureInstancesSnapshot.docs.forEach(doc => {
            if (doc.id !== instanceId) { // Skip the instance we already updated
              batch.update(doc.ref, updatedAppointment);
            }
          });

          await batch.commit();
          console.log(`Updated ${futureInstancesSnapshot.size - 1} future instances`);
        } else {
          // Update the parent and all instances
          await updateDoc(doc(db, appointmentsCollection, parentAppointmentId), updatedAppointment);
          console.log(`Updated parent appointment ${parentAppointmentId}`);

          // Get all instances
          const instancesQuery = query(
            collection(db, appointmentsCollection),
            where('parentRecurringAppointmentId', '==', parentAppointmentId)
          );

          const instancesSnapshot = await getDocs(instancesQuery);

          // Update each instance
          const batch = writeBatch(db);
          instancesSnapshot.docs.forEach(doc => {
            batch.update(doc.ref, updatedAppointment);
          });

          await batch.commit();
          console.log(`Updated ${instancesSnapshot.size} instances`);
        }
        break;

      case 'all':
        // Update the parent and all instances
        await updateDoc(doc(db, appointmentsCollection, parentAppointmentId), updatedAppointment);
        console.log(`Updated parent appointment ${parentAppointmentId}`);

        // Get all instances
        const instancesQuery = query(
          collection(db, appointmentsCollection),
          where('parentRecurringAppointmentId', '==', parentAppointmentId)
        );

        const instancesSnapshot = await getDocs(instancesQuery);

        // Update each instance
        const batch = writeBatch(db);
        instancesSnapshot.docs.forEach(doc => {
          batch.update(doc.ref, updatedAppointment);
        });

        await batch.commit();
        console.log(`Updated ${instancesSnapshot.size} instances`);
        break;
    }
  } catch (error) {
    console.error('Error updating recurring appointment:', error);
    throw error;
  }
};

/**
 * Deletes a recurring appointment series
 * @param parentAppointmentId The ID of the parent recurring appointment
 * @param deleteMode Whether to delete just this instance, all future instances, or the entire series
 * @param instanceId Optional ID of a specific instance to delete
 */
export const deleteRecurringAppointment = async (
  parentAppointmentId: string,
  deleteMode: 'this' | 'future' | 'all',
  instanceId?: string
): Promise<void> => {
  try {
    // Handle different delete modes
    switch (deleteMode) {
      case 'this':
        // Delete just one instance
        if (instanceId) {
          await deleteDoc(doc(db, appointmentsCollection, instanceId));
          console.log(`Deleted instance ${instanceId}`);
        } else {
          // Delete the parent appointment
          await deleteDoc(doc(db, appointmentsCollection, parentAppointmentId));
          console.log(`Deleted parent appointment ${parentAppointmentId}`);
        }
        break;

      case 'future':
        // Delete the parent and all future instances
        if (instanceId) {
          // Get the instance to determine the cutoff date
          const instanceDoc = await getDoc(doc(db, appointmentsCollection, instanceId));
          if (!instanceDoc.exists()) {
            throw new Error(`Instance ${instanceId} not found`);
          }

          const instance = { id: instanceDoc.id, ...instanceDoc.data() } as Appointment;
          const cutoffDate = instance.startTime instanceof Timestamp
            ? instance.startTime.toDate()
            : new Date(instance.startTime as any);

          // Delete the instance itself
          await deleteDoc(doc(db, appointmentsCollection, instanceId));
          console.log(`Deleted instance ${instanceId}`);

          // Get all future instances
          const futureInstancesQuery = query(
            collection(db, appointmentsCollection),
            where('parentRecurringAppointmentId', '==', parentAppointmentId),
            where('startTime', '>=', Timestamp.fromDate(cutoffDate))
          );

          const futureInstancesSnapshot = await getDocs(futureInstancesQuery);

          // Delete each future instance
          const batch = writeBatch(db);
          futureInstancesSnapshot.docs.forEach(doc => {
            if (doc.id !== instanceId) { // Skip the instance we already deleted
              batch.delete(doc.ref);
            }
          });

          await batch.commit();
          console.log(`Deleted ${futureInstancesSnapshot.size - 1} future instances`);
        } else {
          // Delete the parent and all instances
          await deleteDoc(doc(db, appointmentsCollection, parentAppointmentId));
          console.log(`Deleted parent appointment ${parentAppointmentId}`);

          // Get all instances
          const instancesQuery = query(
            collection(db, appointmentsCollection),
            where('parentRecurringAppointmentId', '==', parentAppointmentId)
          );

          const instancesSnapshot = await getDocs(instancesQuery);

          // Delete each instance
          const batch = writeBatch(db);
          instancesSnapshot.docs.forEach(doc => {
            batch.delete(doc.ref);
          });

          await batch.commit();
          console.log(`Deleted ${instancesSnapshot.size} instances`);
        }
        break;

      case 'all':
        // Delete the parent and all instances
        await deleteDoc(doc(db, appointmentsCollection, parentAppointmentId));
        console.log(`Deleted parent appointment ${parentAppointmentId}`);

        // Get all instances
        const instancesQuery = query(
          collection(db, appointmentsCollection),
          where('parentRecurringAppointmentId', '==', parentAppointmentId)
        );

        const instancesSnapshot = await getDocs(instancesQuery);

        // Delete each instance
        const batch = writeBatch(db);
        instancesSnapshot.docs.forEach(doc => {
          batch.delete(doc.ref);
        });

        await batch.commit();
        console.log(`Deleted ${instancesSnapshot.size} instances`);
        break;
    }
  } catch (error) {
    console.error('Error deleting recurring appointment:', error);
    throw error;
  }
};
