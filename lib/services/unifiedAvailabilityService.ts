import { collection, query, where, getDocs, doc, getDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Business, StaffMember, Appointment, ServiceDefinition } from '@/lib/types/db';
import { format, startOfDay, endOfDay, addMinutes, isSameDay } from 'date-fns';

export interface TimeSlot {
  start: Date;
  end: Date;
  available: boolean;
  staffId?: string;
  staffName?: string;
  staffEmail?: string;
  reason?: string;
}

export interface AvailabilityConstraints {
  businessHours: { start: string; end: string } | null;
  businessBreaks: Array<{
    id: string;
    name: string;
    startTime: string;
    endTime: string;
    daysOfWeek: number[];
    applyToAllStaff: boolean;
  }>;
  staffWorkingHours: { start: string; end: string } | null;
  staffBreaks: Array<{
    startTime: string;
    endTime: string;
    date?: Date;
  }>;
  staffUnavailableDates: Date[];
  existingAppointments: Array<{
    startTime: Date;
    endTime: Date;
    staffId?: string;
  }>;
  holidays: Date[];
}

/**
 * Unified Availability Service
 * Handles all availability constraints in one place:
 * - Business hours
 * - Business breaks
 * - Staff working hours
 * - Staff breaks and time off
 * - Existing appointments
 * - Holidays
 */
export class UnifiedAvailabilityService {
  /**
   * Get available time slots for a specific date and service
   */
  static async getAvailableSlots(
    businessId: string,
    date: Date,
    serviceId: string,
    staffId?: string,
    slotDurationMinutes: number = 15
  ): Promise<TimeSlot[]> {
    try {
      console.log('🔍 Getting available slots for:', {
        businessId,
        date: format(date, 'yyyy-MM-dd'),
        serviceId,
        staffId,
        slotDurationMinutes
      });

      // Get business data
      const business = await this.getBusinessData(businessId);
      if (!business) {
        console.error('Business not found');
        return [];
      }

      // Get service data
      const service = await this.getServiceData(serviceId);
      if (!service) {
        console.error('Service not found');
        return [];
      }

      const serviceDuration = service.durationMinutes;
      const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const dayName = this.getDayName(dayOfWeek);

      // Get business hours for the day
      const businessHours = business.businessHours[dayName];
      if (!businessHours) {
        console.log(`Business is closed on ${dayName}`);
        return [];
      }

      // Get constraints
      const constraints = await this.getConstraints(
        business,
        date,
        staffId,
        businessId
      );

      // Generate time slots
      const slots = this.generateTimeSlots(
        date,
        businessHours.start,
        businessHours.end,
        serviceDuration,
        slotDurationMinutes,
        business.timeZone
      );

      // Filter slots based on all constraints
      const availableSlots = slots.map(slot => {
        const conflicts = this.checkSlotConflicts(slot, constraints, dayOfWeek);
        
        return {
          ...slot,
          available: conflicts.length === 0,
          reason: conflicts.length > 0 ? conflicts.join(', ') : undefined
        };
      });

      console.log(`✅ Generated ${availableSlots.length} slots, ${availableSlots.filter(s => s.available).length} available`);
      return availableSlots;

    } catch (error) {
      console.error('Error getting available slots:', error);
      return [];
    }
  }

  /**
   * Get business data from Firestore
   */
  private static async getBusinessData(businessId: string): Promise<Business | null> {
    try {
      const businessRef = doc(db, 'businesses', businessId);
      const businessDoc = await getDoc(businessRef);
      
      if (!businessDoc.exists()) {
        return null;
      }

      return { id: businessDoc.id, ...businessDoc.data() } as Business;
    } catch (error) {
      console.error('Error fetching business data:', error);
      return null;
    }
  }

  /**
   * Get service data from Firestore
   */
  private static async getServiceData(serviceId: string): Promise<ServiceDefinition | null> {
    try {
      const serviceRef = doc(db, 'services', serviceId);
      const serviceDoc = await getDoc(serviceRef);
      
      if (!serviceDoc.exists()) {
        return null;
      }

      return { id: serviceDoc.id, ...serviceDoc.data() } as ServiceDefinition;
    } catch (error) {
      console.error('Error fetching service data:', error);
      return null;
    }
  }

  /**
   * Get staff data from Firestore
   */
  private static async getStaffData(staffId: string): Promise<StaffMember | null> {
    try {
      const staffRef = doc(db, 'staff', staffId);
      const staffDoc = await getDoc(staffRef);
      
      if (!staffDoc.exists()) {
        return null;
      }

      return { id: staffDoc.id, ...staffDoc.data() } as StaffMember;
    } catch (error) {
      console.error('Error fetching staff data:', error);
      return null;
    }
  }

  /**
   * Get all constraints for availability checking
   */
  private static async getConstraints(
    business: Business,
    date: Date,
    staffId: string | undefined,
    businessId: string
  ): Promise<AvailabilityConstraints> {
    const constraints: AvailabilityConstraints = {
      businessHours: null,
      businessBreaks: business.standardBreaks || [],
      staffWorkingHours: null,
      staffBreaks: [],
      staffUnavailableDates: [],
      existingAppointments: [],
      holidays: []
    };

    // Get staff-specific constraints if staffId is provided
    if (staffId) {
      const staff = await this.getStaffData(staffId);
      if (staff) {
        const dayName = this.getDayName(date.getDay());
        
        // Staff working hours (overrides business hours if set)
        if (staff.workingHours && staff.workingHours[dayName]) {
          const staffHours = staff.workingHours[dayName];
          if (staffHours.isWorking) {
            constraints.staffWorkingHours = {
              start: staffHours.start,
              end: staffHours.end
            };
          }
        }

        // Staff unavailable dates
        if (staff.unavailableDates) {
          constraints.staffUnavailableDates = staff.unavailableDates
            .map(timestamp => timestamp.toDate())
            .filter(unavailableDate => isSameDay(unavailableDate, date));
        }

        // Staff breaks
        if (staff.breaks) {
          constraints.staffBreaks = staff.breaks
            .filter(breakItem => {
              if (breakItem.date) {
                return isSameDay(breakItem.date.toDate(), date);
              }
              return false;
            })
            .map(breakItem => ({
              startTime: breakItem.startTime,
              endTime: breakItem.endTime,
              date: breakItem.date?.toDate()
            }));
        }
      }
    }

    // Get existing appointments for the day
    constraints.existingAppointments = await this.getExistingAppointments(
      businessId,
      date,
      staffId
    );

    return constraints;
  }

  /**
   * Get existing appointments for a specific date
   */
  private static async getExistingAppointments(
    businessId: string,
    date: Date,
    staffId?: string
  ): Promise<Array<{ startTime: Date; endTime: Date; staffId?: string }>> {
    try {
      const startOfDayTimestamp = Timestamp.fromDate(startOfDay(date));
      const endOfDayTimestamp = Timestamp.fromDate(endOfDay(date));

      let appointmentsQuery = query(
        collection(db, 'appointments'),
        where('businessId', '==', businessId),
        where('startTime', '>=', startOfDayTimestamp),
        where('startTime', '<=', endOfDayTimestamp),
        where('status', 'in', ['scheduled', 'confirmed'])
      );

      // Add staff filter if provided
      if (staffId) {
        appointmentsQuery = query(
          appointmentsQuery,
          where('staffId', '==', staffId)
        );
      }

      const appointmentsSnapshot = await getDocs(appointmentsQuery);
      
      return appointmentsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          startTime: data.startTime.toDate(),
          endTime: data.endTime.toDate(),
          staffId: data.staffId
        };
      });
    } catch (error) {
      console.error('Error fetching existing appointments:', error);
      return [];
    }
  }

  /**
   * Generate time slots for a given day and time range
   */
  private static generateTimeSlots(
    date: Date,
    startTime: string,
    endTime: string,
    serviceDurationMinutes: number,
    slotIntervalMinutes: number,
    timeZone: string
  ): TimeSlot[] {
    const slots: TimeSlot[] = [];
    
    // Parse start and end times
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);
    
    // Create start and end date objects for the day
    const dayStart = new Date(date);
    dayStart.setHours(startHour, startMinute, 0, 0);
    
    const dayEnd = new Date(date);
    dayEnd.setHours(endHour, endMinute, 0, 0);
    
    let currentTime = new Date(dayStart);
    
    while (currentTime < dayEnd) {
      const slotEnd = addMinutes(currentTime, serviceDurationMinutes);
      
      // Only add slot if the entire service duration fits within business hours
      if (slotEnd <= dayEnd) {
        slots.push({
          start: new Date(currentTime),
          end: new Date(slotEnd),
          available: true // Will be determined by constraint checking
        });
      }
      
      // Move to next slot
      currentTime = addMinutes(currentTime, slotIntervalMinutes);
    }
    
    return slots;
  }

  /**
   * Check if a time slot conflicts with any constraints
   */
  private static checkSlotConflicts(
    slot: TimeSlot,
    constraints: AvailabilityConstraints,
    dayOfWeek: number
  ): string[] {
    const conflicts: string[] = [];

    // Check if staff is unavailable on this date
    if (constraints.staffUnavailableDates.some(date => 
      isSameDay(date, slot.start)
    )) {
      conflicts.push('Staff unavailable');
      return conflicts; // No need to check further if staff is completely unavailable
    }

    // Check business breaks
    for (const businessBreak of constraints.businessBreaks) {
      if (businessBreak.daysOfWeek.includes(dayOfWeek)) {
        if (this.timeRangesOverlap(
          slot.start,
          slot.end,
          businessBreak.startTime,
          businessBreak.endTime,
          slot.start
        )) {
          conflicts.push(`Business break: ${businessBreak.name}`);
        }
      }
    }

    // Check staff breaks
    for (const staffBreak of constraints.staffBreaks) {
      if (this.timeRangesOverlap(
        slot.start,
        slot.end,
        staffBreak.startTime,
        staffBreak.endTime,
        slot.start
      )) {
        conflicts.push('Staff break');
      }
    }

    // Check existing appointments
    for (const appointment of constraints.existingAppointments) {
      if (this.dateRangesOverlap(
        slot.start,
        slot.end,
        appointment.startTime,
        appointment.endTime
      )) {
        conflicts.push('Existing appointment');
      }
    }

    return conflicts;
  }

  /**
   * Check if two time ranges overlap (using time strings and a reference date)
   */
  private static timeRangesOverlap(
    slotStart: Date,
    slotEnd: Date,
    timeStart: string,
    timeEnd: string,
    referenceDate: Date
  ): boolean {
    const [startHour, startMinute] = timeStart.split(':').map(Number);
    const [endHour, endMinute] = timeEnd.split(':').map(Number);
    
    const rangeStart = new Date(referenceDate);
    rangeStart.setHours(startHour, startMinute, 0, 0);
    
    const rangeEnd = new Date(referenceDate);
    rangeEnd.setHours(endHour, endMinute, 0, 0);
    
    return this.dateRangesOverlap(slotStart, slotEnd, rangeStart, rangeEnd);
  }

  /**
   * Check if two date ranges overlap
   */
  private static dateRangesOverlap(
    start1: Date,
    end1: Date,
    start2: Date,
    end2: Date
  ): boolean {
    return start1 < end2 && end1 > start2;
  }

  /**
   * Get the best available staff member for a specific time slot and service
   */
  static async getBestAvailableStaff(
    businessId: string,
    serviceId: string,
    startTime: Date,
    endTime: Date
  ): Promise<{ staffId: string; staffName: string; staffEmail: string } | null> {
    try {
      console.log('🔍 Finding best available staff for:', {
        businessId,
        serviceId,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString()
      });

      // Get all staff members who can perform this service
      const staffQuery = query(
        collection(db, 'staff'),
        where('businessId', '==', businessId),
        where('isActive', '!=', false)
      );

      const staffSnapshot = await getDocs(staffQuery);
      const allStaff = staffSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as StaffMember[];

      // Filter staff who can perform this service
      const availableStaff = allStaff.filter(staff =>
        staff.services?.includes(serviceId) || staff.isOwner === true
      );

      console.log(`Found ${availableStaff.length} staff members who can perform this service`);

      if (availableStaff.length === 0) {
        console.warn('No staff members available for this service');
        return null;
      }

      // Check availability for each staff member
      const staffAvailability = await Promise.all(
        availableStaff.map(async (staff) => {
          const business = await this.getBusinessData(businessId);
          if (!business) {
            return {
              staff,
              available: false,
              conflicts: ['Business not found']
            };
          }
          const constraints = await this.getConstraints(
            business,
            startTime,
            staff.id,
            businessId
          );

          const slot: TimeSlot = {
            start: startTime,
            end: endTime,
            available: true
          };

          const conflicts = this.checkSlotConflicts(
            slot,
            constraints,
            startTime.getDay()
          );

          return {
            staff,
            available: conflicts.length === 0,
            conflicts
          };
        })
      );

      // Filter to only available staff
      const availableStaffMembers = staffAvailability.filter(s => s.available);

      console.log(`${availableStaffMembers.length} staff members are available for this time slot`);

      if (availableStaffMembers.length === 0) {
        console.warn('No staff members available for this time slot');
        return null;
      }

      // Implement round-robin logic
      // For now, we'll use a simple approach based on recent appointment count
      // In a production system, you might want to store round-robin state in the database

      const staffWithAppointmentCounts = await Promise.all(
        availableStaffMembers.map(async ({ staff }) => {
          // Get recent appointment count for this staff member (last 7 days)
          const sevenDaysAgo = new Date();
          sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

          const recentAppointmentsQuery = query(
            collection(db, 'appointments'),
            where('businessId', '==', businessId),
            where('staffId', '==', staff.id),
            where('startTime', '>=', Timestamp.fromDate(sevenDaysAgo)),
            where('status', 'in', ['scheduled', 'confirmed'])
          );

          const recentAppointmentsSnapshot = await getDocs(recentAppointmentsQuery);
          const appointmentCount = recentAppointmentsSnapshot.size;

          return {
            staff,
            appointmentCount
          };
        })
      );

      // Sort by appointment count (ascending) to implement round-robin
      staffWithAppointmentCounts.sort((a, b) => a.appointmentCount - b.appointmentCount);

      const selectedStaff = staffWithAppointmentCounts[0].staff;

      console.log('✅ Selected staff member:', {
        id: selectedStaff.id,
        name: selectedStaff.name,
        email: selectedStaff.email,
        appointmentCount: staffWithAppointmentCounts[0].appointmentCount
      });

      return {
        staffId: selectedStaff.id,
        staffName: selectedStaff.name,
        staffEmail: selectedStaff.email || ''
      };

    } catch (error) {
      console.error('Error finding best available staff:', error);
      return null;
    }
  }

  /**
   * Get day name from day number
   */
  private static getDayName(dayOfWeek: number): string {
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    return days[dayOfWeek];
  }
}
