// lib/services/mailgunService.ts
import FormData from 'form-data';
import Mailgun from 'mailgun.js';

// Initialize Mailgun with proper error handling
let mailgunClient: any = null;
try {
  // Use environment variable for API key, preferring the NEXT_PUBLIC_ prefixed ones for client-side access
  let MAILGUN_API_KEY = process.env.NEXT_PUBLIC_MAILGUN_API_KEY || process.env.MAILGUN_API_KEY;
  const MAILGUN_DOMAIN = process.env.NEXT_PUBLIC_MAILGUN_DOMAIN || process.env.MAILGUN_DOMAIN;

  // Mailgun configuration initialized

  if (MAILGUN_API_KEY && MAILGUN_API_KEY !== 'API_KEY') {
    // Verify API key format
    if (!MAILGUN_API_KEY.startsWith('key-') && !MAILGUN_API_KEY.startsWith('pubkey-')) {
      console.warn('WARNING: Mailgun API key does not start with "key-" or "pubkey-". This might not be a valid Mailgun API key.');
      console.warn('For sending emails, you need a Private API key that starts with "key-".');
    }

    const mailgun = new Mailgun(FormData);
    mailgunClient = mailgun.client({
      username: 'api',
      key: MAILGUN_API_KEY,
    });

    // Log partial API key for debugging (hiding most of it for security)
    const maskedKey = MAILGUN_API_KEY.substring(0, 8) + '...' +
                     (MAILGUN_API_KEY.length > 12 ? MAILGUN_API_KEY.substring(MAILGUN_API_KEY.length - 4) : '');
    console.log(`Mailgun email service initialized with API key: ${maskedKey}`);
    console.log(`Mailgun domain: ${MAILGUN_DOMAIN}`);
  } else {
    console.warn('MAILGUN_API_KEY is not set or is using the default value');
  }
} catch (error) {
  console.error('Failed to initialize Mailgun email service:', error);
}

/**
 * Send an email using Mailgun
 * @param to Recipient email address
 * @param subject Email subject
 * @param html Email HTML content
 * @param from Sender email address (optional)
 * @param attachments Array of attachments (optional)
 * @param replyTo Reply-to email address (optional)
 * @returns A promise that resolves with the email details
 */
export const sendMailgunEmail = async (
  to: string,
  subject: string,
  html: string,
  from?: string,
  attachments?: Array<{
    filename: string;
    content: string;
    contentType?: string;
  }>,
  replyTo?: string
): Promise<{ success: boolean; id?: string; error?: any; details?: string }> => {
  if (!mailgunClient) {
    console.error('Mailgun client not initialized');
    return {
      success: false,
      error: 'Mailgun client not initialized',
      details: 'The Mailgun client was not properly initialized. Check if MAILGUN_API_KEY is set correctly in your environment variables.'
    };
  }

  try {
    const MAILGUN_FROM = process.env.NEXT_PUBLIC_MAILGUN_FROM || process.env.MAILGUN_FROM;
    const MAILGUN_DOMAIN = process.env.NEXT_PUBLIC_MAILGUN_DOMAIN || process.env.MAILGUN_DOMAIN;

    // Attempting to send email via Mailgun
    // Email configuration prepared

    // Prepare message data with optional attachments
    const messageData: any = {
      from: from || MAILGUN_FROM,
      to: [to],
      subject: subject,
      html: html
    };

    // Add reply-to if provided
    if (replyTo) {
      messageData['h:Reply-To'] = replyTo;
      console.log(`Setting reply-to: ${replyTo}`);
    }

    // Add attachments if provided
    if (attachments && attachments.length > 0) {
      console.log(`Adding ${attachments.length} attachment(s) to email`);
      messageData.attachment = attachments.map(attachment => ({
        filename: attachment.filename,
        data: Buffer.from(attachment.content, 'base64'),
        contentType: attachment.contentType || 'application/octet-stream'
      }));
    }

    // Message data prepared
    console.log('Sending email via Mailgun with the following configuration:');
    console.log(`- From: ${messageData.from}`);
    console.log(`- To: ${messageData.to}`);
    console.log(`- Subject: ${messageData.subject}`);
    console.log(`- Attachments: ${messageData.attachment ? messageData.attachment.length : 0}`);

    // Send the email
    const data = await mailgunClient.messages.create(MAILGUN_DOMAIN, messageData);

    console.log('Email sent successfully with Mailgun:', data);
    return { success: true, id: data.id };
  } catch (error: any) {
    console.error('Error sending email with Mailgun:', error);

    // Log detailed error information
    console.error('Detailed error information:');
    console.error('Status:', error.status);
    console.error('Message:', error.message);
    console.error('Details:', error.details);
    console.error('Stack:', error.stack);

    if (error.response) {
      console.error('Response body:', error.response.body);
      console.error('Response status:', error.response.status);
    }

    let details = 'An unknown error occurred while sending the email.';

    // Provide more specific error details based on the error type
    if (error.status === 401) {
      details = 'Authentication failed. Check if your Mailgun API key is correct and has the necessary permissions.';
    } else if (error.status === 400) {
      details = 'Bad request. Check if the recipient email, sender email, and domain are valid.';
    } else if (error.status === 403) {
      details = 'Forbidden. This is likely a sandbox domain restriction. For sandbox domains, you can only send emails to authorized recipients. Either upgrade your Mailgun account or add the recipient email to your authorized recipients list in the Mailgun dashboard.';
    } else if (error.status === 404) {
      details = 'Not found. The domain might not exist or is not properly configured in your Mailgun account.';
    } else if (error.message) {
      details = error.message;
    }

    console.error('Error details:', details);

    return {
      success: false,
      error,
      details
    };
  }
};

export default {
  sendMailgunEmail,
};
