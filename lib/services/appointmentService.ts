// lib/services/appointmentService.ts
import { collection, doc, getDoc, getDocs, addDoc, updateDoc, query, where, Timestamp, orderBy, limit, DocumentReference, DocumentData } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Appointment, AppointmentData } from '@/lib/types/db';
import { getGoogleCalendarSettings, createGoogleCalendarEvent } from './googleCalendarService';
import { getNextAvailableStaffMember } from './roundRobinService';
import { getStaff } from './staffService';
import { findOrCreateClient } from './clientService';

/**
 * Create a new appointment
 * @param appointmentData The appointment data
 * @returns The ID of the created appointment
 */
export async function createAppointment(appointmentData: AppointmentData): Promise<string> {
  console.log('Creating appointment with data:', appointmentData);

  // Validate required fields
  if (!appointmentData.businessId) {
    throw new Error('Business ID is required');
  }

  if (!appointmentData.serviceId && !appointmentData.serviceName) {
    throw new Error('Service ID or name is required');
  }

  if (!appointmentData.startTime || !appointmentData.endTime) {
    throw new Error('Start and end times are required');
  }

  if (!appointmentData.clientName || !appointmentData.clientEmail) {
    throw new Error('Client name and email are required');
  }

  // Create a copy of the appointment data to avoid modifying the original
  const appointmentDataCopy = { ...appointmentData };

  // Ensure dates are Firestore Timestamps
  if (appointmentDataCopy.startTime instanceof Date) {
    appointmentDataCopy.startTime = Timestamp.fromDate(appointmentDataCopy.startTime);
  }

  if (appointmentDataCopy.endTime instanceof Date) {
    appointmentDataCopy.endTime = Timestamp.fromDate(appointmentDataCopy.endTime);
  }

  if (appointmentDataCopy.createdAt instanceof Date) {
    appointmentDataCopy.createdAt = Timestamp.fromDate(appointmentDataCopy.createdAt);
  } else if (!appointmentDataCopy.createdAt) {
    appointmentDataCopy.createdAt = Timestamp.now();
  }

  // Set default status if not provided
  if (!appointmentDataCopy.status) {
    appointmentDataCopy.status = 'confirmed';
  }

  // If useRoundRobin is true, assign a staff member using round-robin scheduling
  if (appointmentDataCopy.useRoundRobin && !appointmentDataCopy.staffId) {
    try {
      console.log('Using round-robin scheduling to assign staff member');
      const staffId = await assignStaffMemberRoundRobin(
        appointmentDataCopy.businessId,
        appointmentDataCopy.serviceId,
        appointmentDataCopy.startTime.toDate(),
        appointmentDataCopy.endTime.toDate()
      );

      if (staffId) {
        console.log('Assigned staff member:', staffId);
        appointmentDataCopy.staffId = staffId;
      } else {
        console.warn('No staff member assigned by round-robin scheduling');
      }
    } catch (roundRobinError) {
      console.error('Error using round-robin scheduling:', roundRobinError);

      // Fall back to simpler method if the improved service fails
      console.log('Falling back to basic staff assignment');

      // 1. Get all staff members who can perform this service
      const allStaff = await getStaff(appointmentDataCopy.businessId);
      console.log(`Found ${allStaff.length} total staff members for business ${appointmentDataCopy.businessId}`);

      // If no staff members can perform this service, use any active staff member
      if (!allStaff.some(staff => staff.services.includes(appointmentDataCopy.serviceId) && staff.isActive !== false)) {
        console.warn('No staff members can perform this service. Using any active staff member as fallback.');

        // Try to find the business owner first
        const businessOwner = allStaff.find(staff => staff.isOwner === true && staff.isActive !== false);
        if (businessOwner) {
          console.warn(`Using business owner as fallback staff member: ${businessOwner.name} (${businessOwner.id})`);
          appointmentDataCopy.staffId = businessOwner.id;
        } else {
          // If no owner, use any active staff member
          const anyActiveStaff = allStaff.find(staff => staff.isActive !== false);
          if (anyActiveStaff) {
            console.warn(`Using fallback staff member: ${anyActiveStaff.name} (${anyActiveStaff.id})`);
            appointmentDataCopy.staffId = anyActiveStaff.id;
          }
        }
      }

      // Check if business owner is in staff list and can perform this service
      const businessOwnerAsStaff = allStaff.find(staff =>
        staff.isOwner === true &&
        staff.services.includes(appointmentDataCopy.serviceId) &&
        staff.isActive !== false
      );

      if (businessOwnerAsStaff) {
        console.log('Using business owner as staff member');
        appointmentDataCopy.staffId = businessOwnerAsStaff.id;
      } else {
        // Find the first available staff member who can perform this service
        const availableStaff = allStaff.find(staff =>
          staff.services.includes(appointmentDataCopy.serviceId) &&
          staff.isActive !== false
        );

        if (availableStaff) {
          console.log(`Using staff member: ${availableStaff.name}`);
          appointmentDataCopy.staffId = availableStaff.id;
        } else {
          console.warn('No staff member found who can perform this service');
        }
      }
    }
  }

  try {
    // Find or create the client automatically
    let clientId: string | undefined;
    try {
      console.log('Attempting to find or create client with data:', {
        businessId: appointmentDataCopy.businessId,
        name: appointmentDataCopy.clientName,
        email: appointmentDataCopy.clientEmail,
        phone: appointmentDataCopy.clientPhone || '',
        notes: appointmentDataCopy.notes || ''
      });

      clientId = await findOrCreateClient(appointmentDataCopy.businessId, {
        name: appointmentDataCopy.clientName,
        email: appointmentDataCopy.clientEmail,
        phone: appointmentDataCopy.clientPhone || '',
        notes: appointmentDataCopy.notes || ''
      });

      // Add the client ID to the appointment data
      appointmentDataCopy.clientId = clientId;
      console.log('✅ Client found/created successfully with ID:', clientId);
    } catch (clientError) {
      console.error('❌ Error finding/creating client:', clientError);
      // Continue without client ID - appointment can still be created
    }

    // Create the appointment in Firestore
    const appointmentsRef = collection(db, 'appointments');
    const appointmentRef = await addDoc(appointmentsRef, appointmentDataCopy);
    console.log('Appointment created with ID:', appointmentRef.id);

    // Extract the data without the circular reference
    const { useRoundRobin, skipCalendarIntegration, ...data } = appointmentDataCopy;

    // Send email notifications regardless of Google Calendar integration
    try {
      console.log('Sending appointment confirmation emails...');
      const { sendAppointmentConfirmationEmail } = await import('./emailService');

      // Get the business timezone and name
      const businessRef = doc(db, 'businesses', appointmentDataCopy.businessId);
      const businessDoc = await getDoc(businessRef);
      const businessData = businessDoc.exists() ? businessDoc.data() : null;
      const timeZone = businessData?.timeZone || 'America/New_York';
      const businessName = businessData?.name || 'Business';

      // If a staff member is assigned, get their email
      let staffEmail = null;
      let staffName = null;

      if (data.staffId) {
        try {
          const staffRef = doc(db, 'staff', data.staffId);
          const staffDoc = await getDoc(staffRef);

          if (staffDoc.exists()) {
            const staffData = staffDoc.data();
            staffEmail = staffData.email;
            staffName = staffData.name;
            console.log(`Found staff email for ${staffName}: ${staffEmail}`);
          } else {
            console.warn(`Staff member with ID ${data.staffId} not found`);
          }
        } catch (staffError) {
          console.error('Error getting staff email:', staffError);
        }
      }

      // Create a complete appointment object with all necessary data
      const completeAppointment = {
        id: appointmentRef.id,
        ...data,
        staffEmail,
        staffName
      } as Appointment;

      // Send confirmation email with the appointment data to all parties
      await sendAppointmentConfirmationEmail(
        completeAppointment,
        businessName,
        timeZone,
        true // Send to all parties (client, business owner, and staff)
      );
      console.log('Appointment confirmation emails sent successfully');
    } catch (emailError) {
      console.error('Error sending appointment confirmation emails:', emailError);
      // Don't throw the error, just log it - appointment is still created
    }

    // Skip Google Calendar integration if requested
    if (skipCalendarIntegration) {
      console.log('Skipping Google Calendar integration as requested');
      return appointmentRef.id;
    }

    // Get Google Calendar settings
    console.log('Checking Google Calendar settings for business:', appointmentDataCopy.businessId);
    const settings = await getGoogleCalendarSettings(appointmentDataCopy.businessId);
    console.log('Google Calendar settings:', settings);

    if (settings?.enabled && settings.tokens) {
      console.log('Google Calendar is enabled, creating event...');

      try {
        // Create the event in Google Calendar
        const eventId = await createGoogleCalendarEvent(
          {
            ...data,
            id: appointmentRef.id
          } as Appointment,
          appointmentDataCopy.businessId
        );

        if (eventId) {
          console.log('Successfully created Google Calendar event:', eventId);
          // Update the appointment with Google Calendar event ID
          await updateDoc(appointmentRef, {
            googleCalendarEventId: eventId
          });
          console.log('Updated appointment with Google Calendar event ID');
        } else {
          console.log('No event ID returned from Google Calendar');
        }
      } catch (calendarError) {
        console.error('Error creating Google Calendar event:', calendarError);
        // If it's an API not enabled error, update the business settings
        if (calendarError instanceof Error &&
            calendarError.message?.includes('Google Calendar API is not enabled')) {
          await updateDoc(doc(db, 'businesses', appointmentDataCopy.businessId), {
            'googleCalendar.enabled': false
          });
          console.log('Disabled Google Calendar integration due to API not being enabled');
        }
        // If it's a token expired error, mark it for the user to reconnect
        else if (calendarError instanceof Error &&
                (calendarError.message?.includes('expired') ||
                 calendarError.message?.includes('Token has been expired'))) {
          console.error('Google Calendar token expired. User needs to reconnect.');
          // Update the appointment to indicate Google Calendar failed but appointment was created
          await updateDoc(appointmentRef, {
            googleCalendarError: 'Token expired. Please reconnect Google Calendar.'
          });
        }
        // Don't throw the error, just log it - appointment is still created
      }
    } else {
      console.log('Google Calendar is not enabled for this business');
      if (!settings?.enabled) console.log('Reason: settings.enabled is false');
      if (!settings?.tokens) console.log('Reason: settings.tokens is missing');
    }

    return appointmentRef.id;
  } catch (error) {
    console.error('Error creating appointment:', error);
    throw error;
  }
}

/**
 * Get all appointments for a business
 * @param businessId The business ID
 * @returns A promise that resolves with an array of appointments
 */
export async function getBusinessAppointments(businessId: string): Promise<Appointment[]> {
  try {
    const appointmentsRef = collection(db, 'appointments');
    const q = query(
      appointmentsRef,
      where('businessId', '==', businessId),
      orderBy('startTime', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const appointments: Appointment[] = [];

    querySnapshot.forEach(doc => {
      appointments.push({
        id: doc.id,
        ...doc.data()
      } as Appointment);
    });

    return appointments;
  } catch (error) {
    console.error('Error getting business appointments:', error);
    throw error;
  }
}

/**
 * Get all appointments for a business (alias for getBusinessAppointments for backward compatibility)
 * @param businessId The business ID
 * @returns A promise that resolves with an array of appointments
 */
export async function getAppointments(businessId: string): Promise<Appointment[]> {
  // Call getBusinessAppointments to avoid code duplication
  return getBusinessAppointments(businessId);
}

/**
 * Get all appointments for a specific day
 * @param businessId The business ID
 * @param date The date to get appointments for
 * @returns A promise that resolves with an array of appointments
 */
export async function getAppointmentsForDay(businessId: string, date: Date): Promise<Appointment[]> {
  try {
    // Create start and end timestamps for the day
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const startTimestamp = Timestamp.fromDate(startOfDay);
    const endTimestamp = Timestamp.fromDate(endOfDay);

    const appointmentsRef = collection(db, 'appointments');
    const q = query(
      appointmentsRef,
      where('businessId', '==', businessId),
      where('startTime', '>=', startTimestamp),
      where('startTime', '<=', endTimestamp),
      orderBy('startTime', 'asc')
    );

    const querySnapshot = await getDocs(q);
    const appointments: Appointment[] = [];

    querySnapshot.forEach(doc => {
      appointments.push({
        id: doc.id,
        ...doc.data()
      } as Appointment);
    });

    return appointments;
  } catch (error) {
    console.error('Error getting appointments for day:', error);
    throw error;
  }
}

/**
 * Get all appointments for a specific staff member
 * @param businessId The business ID
 * @param staffId The staff ID
 * @returns A promise that resolves with an array of appointments
 */
export async function getStaffAppointments(businessId: string, staffId: string): Promise<Appointment[]> {
  try {
    const appointmentsRef = collection(db, 'appointments');
    const q = query(
      appointmentsRef,
      where('businessId', '==', businessId),
      where('staffId', '==', staffId),
      orderBy('startTime', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const appointments: Appointment[] = [];

    querySnapshot.forEach(doc => {
      appointments.push({
        id: doc.id,
        ...doc.data()
      } as Appointment);
    });

    return appointments;
  } catch (error) {
    console.error('Error getting staff appointments:', error);
    throw error;
  }
}

/**
 * Get all appointments for a specific client
 * @param businessId The business ID
 * @param clientEmail The client email
 * @returns A promise that resolves with an array of appointments
 */
export async function getClientAppointments(businessId: string, clientEmail: string): Promise<Appointment[]> {
  try {
    const appointmentsRef = collection(db, 'appointments');
    const q = query(
      appointmentsRef,
      where('businessId', '==', businessId),
      where('clientEmail', '==', clientEmail),
      orderBy('startTime', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const appointments: Appointment[] = [];

    querySnapshot.forEach(doc => {
      appointments.push({
        id: doc.id,
        ...doc.data()
      } as Appointment);
    });

    return appointments;
  } catch (error) {
    console.error('Error getting client appointments:', error);
    throw error;
  }
}

/**
 * Get future recurring appointments for a specific staff member that would occur on a given date
 * @param businessId The business ID
 * @param staffId The staff ID
 * @param date The date to check for recurring appointments
 * @returns A promise that resolves with an array of appointments
 */
export async function getFutureRecurringAppointmentsForStaff(
  businessId: string,
  staffId: string,
  date: Date
): Promise<Appointment[]> {
  try {
    // For now, we'll return an empty array since recurring appointments are not yet implemented
    // This is a placeholder for future implementation
    console.log(`Checking recurring appointments for staff ${staffId} on ${date.toDateString()}`);
    return [];

    // Future implementation would look something like this:
    // 1. Get all recurring appointments for this staff member
    // 2. Filter to only include those that would occur on the given date
    // 3. Return the filtered appointments
  } catch (error) {
    console.error('Error getting future recurring appointments for staff:', error);
    return [];
  }
}

/**
 * Get the next appointment for a specific client
 * @param businessId The business ID
 * @param clientEmail The client email
 * @returns A promise that resolves with the next appointment or null if none exists
 */
export async function getNextClientAppointment(businessId: string, clientEmail: string): Promise<Appointment | null> {
  try {
    const now = new Date();
    const appointmentsRef = collection(db, 'appointments');
    const q = query(
      appointmentsRef,
      where('businessId', '==', businessId),
      where('clientEmail', '==', clientEmail),
      where('startTime', '>=', Timestamp.fromDate(now)),
      orderBy('startTime', 'asc'),
      limit(1)
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    return {
      id: doc.id,
      ...doc.data()
    } as Appointment;
  } catch (error) {
    console.error('Error getting next client appointment:', error);
    throw error;
  }
}

/**
 * Assign a staff member to an appointment using round-robin scheduling
 * @param businessId The business ID
 * @param serviceId The service ID
 * @param startTime The appointment start time
 * @param endTime The appointment end time
 * @returns A promise that resolves with the staff ID
 */
/**
 * Update an appointment's status
 * @param appointmentId The appointment ID
 * @param status The new status
 * @param reason Optional reason for status change (for cancellations or no-shows)
 * @param updatePaymentStatus Whether to automatically update payment status when marking as completed
 * @returns A promise that resolves when the update is complete
 */
export async function updateAppointmentStatus(
  appointmentId: string,
  status: string,
  reason?: string,
  updatePaymentStatus: boolean = false
): Promise<void> {
  try {
    console.log(`Updating appointment ${appointmentId} status to ${status}`);

    // Get the appointment to check current status and get business ID
    const appointmentRef = doc(db, 'appointments', appointmentId);
    const appointmentDoc = await getDoc(appointmentRef);

    if (!appointmentDoc.exists()) {
      throw new Error(`Appointment ${appointmentId} not found`);
    }

    const appointmentData = appointmentDoc.data() as Appointment;
    const businessId = appointmentData.businessId;

    // Prepare update data
    const updateData: Record<string, any> = {
      status,
      updatedAt: Timestamp.now()
    };

    // Add reason if provided (for cancellations or no-shows)
    if (reason) {
      updateData.statusReason = reason;
    }

    // If marking as completed and updatePaymentStatus is true, also update payment status
    if (status === 'completed' && updatePaymentStatus) {
      updateData.paymentStatus = 'paid';

      // If the appointment has a price, record it as revenue
      if (appointmentData.price) {
        updateData.revenue = appointmentData.price;
      }

      console.log(`Also updating payment status to paid and recording revenue: ${appointmentData.price || 0}`);
    }

    // Update the appointment
    await updateDoc(appointmentRef, updateData);
    console.log(`Appointment ${appointmentId} status updated to ${status}`);

    // If the appointment is completed, update staff metrics
    if (status === 'completed' && appointmentData.staffId) {
      try {
        // Import the staff metrics service dynamically to avoid circular dependencies
        const { updateStaffMetrics } = await import('./staffMetricsService');
        await updateStaffMetrics(appointmentData.staffId);
        console.log(`Updated metrics for staff ${appointmentData.staffId}`);
      } catch (metricsError) {
        console.error('Error updating staff metrics:', metricsError);
        // Don't throw the error, just log it
      }
    }
  } catch (error) {
    console.error('Error updating appointment status:', error);
    throw error;
  }
}

/**
 * Update an appointment's payment status
 * @param appointmentId The appointment ID
 * @param paymentStatus The new payment status
 * @returns A promise that resolves when the update is complete
 */
export async function updateAppointmentPaymentStatus(
  appointmentId: string,
  paymentStatus: 'paid' | 'unpaid' | 'partial' | 'refunded'
): Promise<void> {
  try {
    console.log(`Updating appointment ${appointmentId} payment status to ${paymentStatus}`);

    // Get the appointment to check current status and get business ID
    const appointmentRef = doc(db, 'appointments', appointmentId);
    const appointmentDoc = await getDoc(appointmentRef);

    if (!appointmentDoc.exists()) {
      throw new Error(`Appointment ${appointmentId} not found`);
    }

    const appointmentData = appointmentDoc.data() as Appointment;

    // Prepare update data
    const updateData: Record<string, any> = {
      paymentStatus,
      updatedAt: Timestamp.now()
    };

    // If marking as paid and the appointment has a price, record it as revenue
    if (paymentStatus === 'paid' && appointmentData.price) {
      updateData.revenue = appointmentData.price;
    }

    // Update the appointment
    await updateDoc(appointmentRef, updateData);
    console.log(`Appointment ${appointmentId} payment status updated to ${paymentStatus}`);

    // If the appointment has a staff member, update their metrics
    if (appointmentData.staffId) {
      try {
        // Import the staff metrics service dynamically to avoid circular dependencies
        const { updateStaffMetrics } = await import('./staffMetricsService');
        await updateStaffMetrics(appointmentData.staffId);
        console.log(`Updated metrics for staff ${appointmentData.staffId}`);
      } catch (metricsError) {
        console.error('Error updating staff metrics:', metricsError);
        // Don't throw the error, just log it
      }
    }
  } catch (error) {
    console.error('Error updating appointment payment status:', error);
    throw error;
  }
}

export async function assignStaffMemberRoundRobin(
  businessId: string,
  serviceId: string,
  startTime: Date,
  endTime: Date
): Promise<string> {
  try {
    console.log('Using improved round-robin service');
    console.log(`Assigning staff for business ${businessId}, service ${serviceId}`);
    console.log(`Appointment time: ${startTime.toLocaleString()} - ${endTime.toLocaleString()}`);

    const staffAssignment = await getNextAvailableStaffMember(businessId, serviceId, startTime, startTime, endTime);
    console.log(`Round-robin assigned staff: ${staffAssignment.name} (${staffAssignment.id})`);
    return staffAssignment.id;
  } catch (error) {
    console.error('Error using improved round-robin service:', error);

    // Fallback: Get any staff member for this business
    try {
      console.warn('Attempting to find any available staff member as fallback...');

      // Get all active staff members for this business
      const staffQuery = query(
        collection(db, 'staff'),
        where('businessId', '==', businessId),
        where('isActive', '==', true)
      );

      const staffSnapshot = await getDocs(staffQuery);
      console.log(`Found ${staffSnapshot.size} active staff members for business ${businessId}`);

      if (!staffSnapshot.empty) {
        // Try to find the business owner first
        const businessOwner = staffSnapshot.docs.find(doc => {
          const staffData = doc.data();
          return staffData.isOwner === true;
        });

        if (businessOwner) {
          const ownerData = businessOwner.data();
          console.warn(`Using business owner as fallback staff member: ${ownerData.name} (${businessOwner.id})`);
          return businessOwner.id;
        }

        // If no owner, get the first staff member as a fallback
        const firstStaff = staffSnapshot.docs[0];
        const staffData = firstStaff.data();
        console.warn(`Using fallback staff member: ${staffData.name} (${firstStaff.id})`);
        return firstStaff.id;
      }
    } catch (fallbackError) {
      console.error('Error finding fallback staff member:', fallbackError);
    }

    // If we get here, we couldn't find any staff member
    throw new Error('No staff members available for this appointment');
  }
}

/**
 * Reschedule an appointment to a new date and time
 * @param appointmentId The appointment ID
 * @param newStartTime The new start time
 * @param newEndTime The new end time
 * @param updateGoogleCalendar Whether to update Google Calendar
 * @param googleAccessToken Optional Google access token
 * @param businessTimezone Optional business timezone
 * @returns A promise that resolves when the reschedule is complete
 */
export async function rescheduleAppointment(
  appointmentId: string,
  newStartTime: Date,
  newEndTime: Date,
  updateGoogleCalendar: boolean = true,
  googleAccessToken?: string,
  businessTimezone?: string
): Promise<void> {
  try {
    console.log(`Rescheduling appointment ${appointmentId} to ${newStartTime.toLocaleString()} - ${newEndTime.toLocaleString()}`);

    // Get the appointment to check current details
    const appointmentRef = doc(db, 'appointments', appointmentId);
    const appointmentDoc = await getDoc(appointmentRef);

    if (!appointmentDoc.exists()) {
      throw new Error(`Appointment ${appointmentId} not found`);
    }

    const appointmentData = appointmentDoc.data() as Appointment;

    // Prepare update data
    const updateData: Record<string, any> = {
      startTime: Timestamp.fromDate(newStartTime),
      endTime: Timestamp.fromDate(newEndTime),
      updatedAt: Timestamp.now()
    };

    // Update the appointment in Firestore
    await updateDoc(appointmentRef, updateData);
    console.log(`Appointment ${appointmentId} rescheduled successfully`);

    // Update Google Calendar if requested and event exists
    if (updateGoogleCalendar && appointmentData.googleCalendarEventId) {
      try {
        // Import Google Calendar service dynamically to avoid circular dependencies
        const { updateGoogleCalendarEvent } = await import('./googleCalendarService');

        await updateGoogleCalendarEvent(
          appointmentData.googleCalendarEventId,
          {
            ...appointmentData,
            startTime: Timestamp.fromDate(newStartTime),
            endTime: Timestamp.fromDate(newEndTime)
          } as Appointment,
          appointmentData.businessId,
          googleAccessToken
        );

        console.log('Updated Google Calendar event successfully');
      } catch (calendarError) {
        console.error('Error updating Google Calendar event:', calendarError);
        // Don't throw the error, just log it - appointment is still rescheduled
      }
    }

    // Send reschedule notification emails
    try {
      // Import email service dynamically to avoid circular dependencies
      const { sendAppointmentRescheduledEmail } = await import('./emailService');

      await sendAppointmentRescheduledEmail({
        ...appointmentData,
        startTime: Timestamp.fromDate(newStartTime),
        endTime: Timestamp.fromDate(newEndTime)
      } as Appointment);

      console.log('Sent reschedule notification emails');
    } catch (emailError) {
      console.error('Error sending reschedule notification emails:', emailError);
      // Don't throw the error, just log it - appointment is still rescheduled
    }

  } catch (error) {
    console.error('Error rescheduling appointment:', error);
    throw error;
  }
}
