import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Review, ReviewRequest, ReviewSubmission, ReviewResponse, ReviewStatusUpdate, ReviewFeatureUpdate } from '@/lib/types/reviews';
import { v4 as uuidv4 } from 'uuid';
import { getAppointmentById } from './appointmentService';
import { getClientById } from './clientService';
import { getServiceById } from './serviceService';
import { getStaffById } from './staffService';

// Collection names
const reviewsCollection = 'reviews';
const reviewRequestsCollection = 'reviewRequests';

/**
 * Create a new review request
 * @param businessId Business ID
 * @param clientId Client ID
 * @param appointmentId Appointment ID
 * @returns The created review request
 */
export const createReviewRequest = async (
  businessId: string,
  clientId: string,
  appointmentId: string
): Promise<ReviewRequest> => {
  try {
    // IMPORTANT DEBUG LOG - Check if this appears in the console
    console.log('🟢 CREATING REVIEW REQUEST:', { businessId, clientId, appointmentId });
    // Check if a request already exists for this appointment
    const existingRequests = await getDocs(
      query(
        collection(db, reviewRequestsCollection),
        where('appointmentId', '==', appointmentId)
      )
    );

    if (!existingRequests.empty) {
      // Return the existing request
      const existingRequest = existingRequests.docs[0].data() as ReviewRequest;
      return {
        ...existingRequest,
        id: existingRequests.docs[0].id
      };
    }

    // Check if the client already has a review for this business in the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const existingReviews = await getDocs(
      query(
        collection(db, reviewsCollection),
        where('businessId', '==', businessId),
        where('clientId', '==', clientId),
        where('createdAt', '>=', Timestamp.fromDate(sixMonthsAgo))
      )
    );

    if (!existingReviews.empty) {
      throw new Error('Client has already submitted a review in the last 6 months');
    }

    // Generate a unique token
    const token = uuidv4();

    // Create expiration date (30 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    // Create the review request
    const reviewRequest: Omit<ReviewRequest, 'id'> = {
      businessId,
      clientId,
      appointmentId,
      token,
      sentAt: Timestamp.now(),
      status: 'sent',
      expiresAt: Timestamp.fromDate(expiresAt)
    };

    // Add to Firestore
    const docRef = await addDoc(collection(db, reviewRequestsCollection), reviewRequest);

    return {
      ...reviewRequest,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error creating review request:', error);
    throw error;
  }
};

/**
 * Get a review request by token
 * @param token The unique token
 * @returns The review request or null if not found
 */
export const getReviewRequestByToken = async (token: string): Promise<ReviewRequest | null> => {
  try {
    const reviewRequests = await getDocs(
      query(
        collection(db, reviewRequestsCollection),
        where('token', '==', token)
      )
    );

    if (reviewRequests.empty) {
      return null;
    }

    const reviewRequest = reviewRequests.docs[0].data() as ReviewRequest;

    // Check if the request has expired
    if (reviewRequest.expiresAt.toDate() < new Date()) {
      await updateDoc(doc(db, reviewRequestsCollection, reviewRequests.docs[0].id), {
        status: 'expired'
      });
      return null;
    }

    // Update status to clicked if it was sent
    if (reviewRequest.status === 'sent') {
      await updateDoc(doc(db, reviewRequestsCollection, reviewRequests.docs[0].id), {
        status: 'clicked'
      });
    }

    return {
      ...reviewRequest,
      id: reviewRequests.docs[0].id
    };
  } catch (error) {
    console.error('Error getting review request by token:', error);
    throw error;
  }
};

/**
 * Submit a review
 * @param token The review request token
 * @param reviewData The review data
 * @returns The created review
 */
export const submitReview = async (
  token: string,
  reviewData: ReviewSubmission
): Promise<Review> => {
  try {
    // Get the review request
    const reviewRequest = await getReviewRequestByToken(token);

    if (!reviewRequest) {
      throw new Error('Invalid or expired review token');
    }

    if (reviewRequest.status === 'completed') {
      throw new Error('Review has already been submitted for this appointment');
    }

    // Get appointment details
    const appointment = await getAppointmentById(reviewRequest.appointmentId);

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    // Create the review with only defined fields
    const review: Omit<Review, 'id'> = {
      businessId: reviewRequest.businessId,
      clientId: reviewRequest.clientId,
      appointmentId: reviewRequest.appointmentId,
      rating: reviewData.rating,
      comment: reviewData.comment,
      serviceId: appointment.serviceId,
      staffId: appointment.staffId || '',
      createdAt: Timestamp.now(),
      status: 'pending', // Requires business approval
      featured: false,
      clientName: reviewData.clientName
    };

    // Only add optional fields if they are defined
    if (reviewData.clientEmail) {
      review.clientEmail = reviewData.clientEmail;
    }

    if (reviewData.clientPhotoUrl) {
      review.clientPhotoUrl = reviewData.clientPhotoUrl;
    }

    // Add to Firestore
    const docRef = await addDoc(collection(db, reviewsCollection), review);

    // Update review request status
    await updateDoc(doc(db, reviewRequestsCollection, reviewRequest.id), {
      status: 'completed'
    });

    return {
      ...review,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error submitting review:', error);
    throw error;
  }
};

/**
 * Get reviews for a business
 * @param businessId The business ID
 * @param status Optional status filter
 * @param maxReviews Optional limit on number of reviews
 * @returns Array of reviews
 */
export const getBusinessReviews = async (
  businessId: string,
  status?: 'pending' | 'approved' | 'rejected',
  maxReviews?: number,
  serviceId?: string
): Promise<Review[]> => {
  try {
    let reviewsQuery = query(
      collection(db, reviewsCollection),
      where('businessId', '==', businessId),
      orderBy('createdAt', 'desc')
    );

    if (status) {
      reviewsQuery = query(
        reviewsQuery,
        where('status', '==', status)
      );
    }

    if (serviceId) {
      reviewsQuery = query(
        reviewsQuery,
        where('serviceId', '==', serviceId)
      );
    }

    if (maxReviews) {
      reviewsQuery = query(
        reviewsQuery,
        limit(maxReviews)
      );
    }

    const reviewsSnapshot = await getDocs(reviewsQuery);

    return reviewsSnapshot.docs.map(doc => ({
      ...doc.data(),
      id: doc.id
    } as Review));
  } catch (error) {
    console.error('Error getting business reviews:', error);
    throw error;
  }
};

/**
 * Get featured reviews for a business
 * @param businessId The business ID
 * @param maxReviews Optional limit on number of reviews
 * @returns Array of featured reviews
 */
export const getFeaturedReviews = async (
  businessId: string,
  maxReviews: number = 5,
  serviceId?: string
): Promise<Review[]> => {
  try {
    // Create base query
    let baseQuery = query(
      collection(db, reviewsCollection),
      where('businessId', '==', businessId),
      where('status', '==', 'approved')
    );

    // Add serviceId filter if provided
    if (serviceId) {
      baseQuery = query(
        baseQuery,
        where('serviceId', '==', serviceId)
      );
    }

    // Get featured reviews first
    const reviewsQuery = query(
      baseQuery,
      where('featured', '==', true),
      orderBy('createdAt', 'desc'),
      limit(maxReviews)
    );

    const reviewsSnapshot = await getDocs(reviewsQuery);

    const featuredReviews = reviewsSnapshot.docs.map(doc => ({
      ...doc.data(),
      id: doc.id
    } as Review));

    // Only return featured reviews
    // We no longer show non-featured approved reviews
    // Since all approved reviews are automatically featured, this should be fine

    return featuredReviews;
  } catch (error) {
    console.error('Error getting featured reviews:', error);
    throw error;
  }
};

/**
 * Update review status
 * @param reviewId The review ID
 * @param statusUpdate The status update
 * @returns Promise that resolves when the update is complete
 */
export const updateReviewStatus = async (
  reviewId: string,
  statusUpdate: ReviewStatusUpdate
): Promise<void> => {
  try {
    await updateDoc(doc(db, reviewsCollection, reviewId), {
      status: statusUpdate.status,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating review status:', error);
    throw error;
  }
};

/**
 * Update review featured status
 * @param reviewId The review ID
 * @param featureUpdate The feature update
 * @returns Promise that resolves when the update is complete
 */
export const updateReviewFeature = async (
  reviewId: string,
  featureUpdate: ReviewFeatureUpdate
): Promise<void> => {
  try {
    await updateDoc(doc(db, reviewsCollection, reviewId), {
      featured: featureUpdate.featured,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error updating review feature status:', error);
    throw error;
  }
};

/**
 * Add business response to a review
 * @param reviewId The review ID
 * @param response The business response
 * @returns Promise that resolves when the update is complete
 */
export const addBusinessResponse = async (
  reviewId: string,
  response: ReviewResponse
): Promise<void> => {
  try {
    await updateDoc(doc(db, reviewsCollection, reviewId), {
      businessResponse: response.businessResponse,
      businessResponseDate: response.businessResponseDate,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error adding business response:', error);
    throw error;
  }
};

/**
 * Get review statistics for a business
 * @param businessId The business ID
 * @returns Review statistics
 */
export const getReviewStatistics = async (
  businessId: string,
  serviceId?: string
): Promise<{
  averageRating: number;
  totalReviews: number;
  ratingCounts: Record<number, number>;
}> => {
  try {
    const reviews = await getBusinessReviews(businessId, 'approved', undefined, serviceId);

    if (reviews.length === 0) {
      return {
        averageRating: 0,
        totalReviews: 0,
        ratingCounts: {
          1: 0,
          2: 0,
          3: 0,
          4: 0,
          5: 0
        }
      };
    }

    // Calculate average rating
    const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
    const averageRating = sum / reviews.length;

    // Count ratings
    const ratingCounts = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0
    };

    reviews.forEach(review => {
      ratingCounts[review.rating as 1 | 2 | 3 | 4 | 5]++;
    });

    return {
      averageRating,
      totalReviews: reviews.length,
      ratingCounts
    };
  } catch (error) {
    console.error('Error getting review statistics:', error);
    throw error;
  }
};

/**
 * Check if a client is eligible for a review request
 * @param businessId The business ID
 * @param clientId The client ID
 * @returns Whether the client is eligible
 */
export const isClientEligibleForReview = async (
  businessId: string,
  clientId: string
): Promise<boolean> => {
  try {
    // Log the check for debugging
    console.log('Checking client eligibility for review:', { businessId, clientId });

    // Get client details to check if they're a new client
    try {
      // Import the client service dynamically to avoid circular dependencies
      const { getClientById } = await import('./clientService');
      const client = await getClientById(clientId);

      // If this is a new client (first appointment), always allow a review
      if (client && (client.totalAppointments ?? 0) <= 1) {
        console.log('New client with first appointment - eligible for review');
        return true;
      }

      console.log('Client appointment history:', {
        clientId,
        totalAppointments: client?.totalAppointments || 0,
        lastAppointmentDate: client?.lastAppointment ? client.lastAppointment.toDate().toISOString() : 'none'
      });
    } catch (clientError) {
      console.error('Error getting client details for eligibility check:', clientError);
      // Continue with standard eligibility check
    }

    // Check if the client has submitted a review in the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const existingReviews = await getDocs(
      query(
        collection(db, reviewsCollection),
        where('businessId', '==', businessId),
        where('clientId', '==', clientId),
        where('createdAt', '>=', Timestamp.fromDate(sixMonthsAgo))
      )
    );

    const isEligible = existingReviews.empty;
    console.log(`Client has ${existingReviews.size} reviews in the last 6 months - eligible: ${isEligible}`);

    return isEligible;
  } catch (error) {
    console.error('Error checking client eligibility for review:', error);
    // Default to eligible in case of error to ensure new clients get review requests
    return true;
  }
};
