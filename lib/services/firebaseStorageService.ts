import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { app } from '@/lib/firebase/config';
import { generateICSContent, generateICSFileName, createICSBlob } from './icsService';
import { Appointment } from '@/lib/types/db';

// Initialize Firebase Storage
const storage = getStorage(app);

/**
 * Uploads an ICS file to Firebase Storage
 * @param appointment The appointment data
 * @param businessName The name of the business
 * @param businessAddress Optional business address
 * @returns Promise that resolves to the download URL
 */
export async function uploadICSFile(
  appointment: Appointment,
  businessName: string,
  businessAddress?: string
): Promise<string> {
  try {
    // Generate ICS content
    const icsContent = generateICSContent(appointment, businessName, businessAddress);
    const fileName = generateICSFileName(appointment, businessName);
    
    // Create a blob from the ICS content
    const blob = createICSBlob(icsContent);
    
    // Create a reference to the file in Firebase Storage
    const storageRef = ref(storage, `ics_files/${appointment.businessId}/${fileName}`);
    
    // Upload the file
    const snapshot = await uploadBytes(storageRef, blob, {
      contentType: 'text/calendar',
      customMetadata: {
        appointmentId: appointment.id || '',
        businessId: appointment.businessId,
        clientEmail: appointment.clientEmail,
        serviceName: appointment.serviceName || '',
        createdAt: new Date().toISOString()
      }
    });
    
    // Get the download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    console.log('ICS file uploaded successfully:', fileName);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading ICS file:', error);
    throw error;
  }
}

/**
 * Deletes an ICS file from Firebase Storage
 * @param appointment The appointment data
 * @param businessName The name of the business
 * @returns Promise that resolves when the file is deleted
 */
export async function deleteICSFile(
  appointment: Appointment,
  businessName: string
): Promise<void> {
  try {
    const fileName = generateICSFileName(appointment, businessName);
    const storageRef = ref(storage, `ics_files/${appointment.businessId}/${fileName}`);
    
    await deleteObject(storageRef);
    console.log('ICS file deleted successfully:', fileName);
  } catch (error) {
    console.error('Error deleting ICS file:', error);
    // Don't throw error for deletion failures as it's not critical
  }
}

/**
 * Gets the download URL for an existing ICS file
 * @param appointment The appointment data
 * @param businessName The name of the business
 * @returns Promise that resolves to the download URL or null if not found
 */
export async function getICSFileURL(
  appointment: Appointment,
  businessName: string
): Promise<string | null> {
  try {
    const fileName = generateICSFileName(appointment, businessName);
    const storageRef = ref(storage, `ics_files/${appointment.businessId}/${fileName}`);
    
    const downloadURL = await getDownloadURL(storageRef);
    return downloadURL;
  } catch (error) {
    console.error('Error getting ICS file URL:', error);
    return null;
  }
}

/**
 * Updates an ICS file in Firebase Storage (deletes old and uploads new)
 * @param appointment The appointment data
 * @param businessName The name of the business
 * @param businessAddress Optional business address
 * @returns Promise that resolves to the new download URL
 */
export async function updateICSFile(
  appointment: Appointment,
  businessName: string,
  businessAddress?: string
): Promise<string> {
  try {
    // Delete the old file (if it exists)
    await deleteICSFile(appointment, businessName);
    
    // Upload the new file
    const downloadURL = await uploadICSFile(appointment, businessName, businessAddress);
    
    console.log('ICS file updated successfully');
    return downloadURL;
  } catch (error) {
    console.error('Error updating ICS file:', error);
    throw error;
  }
}

/**
 * Downloads an ICS file content from Firebase Storage
 * @param downloadURL The download URL of the ICS file
 * @returns Promise that resolves to the ICS file content
 */
export async function downloadICSContent(downloadURL: string): Promise<string> {
  try {
    const response = await fetch(downloadURL);
    if (!response.ok) {
      throw new Error(`Failed to download ICS file: ${response.statusText}`);
    }
    
    const content = await response.text();
    return content;
  } catch (error) {
    console.error('Error downloading ICS content:', error);
    throw error;
  }
}

/**
 * Creates a temporary download link for an ICS file
 * @param appointment The appointment data
 * @param businessName The name of the business
 * @param businessAddress Optional business address
 * @returns A temporary blob URL for download
 */
export function createTemporaryICSDownload(
  appointment: Appointment,
  businessName: string,
  businessAddress?: string
): string {
  const icsContent = generateICSContent(appointment, businessName, businessAddress);
  const blob = createICSBlob(icsContent);
  return URL.createObjectURL(blob);
}
